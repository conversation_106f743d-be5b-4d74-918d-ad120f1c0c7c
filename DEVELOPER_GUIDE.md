# Prospr Light - Developer Guide

This guide provides comprehensive information for developers working on the Prospr Light application, a Qt5/QML-based industrial printer management system.

## Development Environment Setup

### Supported Platforms

- **Primary Development**: Ubuntu 22.04.x LTS
- **Secondary Development**: macOS (with Homebrew)
- **Target Deployment**: Allwinner T507 aarch64 embedded Linux

> **Note**: Windows development is supported via WSL (Windows Subsystem for Linux)

### Initial Setup

1. **Clone the Repository**
   ```bash
   git clone https://github.com/paaxware/light.git
   cd light
   ```

2. **Initialize Submodules**
   ```bash
   git submodule update --init --recursive
   ```

3. **Setup Development Environment**
   ```bash
   ./scripts/setup_dev_env.sh
   ```

### Building the Application

#### Quick Build (Debug)
```bash
./scripts/build.sh -t x86_64 -b debug -c
```

#### Build Options
- **Target**: `-t x86_64` (local) or `-t aarch64` (cross-compile)
- **Build Type**: `-b debug` or `-b release`
- **Clean**: `-c` (clean before build) or `--no-clean`
- **Skip Components**: `--skip-cutekeyboard`, `--skip-qrc`

#### Examples
```bash
# Release build for local development
./scripts/build.sh -t x86_64 -b release -c

# Cross-compile for embedded target
./scripts/build.sh -t aarch64 -b release -c

# Quick rebuild without cleaning
./scripts/build.sh --no-clean
```

### Running the Application

```bash
./scripts/run.sh
```

## Project Architecture

### Directory Structure

```
light/
├── main.qml                  # Application entry point
├── main.cpp                  # C++ entry point
├── src/                      # C++ backend components
│   ├── models/              # Data models (PermissionsModel, ErrorModel, etc.)
│   ├── printer/             # Printer management (PrinterManager, ServiceFunction)
│   ├── printfile/           # Print file management
│   ├── ConfigManager.*      # Application configuration
│   ├── UserManager.*        # User authentication and permissions
│   ├── ErrorManager.*       # Error logging and management
│   └── TimeHandler.*        # Date/time utilities
├── UI/                      # QML user interface
│   ├── Core/               # Core application logic
│   ├── Components/         # Reusable UI components
│   ├── Screens/            # Application screens
│   ├── Theme/              # Design system and theming
│   └── Assets/             # Images, icons, and resources
├── tests/                   # Test suite
│   ├── qmltests/           # QML/JavaScript tests
│   ├── uitests/            # UI component tests
│   └── pathresolver/       # Path resolution tests
├── scripts/                # Build and deployment scripts
├── 3rdparty/               # External dependencies
│   └── cutekeyboard/       # Virtual keyboard component
├── fonts/                  # Font files for embedded deployment
└── build/                  # Build output directory
```

## Core Components

### UI/Core Components

#### AppController.qml
**Purpose:** Central application controller managing app state, navigation, and initialization.

**Key Features:**
- Screen navigation with `navigateTo(screenName)`
- Application state management
- Initialization and cleanup
- Signal-based communication with UI components

**Usage:**
```qml
import UI.Core 1.0

AppController {
    id: appController
    onScreenChanged: console.log("Navigated to:", currentScreen)
}
```

#### ContentLoader.qml
**Purpose:** Dynamic QML screen loader with error handling and fallback support.

**Key Features:**
- Asynchronous screen loading
- Error handling with fallback screens
- Loading state management
- Path resolution integration

**Usage:**
```qml
ContentLoader {
    id: contentLoader
    appController: appController
    onLoadingCompleted: console.log("Screen loaded:", screenName)
    onLoadingFailed: console.error("Load failed:", errorMessage)
}
```

#### PathResolver.qml
**Purpose:** Cross-platform path resolution for desktop and embedded environments.

**Key Features:**
- Desktop (`qrc:/`) vs embedded (`file:///`) path handling
- Screen and asset path resolution
- Platform detection and adaptation

**Usage:**
```qml
import UI.Core 1.0

Loader {
    source: PathResolver.resolveScreenPath("Home")
}
```

#### DisplayConfig.qml
**Purpose:** Display configuration and scaling management.

**Key Features:**
- Resolution and scaling calculations
- Platform-specific display settings
- Development vs production configurations

#### PlatformHelpers.qml
**Purpose:** Platform-specific utilities and helpers.

**Key Features:**
- Platform detection (macOS, Linux, embedded)
- Environment-specific configurations
- System integration utilities

### C++ Backend Components

#### Backend Singletons
The application uses several C++ singletons registered for QML access:

- **ConfigManager** (`Backend.ConfigManager 1.0`): Application configuration management
- **UserManager** (`Backend.UserManager 1.0`): User authentication and permissions
- **PrintfileManager** (`Backend.PrintfileManager 1.0`): Print file management
- **PrinterManager** (`Backend.PrinterManager 1.0`): Printer and service management
- **ErrorManager** (`Backend.Error 1.0`): Error logging and management

#### Key Backend Classes

**UserManager**
- User authentication and session management
- Permission system with role-based access control
- User creation, modification, and validation
- Integration with JSON-based user storage

**ConfigManager**
- Application settings and configuration
- Window dimensions and display settings
- Virtual keyboard configuration
- Theme and UI preferences

**PrinterManager**
- Service function management (start/stop operations)
- Status monitoring and indicators
- Print job management
- Hardware integration interface

**ErrorManager**
- Centralized error logging
- Error categorization and timestamps
- Persistent error storage
- Real-time error notifications

## Theme System

The `UI/Theme` directory provides a comprehensive design system with centralized theming:

```qml
import "qrc:/UI/Theme" as Theme
```

### Theme Components

#### Colors.qml
Centralized color palette with brand, semantic, and neutral colors.
```qml
Rectangle {
    color: Theme.Colors.primary    // Prospr brand orange
    border.color: Theme.Colors.success  // Semantic colors
}
```

#### Typography.qml
Font system with industrial-optimized readability.
```qml
Text {
    font.family: Theme.Typography.primaryFontFamily
    font.pixelSize: Theme.Typography.h1
    font.weight: Theme.Typography.weightBold
}
```

#### Spacing.qml
Consistent spacing system with touch-optimized dimensions.
```qml
Column {
    spacing: Theme.Spacing.medium  // 24px
    anchors.margins: Theme.Spacing.touchableMinHeight  // 44px
}
```

#### Radius.qml
Standard border radius values for UI consistency.
```qml
Rectangle {
    radius: Theme.Radius.button  // 8px
}
```

#### Shadows.qml
Performance-optimized elevation system using borders.
```qml
Component.onCompleted: Theme.Shadows.applyElevation(myRect, 1)
```

## Testing

### Test Structure
```
tests/
├── qmltests/          # Pure QML/JavaScript tests
├── uitests/           # UI component tests with rendering
├── pathresolver/      # Path resolution tests
└── printer/           # Backend component tests
```

### Running Tests

#### All Tests
```bash
./tests/run_tests.sh
```

#### Individual Test Suites
```bash
# QML tests
cd tests/qmltests && qmake && make && ./tst_qml

# UI tests
cd tests/uitests && qmake && make && ./tst_ui

# Path resolver tests
cd tests/pathresolver && qmake && make && ./tst_pathresolver
```

### Test Coverage
- **Content Scaling**: UI scaling behavior across platforms
- **Platform Detection**: macOS and Linux detection logic
- **Window Sizing**: Dimension calculations and positioning
- **Component Rendering**: UI component appearance and behavior
- **Path Resolution**: Resource path handling across environments

## Development Workflow

### Code Quality Tools

#### QML Formatting
```bash
./scripts/format_qml.sh
```

#### QML Linting
```bash
./scripts/lint_qml.sh
```

### Font Management
For embedded targets, fonts are automatically prepared:
```bash
./scripts/prepare_fonts.sh
```

### Configuration Management
Set application configuration:
```bash
./scripts/set_config.sh
```

## Cross-Platform Development

### Desktop Development
- Uses `qrc:/` resource paths
- Standard Qt5 installation
- Native window management
- Development-optimized scaling

### Embedded Target (T507)
- Uses `file:///` paths for resources
- Cross-compilation via Docker
- Framebuffer-based display
- Production-optimized performance
- Custom font deployment

### Build Targets
- **x86_64**: Local development (Linux/macOS)
- **aarch64**: Cross-compilation for T507 embedded target

## Pull Request Process

1. **Branch Creation**: `git checkout -b <ticket-number>-<meaningful-name>`
2. **Development**: Make changes following coding standards
3. **Testing**: Run relevant test suites
4. **Code Quality**: Format and lint code
5. **Commit**: Use descriptive commit messages
6. **Push**: `git push origin <branch-name>`
7. **Pull Request**: Create PR against `main` branch
8. **Review**: Wait for code review and approval
9. **Merge**: Squash and merge after approval

## Additional Resources

- **Architecture**: See [ARCHITECTURE.md](ARCHITECTURE.md) for system design
- **QML Best Practices**: See `context/qml_best_practices.md`
- **Qt Documentation**: See `context/qt_qml_doc.md`
- **Hardware Setup**: See `docs/ALLWINNER_T507_CONFIGURATION.md`
- **Error Logging**: See `docs/ERROR_LOG_IMPLEMENTATION.md`
