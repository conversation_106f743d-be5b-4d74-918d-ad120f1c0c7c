#!/bin/bash

# Usage: ./build_and_push.sh [tag] [dockerfile]
# Example: ./build_and_push.sh latest Dockerfile.dev

set -e

TAG="${1:-latest}"
DOCKERFILE="${2:-containers/Dockerfile.dev}"
IMAGE="ghcr.io/paaxware/light:$TAG"

echo "Building Docker image: $IMAGE using Dockerfile: $DOCKERFILE"
docker build -f "$DOCKERFILE" -t "$IMAGE" .

echo "Pushing Docker image: $IMAGE"
docker push "$IMAGE"

echo "Done."