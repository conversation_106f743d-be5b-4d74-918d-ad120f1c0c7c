import Backend.ConfigManager 1.0
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.CuteKeyboard 1.0
import QtQuick.Window 2.15
import "qrc:/UI/Core" as Core

ApplicationWindow {
    id: rootWindow

    property var platform: platformHelpers
    property var display: displayConfig
    property var appController: applicationController

    width: ConfigManager.windowWidth
    height: ConfigManager.windowHeight
    title: "Prospr Light"
    Component.onCompleted: {
        console.debug("Initializing PathResolver singleton...");
        var isEmbedded = Core.PathResolver.isEmbedded;
        console.debug("PathResolver detected platform: " + (isEmbedded ? "Embedded" : "Desktop"));
        console.debug("PathResolver using prefix: " + Core.PathResolver.currentPrefix);
        console.debug("Application starting");
        platform.logPlatformInfo();
        display.logDisplayConfig();
        visibility = display.visibilityMode;
        // Set initial screen from config
        appController.initialize();
        console.debug("Main layout size: " + width + "x" + height);
        console.debug("Main layout scale: " + display.scaleFactor);
        console.debug("=== Deployment Debug Info ===");
        var qmlImportPath = "not set";
        try {
            if (Qt.application.environment && typeof Qt.application.environment === 'function')
                qmlImportPath = Qt.application.environment("QML_IMPORT_PATH") || "not set";

        } catch (e) {
        }
        console.debug("QML_IMPORT_PATH: " + qmlImportPath);
        console.debug("Current directory: " + Qt.application.arguments[0]);
        console.debug("Qt.resolvedUrl(./): " + Qt.resolvedUrl("./"));
        var componentPaths = ["UI/Core/PlatformHelpers.qml", "UI/Core/DisplayConfig.qml", "UI/Core/AppController.qml", "UI/Core/ContentLoader.qml", "UI/Core/FallbackScreen.qml", "UI/MainLayout.qml"];
    }

    // Path resolver for development environment
    QtObject {
        id: pathResolver

        function resolve(path) {
            // Use QRC paths for host machine
            return path;
        }

    }

    Core.PlatformHelpers {
        id: platformHelpers
    }

    Core.DisplayConfig {
        id: displayConfig

        platform: platformHelpers
    }

    Core.AppController {
        id: applicationController
    }

    Core.ContentLoader {
        id: contentLoader

        anchors.fill: parent
        appController: applicationController
    }

    Loader {
        id: mainLayoutLoader

        anchors.fill: parent
        source: Core.PathResolver.resolvePath("qrc:/UI/MainLayout.qml")
        asynchronous: false
        onLoaded: {
            if (item) {
                if (item.hasOwnProperty("isEmbeddedMode"))
                    item.isEmbeddedMode = platform.isEmbedded || platform.isAarch64Target;

                if (item.hasOwnProperty("scaleFactor"))
                    item.scaleFactor = display.scaleFactor;

                if (item.navigateToRequested)
                    item.navigateToRequested.connect(function(to, from) {
                    NavigationManager.go(to, from);
                });
                else
                    console.warn("MainLayout.navigateToRequested signal not found for connection.");
                console.debug("MainLayout configured successfully");
            }
        }
        onStatusChanged: {
            if (status === Loader.Error) {
                console.error("Failed to load MainLayout");
                console.error(Loader.Error);
                errorPlaceholder.visible = true;
            }
        }
    }

    Rectangle {
        id: errorPlaceholder

        anchors.fill: parent
        color: "#333333"
        visible: false

        Text {
            anchors.centerIn: parent
            color: "white"
            // TODO: Internationalize this string using Qt's translation system
            text: "Error loading main layout"
            font.pixelSize: 24
        }

    }

    // InputPanel only visible on embedded/touch platforms
    InputPanel {
        id: inputPanel

        z: 99
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        visible: platform.isEmbedded && Qt.inputMethod.visible
    }

}
