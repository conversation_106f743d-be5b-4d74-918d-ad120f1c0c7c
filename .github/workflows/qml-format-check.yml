name: QML Format Check

on:
  push:
  pull_request:

jobs:
  qml-format:
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/paaxware/light:latest
      credentials:
        username: ${{ github.actor }}
        password: ${{ secrets.GHCR_PAT }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Make formatter script executable
        run: chmod +x scripts/format_qml.sh

      - name: Run QML formatter
        run: ./scripts/format_qml.sh

      - name: Check for unformatted QML files
        run: |
          if git rev-parse --is-inside-work-tree >/dev/null 2>&1; then
            if ! git diff --exit-code -- UI/**/*.qml main.qml; then
              echo "QML formatting issues detected! Run scripts/format_qml.sh locally and commit the changes."
              exit 1
            fi
          else
            echo "Not inside a git repository. Skipping QML formatting check."
          fi
