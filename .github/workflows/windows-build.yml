name: Windows Qt Build

on:
  push:

jobs:
  build:
    runs-on: windows-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Qt
        uses: jurplel/install-qt-action@v3
        with:
          version: '5.15.2'
          host: 'windows'
          target: 'desktop'
          arch: 'win64_msvc2019_64'

      - name: Set up MSVC
        uses: ilammy/msvc-dev-cmd@v1
        with:
          arch: x64

      - name: Configure project with qmake
        run: qmake light_qt5.pro

      - name: Build project
        run: nmake

      - name: Deploy with windeployqt
        run: |
          mkdir deploy
          Copy-Item release\light_qt5.exe deploy\light_qt5.exe -Force
          Copy-Item -Recurse -Force release\storage deploy\storage
          windeployqt --qmldir . deploy\light_qt5.exe

      - name: Archive build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: light-windows-build
          path: deploy
          if-no-files-found: error
