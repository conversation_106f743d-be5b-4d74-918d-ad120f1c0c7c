QT += quick quickcontrols2 gui

# Virtual keyboard support - conditional based on platform
linux {
    # Check if the module exists before adding it
    system(pkg-config --exists Qt5VirtualKeyboard): QT += virtualkeyboard
}


# On Linux, ensure we use the right paths for QML modules
linux {
    QML2_IMPORT_PATH += /usr/lib/x86_64-linux-gnu/qt5/qml
    QML2_IMPORT_PATH += $$PWD/3rdparty/cutekeyboard/qml
    QML_DESIGNER_IMPORT_PATH += /usr/lib/x86_64-linux-gnu/qt5/qml
}

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

# Get Qt version to handle compatibility between development and target
system("qmake -query QT_VERSION"): QT_CURRENT_VERSION = $$system("qmake -query QT_VERSION")
else: QT_CURRENT_VERSION = 0

# Define target Qt version (5.15.4)
QT_TARGET_VERSION = 5.15.4
!equals(QT_CURRENT_VERSION, $$QT_TARGET_VERSION) {
    message("Development using Qt version $$QT_CURRENT_VERSION but targeting $$QT_TARGET_VERSION")
    DEFINES += QT_VERSION_DIFFERENCE
    DEFINES += COMPATIBILITY_5_15_16
    CONFIG += c++17
}

# C++ include path
INCLUDEPATH += src/

# Use C++11 by default unless C++17 is set above
!contains(CONFIG, c++17) {
    CONFIG += c++11
}

# --- Copy config/ui.ini into .app bundle for macOS ---
macx {
    CONFIG += no_check_exist
    QMAKE_POST_LINK += mkdir -p $$OUT_PWD/../Resources/config; cp $$PWD/config/ui.ini $$OUT_PWD/../Resources/config/ui.ini;
    # Use QMAKE_BUNDLE_DATA to copy JSON files into the app bundle
    permissionsjson.files = $$PWD/storage/permissions.json
    permissionsjson.path = Contents/MacOS/storage
    usersjson.files = $$PWD/storage/users.json
    usersjson.path = Contents/MacOS/storage
    errorlogjson.files = $$PWD/storage/errorlog.json
    errorlogjson.path = Contents/MacOS/storage
    QMAKE_BUNDLE_DATA += permissionsjson usersjson errorlogjson
    printfiles.file1 = $$PWD/storage/printfiles/compact_label_printfile.json
    printfiles.file2 = $$PWD/storage/printfiles/product_label_printfile.json
    printfiles.file3 = $$PWD/storage/printfiles/simple_text_printfile.json
    printfiles.file4 = $$PWD/storage/printfiles/compact_label_printfile.json
    printfiles.path = Contents/MacOS/storage/printfiles
    QMAKE_BUNDLE_DATA += permissionsjson usersjson printfiles
}

win32 {
    DEFINES += DESKTOP_BUILD

    # --- Copy storage folder into build directory for runtime access ---
    copy_storage.commands = \
        xcopy /E /I /Y /Q /H /C /R /K /D \"$$PWD/storage\" \"$$OUT_PWD/debug/storage\" & \
        xcopy /E /I /Y /Q /H /C /R /K /D \"$$PWD/storage\" \"$$OUT_PWD/release/storage\"

    copy_storage.depends = FORCE
    QMAKE_EXTRA_TARGETS += copy_storage
    PRE_TARGETDEPS += copy_storage
}

# Add Qt compatibility code if needed
exists($$PWD/qt_compatibility.h) {
    HEADERS += $$PWD/qt_compatibility.h
}

# TODO - consolidate SOURCES/LIBS/HEADERS
# Project sources and headers
SOURCES += \
    main.cpp \
    src/models/PermissionsModel.cpp \
    src/models/PrintfileItemModel.cpp \
    src/models/PrintfileModel.cpp \
    src/models/ServiceFunctionModel.cpp \
    src/models/ServiceStatusModel.cpp \
    src/printer/PrinterManager.cpp \
    src/printer/ServiceFunction.cpp \
    src/printer/ServiceStatus.cpp \
    src/Permission.cpp \
    src/TimeHandler.cpp \
    src/ConfigManager.cpp \
    src/YamlConfigLoader.cpp \
    src/BackendSingletons.cpp \
    src/QmlRegistration.cpp \
    src/UserInfo.cpp \
    src/UserManager.cpp \
    src/printfile/Printfile.cpp \
    src/printfile/PrintfileItem.cpp \
    src/printfile/PrintfileManager.cpp \
    src/models/ErrorModel.cpp \
    src/ErrorManager.cpp

HEADERS += \
    src/Constants.h \
    src/models/PermissionsModel.h \
    src/models/PrintfileItemModel.h \
    src/models/PrintfileModel.h \
    src/models/ServiceFunctionModel.h \
    src/models/ServiceStatusModel.h \
    src/printer/PrinterManager.h \
    src/printer/ServiceFunction.h \
    src/printer/ServiceStatus.h \
    src/Permission.h \
    src/TimeHandler.h \
    src/ConfigManager.h \
    src/YamlConfigLoader.h \
    src/BackendSingletons.h \
    src/QmlRegistration.h \
    src/UserInfo.h \
    src/UserManager.h \
    src/printfile/Printfile.h \
    src/printfile/PrintfileItem.h \
    src/printfile/PrintfileManager.h \
    src/models/ErrorModel.h \
    src/ErrorManager.h

# Test sources
SOURCES += \
    tests/printer/PrinterManagerTest.cpp

HEADERS += \
    tests/printer/PrinterManagerTest.h

INCLUDEPATH += \
    src

# QML resources
RESOURCES += qml.qrc

# Translation setup
TRANSLATIONS += light_qt5_en_US.ts
linux {
    QMAKE_LRELEASE = /usr/bin/lrelease-qt5
    exists($$QMAKE_LRELEASE) {
        message("Using lrelease at $$QMAKE_LRELEASE")
        CONFIG += lrelease embed_translations
    }

    LIBS += -lyaml-cpp
}

macx {
    INCLUDEPATH += /usr/local/opt/yaml-cpp/include
    LIBS += -L/usr/local/opt/yaml-cpp/lib -lyaml-cpp
}

# END OF FILE
