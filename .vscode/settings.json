{"qml.importPaths": ["/usr/local/Cellar/qt@5/5.15.16_2/qml", "${workspaceFolder}/UI", "${workspaceFolder}/UI/Components", "${workspaceFolder}/UI/Theme", "${workspaceFolder}/UI/Core", "${workspaceFolder}/3rdparty/cutekeyboard/qml"], "qml.qmlls.arguments": ["--import-path", "/usr/local/Cellar/qt@5/5.15.16_2/qml", "--import-path", "${workspaceFolder}/UI", "--import-path", "${workspaceFolder}/UI/Components", "--import-path", "${workspaceFolder}/UI/Theme", "--import-path", "${workspaceFolder}/UI/Core"], "qml.lint.arguments": ["-I", "/usr/local/Cellar/qt@5/5.15.16_2/qml", "-I", "${workspaceFolder}/UI", "-I", "${workspaceFolder}/UI/Components", "-I", "${workspaceFolder}/UI/Theme", "-I", "${workspaceFolder}/UI/Core"]}