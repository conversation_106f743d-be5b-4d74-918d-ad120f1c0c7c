#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QFile>
#include <QDir>
#include <QCoreApplication>
#include <QUuid>
#include <QQmlEngine>

#include "Constants.h"
#include "PrintfileManager.h"

PrintfileManager::PrintfileManager(ConfigManager* configManager, QObject *parent)
    : QObject(parent)
    , m_printfileModel(new PrintfileModel(this))
    , m_currentPrintfileItemModel(new PrintfileItemModel(this))
    , m_currentPrintfile(nullptr)
    , m_editPrintfileItemModel(new PrintfileItemModel(this))
    , m_editMode(false)
    , m_unsavedChanges(false)
    , m_configManager(configManager)
{
    // Initialize selectedPrintfileId from config if available
    if (m_configManager) {
        m_selectedPrintfileId = m_configManager->selectedPrintfileId();
    }

    // Load existing printfiles
    loadAllPrintfiles();
}

PrintfileManager::~PrintfileManager()
{
    discardEditPrintfile();
}

QString PrintfileManager::currentPrintfileId() const
{
    return m_currentPrintfile ? m_currentPrintfile->printfileId() : QString();
}

void PrintfileManager::setCurrentPrintfileId(const QString &printfileId)
{
    Printfile *newPrintfile = getPrintfile(printfileId);
    if (m_currentPrintfile != newPrintfile) {
        if (m_currentPrintfile) {
            disconnect(m_currentPrintfile, &Printfile::itemAdded, this, &PrintfileManager::onPrintfileItemAdded);
            disconnect(m_currentPrintfile, &Printfile::itemRemoved, this, &PrintfileManager::onPrintfileItemRemoved);
            disconnect(m_currentPrintfile, &Printfile::cleared, m_currentPrintfileItemModel, &PrintfileItemModel::clear);
        }

        m_currentPrintfile = newPrintfile;

        if (m_currentPrintfile) {
            connect(m_currentPrintfile, &Printfile::itemAdded, this, &PrintfileManager::onPrintfileItemAdded);
            connect(m_currentPrintfile, &Printfile::itemRemoved, this, &PrintfileManager::onPrintfileItemRemoved);
            connect(m_currentPrintfile, &Printfile::cleared, m_currentPrintfileItemModel, &PrintfileItemModel::clear);
        }

        updateCurrentPrintfileItemModel();
        emit currentPrintfileChanged();
    }
}

QString PrintfileManager::selectedPrintfileId() const
{
    return m_selectedPrintfileId;
}

void PrintfileManager::setSelectedPrintfileId(const QString &printfileId)
{
    if (m_selectedPrintfileId != printfileId) {
        m_selectedPrintfileId = printfileId;

        // Save to config manager if available
        if (m_configManager) {
            m_configManager->setSelectedPrintfileId(printfileId);
        }

        emit selectedPrintfileIdChanged(printfileId);
    }
}

QWeakPointer<Printfile> PrintfileManager::createPrintfile(const QString &printfileId, const QString &name)
{
    QString actualId = printfileId.isEmpty() ? generateUniquePrintfileId() : printfileId;

    if (m_printfiles.contains(actualId)) {
        qWarning() << "Printfile with ID" << actualId << "already exists";
        return {};
    }

    QString actualName = name.isEmpty() ? actualId : name;
    QSharedPointer<Printfile> printfilePtr = QSharedPointer<Printfile>::create(actualId, actualName);
    QQmlEngine::setObjectOwnership(printfilePtr.data(), QQmlEngine::CppOwnership);
    registerPrintfile(printfilePtr);

    emit printifleCreated(actualId);
    return printfilePtr.toWeakRef();
}

bool PrintfileManager::deletePrintfile(const QString &printfileId)
{
    auto it = m_printfiles.find(printfileId);
    if (it == m_printfiles.end()) {
        return false;
    }

    auto printfileRef = it.value();

    // If this is the current printfile, clear it
    if (m_currentPrintfile == printfileRef.data()) {
        setCurrentPrintfileId(QString());
    }

    unregisterPrintfile(printfileRef);
    m_printfiles.erase(it);

    emit printfileDeleted(printfileId);
    return true;
}

Printfile* PrintfileManager::getPrintfile(const QString &printfileId) const
{
    return m_printfiles.value(printfileId).data();
}

QStringList PrintfileManager::getPrintfileIds() const
{
    return m_printfiles.keys();
}

bool PrintfileManager::hasPrintfile(const QString &printfileId) const
{
    return m_printfiles.contains(printfileId);
}

void PrintfileManager::setUnsavedChanges(bool unsaved)
{
    if (m_unsavedChanges != unsaved) {
        m_unsavedChanges = unsaved;
        emit unsavedChangesChanged(m_unsavedChanges);
    }
}

PrintfileItem* PrintfileManager::createEditPrintfileItem(const QString &itemId, const QString &name, const QString &qmlPath)
{
    if (!m_editPrintfile) {
        qWarning() << "No edit printfile set";
        return {};
    }

    QString actualId = itemId.isEmpty() ? generateUniqueItemId(m_editPrintfile->printfileId()) : itemId;

    if (m_editPrintfile->hasItem(actualId)) {
        qWarning() << "Item with ID" << actualId << "already exists in edit printfile";
        return {};
    }

    PrintfileItem* item = m_editPrintfile->createItem(actualId, name, qmlPath);
    if (item) {
        emit printfileItemCreated(m_editPrintfile->printfileId(), actualId);
    }
    return item;
}

bool PrintfileManager::deleteEditPrintfileItem(const QString &itemId)
{
    if (!m_editPrintfile) {
        return false;
    }

    if (!m_editPrintfile->hasItem(itemId)) {
        return false;
    }

    m_editPrintfile->removeItem(itemId);
    emit printfileItemDeleted(m_editPrintfile->printfileId(), itemId);
    return true;
}

PrintfileItem* PrintfileManager::getEditPrintfileItem(const QString &itemId) const
{
    if (!m_editPrintfile) {
        return nullptr;
    }

    return m_editPrintfile->getItem(itemId);
}

PrintfileItem* PrintfileManager::duplicateEditPrintfileItem(const QString &sourceItemId, const QString &newItemId)
{
    if (!m_editPrintfile) {
        qWarning() << "No edit printfile set";
        return {};
    }

    PrintfileItem* sourceItem = m_editPrintfile->getItem(sourceItemId);
    if (!sourceItem) {
        qWarning() << "Source item not found:" << sourceItemId;
        return {};
    }

    QString actualId = newItemId.isEmpty() ? generateUniqueItemId(m_editPrintfile->printfileId()) : newItemId;

    if (m_editPrintfile->hasItem(actualId)) {
        qWarning() << "Item with ID" << actualId << "already exists in current printfile";
        return {};
    }

    // Create new item and copy data
    auto newItem = QSharedPointer<PrintfileItem>::create(actualId);
    QQmlEngine::setObjectOwnership(newItem.data(), QQmlEngine::CppOwnership);

    QVariantMap sourceData = sourceItem->toVariantMap();
    sourceData["itemId"] = actualId;
    sourceData["name"] = sourceItem->name() + " (Copy)";
    // Offset position slightly
    sourceData["x"] = sourceItem->x() + 10;
    sourceData["y"] = sourceItem->y() + 10;
    newItem->fromVariantMap(sourceData);

    if (m_editPrintfile->addItem(newItem)) {
        emit printfileItemCreated(m_editPrintfile->printfileId(), actualId);
        return newItem.data();
    }

    return {};
}

void PrintfileManager::movePrintfileItem(const QString &itemId, qreal x, qreal y)
{
    PrintfileItem* item = getEditPrintfileItem(itemId);

    if (item) {
        item->setPosition(x, y);
    }
}

void PrintfileManager::resizePrintfileItem(const QString &itemId, qreal width, qreal height)
{
    PrintfileItem* item = getEditPrintfileItem(itemId);

    if (item) {
        item->setSize(width, height);
    }
}

void PrintfileManager::transformPrintfileItem(const QString &itemId, qreal x, qreal y, qreal width, qreal height, qreal rotation, qreal scale)
{
    PrintfileItem* item = getEditPrintfileItem(itemId);

    if (item) {
        item->setTransform(x, y, width, height, rotation, scale);
    }
}

void PrintfileManager::setPrintfileItemProperty(const QString &itemId, const QString &key, const QVariant &value)
{
    PrintfileItem* item = getEditPrintfileItem(itemId);

    if (item) {
        item->setProperty(key, value);
    }
}

bool PrintfileManager::savePrintfile(const QString &printfileId)
{
    Printfile *printfileObj = getPrintfile(printfileId);
    if (!printfileObj) {
        return false;
    }

    // Create printfiles directory if it doesn't exist
    QString printfilesDir = QDir(QCoreApplication::applicationDirPath()).filePath(Constants::PRINTFILES_DIR);
    QDir dir;
    if (!dir.exists(printfilesDir)) {
        dir.mkpath(printfilesDir);
    }

    QString filePath = printfilesDir + "/" + printfileId + ".json";
    return savePrintfileToFile(printfileId, filePath);
}

bool PrintfileManager::loadPrintfile(const QString &printfileId)
{
    QString printfilesDir = QDir(QCoreApplication::applicationDirPath()).filePath(Constants::PRINTFILES_DIR);
    QString filePath = printfilesDir + "/" + printfileId + ".json";

    return loadPrintfileFromFile(filePath, printfileId);
}

bool PrintfileManager::saveAllPrintfiles()
{
    bool allSuccess = true;
    for (auto it = m_printfiles.begin(); it != m_printfiles.end(); ++it) {
        if (!savePrintfile(it.key())) {
            allSuccess = false;
        }
    }

    if (allSuccess) {
        emit printfilesSaved();
    }

    return allSuccess;
}

bool PrintfileManager::loadAllPrintfiles()
{
    QString printfilesDir = QDir(QCoreApplication::applicationDirPath()).filePath(Constants::PRINTFILES_DIR);
    QDir dir(printfilesDir);

    if (!dir.exists()) {
        return true; // No printfiles directory is not an error
    }

    QStringList filters;
    filters << "*.json";
    const QFileInfoList files = dir.entryInfoList(filters, QDir::Files);

    bool allSuccess = true;
    for (const QFileInfo &fileInfo : files) {
        QString printfileId = fileInfo.baseName();
        if (!loadPrintfileFromFile(fileInfo.absoluteFilePath(), printfileId)) {
            allSuccess = false;
        }
    }

    if (allSuccess) {
        emit printfilesLoaded();
    }

    return allSuccess;
}

QVariantMap PrintfileManager::exportPrintfile(const QString &printfileId) const
{
    Printfile *printfileObj = getPrintfile(printfileId);
    if (!printfileObj) {
        return QVariantMap();
    }

    return printfileObj->toVariantMap();
}

bool PrintfileManager::importPrintfile(const QVariantMap &data, const QString &newPrintfileId)
{
    QString printfileId = newPrintfileId.isEmpty() ? data.value("printfileId").toString() : newPrintfileId;

    if (printfileId.isEmpty()) {
        printfileId = generateUniquePrintfileId();
    } else if (m_printfiles.contains(printfileId)) {
        printfileId = generateUniquePrintfileId(printfileId);
    }

    QSharedPointer<Printfile> printfilePtr = QSharedPointer<Printfile>::create(printfileId);
    QQmlEngine::setObjectOwnership(printfilePtr.data(), QQmlEngine::CppOwnership);
    QVariantMap modifiedData = data;
    modifiedData["printfileId"] = printfileId;
    printfilePtr->fromVariantMap(modifiedData);

    registerPrintfile(printfilePtr);
    emit printifleCreated(printfileId);
    return true;
}

bool PrintfileManager::savePrintfileToFile(const QString &printfileId, const QString &filePath)
{
    Printfile *printfileObj = getPrintfile(printfileId);
    if (!printfileObj) {
        return false;
    }

    QVariantMap data = printfileObj->toVariantMap();
    QJsonDocument doc = QJsonDocument::fromVariant(data);

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "Cannot open file for writing:" << filePath;
        return false;
    }

    file.write(doc.toJson());
    return true;
}

bool PrintfileManager::loadPrintfileFromFile(const QString &filePath, const QString &newPrintfileId)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Cannot open file for reading:" << filePath;
        return false;
    }

    QByteArray data = file.readAll();
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);

    if (error.error != QJsonParseError::NoError) {
        qWarning() << "JSON parse error:" << error.errorString();
        return false;
    }

    return importPrintfile(doc.toVariant().toMap(), newPrintfileId);
}

void PrintfileManager::onPrintfileChanged()
{
}

void PrintfileManager::onPrintfileItemAdded(QWeakPointer<PrintfileItem> item)
{
    if (m_currentPrintfile && item) {
        m_currentPrintfileItemModel->addItem(item);
    }
}

void PrintfileManager::onPrintfileItemRemoved(const QString &itemId)
{
    m_currentPrintfileItemModel->removeItem(itemId);
}

QSharedPointer<Printfile> PrintfileManager::duplicatePrintfile(const QString &sourcePrintfileId, const QString &newPrintfileId, const QString &newName)
{
    Printfile *sourcePrintfile = getPrintfile(sourcePrintfileId);
    if (!sourcePrintfile) {
        qWarning() << "Source printfile not found:" << sourcePrintfileId;
        return {};
    }

    QString actualId = newPrintfileId.isEmpty() ? generateUniquePrintfileId(sourcePrintfile->printfileId() + "_copy") : newPrintfileId;

    if (m_printfiles.contains(actualId)) {
        qWarning() << "Printfile with ID" << actualId << "already exists";
        return {};
    }

    QString actualName = newName.isEmpty() ? (sourcePrintfile->name() + " (Copy)") : newName;

    auto newPrintfile = QSharedPointer<Printfile>::create(actualId, actualName);
    QQmlEngine::setObjectOwnership(newPrintfile.data(), QQmlEngine::CppOwnership);

    QVariantMap sourceData = sourcePrintfile->toVariantMap();
    sourceData["printfileId"] = actualId;
    sourceData["name"] = actualName;
    newPrintfile->fromVariantMap(sourceData);

    return newPrintfile;
}

void PrintfileManager::registerPrintfile(const QSharedPointer<Printfile> &printfileObj)
{
    if (!printfileObj) {
        return;
    }

    m_printfiles[printfileObj->printfileId()] = printfileObj;
    m_printfileModel->addPrintfile(printfileObj);

    connect(printfileObj.data(), &Printfile::printfileChanged, this, &PrintfileManager::onPrintfileChanged);

    emit printfileCountChanged();
}

void PrintfileManager::unregisterPrintfile(const QSharedPointer<Printfile>& printfilePtr)
{
    if (!printfilePtr) {
        return;
    }

    disconnect(printfilePtr.data(), &Printfile::printfileChanged, this, &PrintfileManager::onPrintfileChanged);
    m_printfileModel->removePrintfile(printfilePtr->printfileId());

    emit printfileCountChanged();
}

void PrintfileManager::updateCurrentPrintfileItemModel()
{
    if (m_currentPrintfile) {
        m_currentPrintfileItemModel->setItems(m_currentPrintfile->getItems());
    } else {
        m_currentPrintfileItemModel->clear();
    }
}

QString PrintfileManager::generateUniquePrintfileId(const QString &baseName) const
{
    QString base = baseName.isEmpty() ? "printfile" : baseName;
    QString candidateId = base;
    int counter = 1;

    while (m_printfiles.contains(candidateId)) {
        candidateId = QString("%1_%2").arg(base).arg(counter++);
    }

    return candidateId;
}

QString PrintfileManager::generateUniqueItemId(const QString printfileId, const QString &baseName) const
{
    Printfile* printfileObj = printfileId.isEmpty() ? m_currentPrintfile : getPrintfile(printfileId);

    if (!printfileObj) {
        return QUuid::createUuid().toString(QUuid::WithoutBraces);
    }

    QString base = baseName.isEmpty() ? "item" : baseName;
    QString candidateId = base;
    int counter = 1;

    while (printfileObj->hasItem(candidateId)) {
        candidateId = QString("%1_%2").arg(base).arg(counter++);
    }

    return candidateId;
}

Printfile* PrintfileManager::editPrintfile() const
{
    return m_editPrintfile.data();
}

bool PrintfileManager::editMode() const
{
    return m_editMode;
}

void PrintfileManager::setEditMode(bool newEditMode)
{
    if (m_editMode == newEditMode)
        return;
    m_editMode = newEditMode;
    emit editModeChanged();
}

void PrintfileManager::startEditPrintfile(const QString &printfileId)
{
    if (m_editMode) {
        qWarning() << "Discarding unsaved edit printfile:" << m_editPrintfile->printfileId();
        discardEditPrintfile();
    }

    setEditMode(true);

    auto it = m_printfiles.find(printfileId);
    if (it == m_printfiles.end()) {
        qWarning() << "There is no such printfile to edit:" << printfileId;
        return;
    }

    auto duplicatedPrintfile = duplicatePrintfile(printfileId);
    if (!duplicatedPrintfile) {
        qWarning() << "Failed to duplicate printfile for editing:" << printfileId;
        setEditMode(false);
        return;
    }

    m_editPrintfile = duplicatedPrintfile;

    connect(m_editPrintfile.data(), &Printfile::itemAdded, m_editPrintfileItemModel, &PrintfileItemModel::addItem);
    connect(m_editPrintfile.data(), &Printfile::itemRemoved, m_editPrintfileItemModel, qOverload<const QString&>(&PrintfileItemModel::removeItem));
    connect(m_editPrintfile.data(), &Printfile::cleared, m_editPrintfileItemModel, &PrintfileItemModel::clear);
    connect(m_editPrintfile.data(), &Printfile::printfileChanged, this, [this]() { setUnsavedChanges(true); });

    m_editPrintfileItemModel->setItems(m_editPrintfile->getItems());

    m_editPrintfileOriginalId = printfileId;
    emit editPrintfileChanged(m_editPrintfile.data());
}

void PrintfileManager::startNewPrintfile()
{
    if (m_editMode)
        discardEditPrintfile();

    setEditMode(true);

    m_editPrintfile = QSharedPointer<Printfile>::create(generateUniquePrintfileId(), QStringLiteral("New Printfile"));

    connect(m_editPrintfile.data(), &Printfile::itemAdded, m_editPrintfileItemModel, &PrintfileItemModel::addItem);
    connect(m_editPrintfile.data(), &Printfile::itemRemoved, m_editPrintfileItemModel, qOverload<const QString&>(&PrintfileItemModel::removeItem));
    connect(m_editPrintfile.data(), &Printfile::cleared, m_editPrintfileItemModel, &PrintfileItemModel::clear);
    connect(m_editPrintfile.data(), &Printfile::printfileChanged, this, [this]() { setUnsavedChanges(true); });

    m_editPrintfileItemModel->clear();
    m_editPrintfileOriginalId.clear();
    emit editPrintfileChanged(m_editPrintfile.data());
}

void PrintfileManager::saveEditPrintfile()
{
    if (!m_editMode || !m_editPrintfile) {
        return;
    }

    if (m_editPrintfileOriginalId.isEmpty()) {
        QString newPrintfileId = m_editPrintfile->printfileId();

        // Transfer ownership to the printfile registry
        registerPrintfile(m_editPrintfile);
        m_editPrintfile.reset(); // Clear the edit printfile

        m_editMode = false;

        emit printifleCreated(newPrintfileId);
        emit printfileCountChanged();

        m_editPrintfileOriginalId = newPrintfileId;
        startEditPrintfile(newPrintfileId);
    } else {
        Printfile *originalPrintfile = getPrintfile(m_editPrintfileOriginalId);

        if (!originalPrintfile) {
            qWarning() << "Original printfile id not found:" << m_editPrintfileOriginalId;
            return;
        }

        // Get all items from source printfile
        const QList<QWeakPointer<PrintfileItem>> items = m_editPrintfile->getItems();
        originalPrintfile->clearItems();

        for (auto& printfileItem: items) {
            auto sharedItem = printfileItem.toStrongRef();
            if (sharedItem) {
                originalPrintfile->addItem(sharedItem);
            }
        }
    }

    setUnsavedChanges(false);
}

void PrintfileManager::discardEditPrintfile()
{
    if (m_editPrintfile) {
        m_editPrintfile.reset(); // Clear the shared pointer
        m_editPrintfileItemModel->clear();
    }
    setEditMode(false);
    setUnsavedChanges(false);
    m_editPrintfileOriginalId.clear();
    emit editPrintfileChanged(nullptr);
}
