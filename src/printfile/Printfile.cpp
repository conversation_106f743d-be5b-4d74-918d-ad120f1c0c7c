#include <QDebug>
#include <QQmlEngine>
#include <QSharedPointer>

#include "Printfile.h"

Printfile::Printfile(const QString &printfileId, QObject *parent)
    : QObject(parent)
    , m_printfileId(printfileId)
    , m_name(printfileId)
{
}

Printfile::Printfile(const QString &printfileId, const QString &name, QObject *parent)
    : QObject(parent)
    , m_printfileId(printfileId)
    , m_name(name)
{
}

void Printfile::setName(const QString &name)
{
    if (m_name != name) {
        m_name = name;
        emit nameChanged(name);
        emit printfileChanged();
    }
}

void Printfile::setDescription(const QString &description)
{
    if (m_description != description) {
        m_description = description;
        emit descriptionChanged(description);
        emit printfileChanged();
    }
}

void Printfile::setBackgroundColor(const QString &color)
{
    if (m_backgroundColor != color) {
        m_backgroundColor = color;
        emit backgroundColorChanged(color);
        emit printfileChanged();
    }
}

bool Printfile::addItem(QSharedPointer<PrintfileItem> item)
{
    if (!item || m_items.contains(item->itemId())) {
        return false;
    }

    m_items[item->itemId()] = item;

    connect(item.data(), &PrintfileItem::itemChanged, this, &Printfile::onItemChanged);

    emit itemAdded(item.toWeakRef());
    emit itemCountChanged(m_items.count());
    emit printfileChanged();

    return true;
}

void Printfile::removeItem(const QString &itemId)
{
    auto it = m_items.find(itemId);
    if (it != m_items.end()) {
        auto item = it.value();
        m_items.erase(it);

        emit itemRemoved(itemId);
        emit itemCountChanged(m_items.count());
        emit printfileChanged();
    }
}

PrintfileItem* Printfile::getItem(const QString &itemId) const
{
    return m_items.value(itemId).data();
}

QList<QWeakPointer<PrintfileItem>> Printfile::getItems() const
{
    QList<QWeakPointer<PrintfileItem>> result;

    for (const auto &item: std::as_const(m_items)) {
        result.append(item);
    }

    return result;
}

void Printfile::clearItems()
{
    m_items.clear();
    emit itemCountChanged(0);
    emit printfileChanged();
    emit cleared();
}

bool Printfile::hasItem(const QString &itemId) const
{
    return m_items.contains(itemId);
}

PrintfileItem* Printfile::createItem(const QString &itemId, const QString &name, const QString &qmlPath)
{
    if (m_items.contains(itemId)) {
        qWarning() << "Item with ID" << itemId << "already exists in printfile";
        return {};
    }

    auto item = QSharedPointer<PrintfileItem>::create(itemId, name, qmlPath);
    QQmlEngine::setObjectOwnership(item.data(), QQmlEngine::CppOwnership);

    if (addItem(item)) {
        return item.data();
    }

    return {};
}

QVariantMap Printfile::toVariantMap() const
{
    QVariantMap data;
    data["printfileId"] = m_printfileId;
    data["name"] = m_name;
    data["description"] = m_description;
    data["backgroundColor"] = m_backgroundColor;

    QVariantList itemsList;
    for (auto it = m_items.begin(); it != m_items.end(); ++it) {
        if (it.value()) {
            itemsList.append(it.value()->toVariantMap());
        }
    }
    data["items"] = itemsList;

    return data;
}

void Printfile::fromVariantMap(const QVariantMap &data)
{
    // Don't change printfileId as it should be immutable
    setName(data.value("name", m_name).toString());
    setDescription(data.value("description", m_description).toString());
    setBackgroundColor(data.value("backgroundColor", m_backgroundColor).toString());

    // Clear existing items
    clearItems();

    // Load items
    const QVariantList itemsList = data.value("items").toList();
    for (const QVariant &itemVariant : itemsList) {
        QVariantMap itemData = itemVariant.toMap();
        QString itemId = itemData.value("itemId").toString();

        if (!itemId.isEmpty()) {
            QSharedPointer<PrintfileItem> item = QSharedPointer<PrintfileItem>::create(itemId);
            QQmlEngine::setObjectOwnership(item.data(), QQmlEngine::CppOwnership);
            item->fromVariantMap(itemData);
            addItem(item);
        }
    }
}

void Printfile::onItemChanged()
{
    emit printfileChanged();
}
