#ifndef PRINTFILE_H
#define PRINTFILE_H

#include <QObject>
#include <QString>
#include <QVariant>
#include <QVariantMap>
#include <QList>
#include "PrintfileItem.h"

class Printfile : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QString printfileId READ printfileId CONSTANT)
    Q_PROPERTY(QString name READ name WRITE setName NOTIFY nameChanged)
    Q_PROPERTY(QString description READ description WRITE setDescription NOTIFY descriptionChanged)
    Q_PROPERTY(QString backgroundColor READ backgroundColor WRITE setBackgroundColor NOTIFY backgroundColorChanged)
    Q_PROPERTY(int itemCount READ itemCount NOTIFY itemCountChanged)

public:
    explicit Printfile(const QString &printfileId, QObject *parent = nullptr);
    explicit Printfile(const QString &printfileId, const QString &name, QObject *parent = nullptr);

    // Getters
    QString printfileId() const { return m_printfileId; }
    QString name() const { return m_name; }
    QString description() const { return m_description; }
    QString backgroundColor() const { return m_backgroundColor; }
    int itemCount() const { return m_items.count(); }

    // Setters
    void setName(const QString &name);
    void setDescription(const QString &description);
    void setBackgroundColor(const QString &color);

    // Item management
    bool addItem(QSharedPointer<PrintfileItem> item);
    Q_INVOKABLE void removeItem(const QString &itemId);
    Q_INVOKABLE PrintfileItem* getItem(const QString &itemId) const;
    Q_INVOKABLE QList<QWeakPointer<PrintfileItem>> getItems() const;
    Q_INVOKABLE void clearItems();
    Q_INVOKABLE bool hasItem(const QString &itemId) const;
    Q_INVOKABLE PrintfileItem* createItem(const QString &itemId, const QString &name, const QString &qmlPath);

    // Serialization
    Q_INVOKABLE QVariantMap toVariantMap() const;
    Q_INVOKABLE void fromVariantMap(const QVariantMap &data);

signals:
    void nameChanged(const QString &name);
    void descriptionChanged(const QString &description);
    void backgroundColorChanged(const QString &color);
    void itemCountChanged(int count);
    void itemAdded(QWeakPointer<PrintfileItem> item);
    void itemRemoved(const QString &itemId);
    void printfileChanged();
    void cleared();

private slots:
    void onItemChanged();

private:
    QString m_printfileId;
    QString m_name;
    QString m_description;
    QString m_backgroundColor = "#FFFFFF";
    QMap<QString, QSharedPointer<PrintfileItem>> m_items;
};

#endif // PRINTFILE_H
