#ifndef PRINTFILEITEM_H
#define PRINTFILEITEM_H

#include <QObject>
#include <QString>
#include <QVariant>
#include <QVariantMap>
#include <QUrl>

class PrintfileItem : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QString itemId READ itemId CONSTANT)
    Q_PROPERTY(QString name READ name WRITE setName NOTIFY nameChanged)
    Q_PROPERTY(QString qmlPath READ qmlPath WRITE setQmlPath NOTIFY qmlPathChanged)
    Q_PROPERTY(qreal x READ x WRITE setX NOTIFY xChanged)
    Q_PROPERTY(qreal y READ y WRITE setY NOTIFY yChanged)
    Q_PROPERTY(qreal width READ width WRITE setWidth NOTIFY widthChanged)
    Q_PROPERTY(qreal height READ height WRITE setHeight NOTIFY heightChanged)
    Q_PROPERTY(qreal rotation READ rotation WRITE setRotation NOTIFY rotationChanged)
    Q_PROPERTY(qreal scale READ scale WRITE setScale NOTIFY scaleChanged)
    Q_PROPERTY(QVariantMap properties READ properties WRITE setProperties NOTIFY propertiesChanged)
    Q_PROPERTY(bool visible READ visible WRITE setVisible NOTIFY visibleChanged)
    Q_PROPERTY(int zOrder READ zOrder WRITE setZOrder NOTIFY zOrderChanged)

public:
    explicit PrintfileItem(const QString &itemId, QObject *parent = nullptr);
    explicit PrintfileItem(const QString &itemId, const QString &name, const QString &qmlPath, QObject *parent = nullptr);

    // Getters
    QString itemId() const { return m_itemId; }
    QString name() const { return m_name; }
    QString qmlPath() const { return m_qmlPath; }
    qreal x() const { return m_x; }
    qreal y() const { return m_y; }
    qreal width() const { return m_width; }
    qreal height() const { return m_height; }
    qreal rotation() const { return m_rotation; }
    qreal scale() const { return m_scale; }
    QVariantMap properties() const { return m_properties; }
    bool visible() const { return m_visible; }
    int zOrder() const { return m_zOrder; }

    // Setters
    void setName(const QString &name);
    void setQmlPath(const QString &qmlPath);
    void setX(qreal x);
    void setY(qreal y);
    void setWidth(qreal width);
    void setHeight(qreal height);
    void setRotation(qreal rotation);
    void setScale(qreal scale);
    void setProperties(const QVariantMap &properties);
    void setVisible(bool visible);
    void setZOrder(int zOrder);

    // Property helpers
    Q_INVOKABLE void setProperty(const QString &key, const QVariant &value);
    Q_INVOKABLE QVariant getProperty(const QString &key, const QVariant &defaultValue = QVariant()) const;
    Q_INVOKABLE bool hasProperty(const QString &key) const;
    Q_INVOKABLE void removeProperty(const QString &key);

    // Position helpers
    Q_INVOKABLE void setPosition(qreal x, qreal y);
    Q_INVOKABLE void setSize(qreal width, qreal height);
    Q_INVOKABLE void setTransform(qreal x, qreal y, qreal width, qreal height, qreal rotation = 0, qreal scale = 1.0);

    // Serialization
    Q_INVOKABLE QVariantMap toVariantMap() const;
    Q_INVOKABLE void fromVariantMap(const QVariantMap &data);

signals:
    void nameChanged(const QString &name);
    void qmlPathChanged(const QString &qmlPath);
    void xChanged(qreal x);
    void yChanged(qreal y);
    void widthChanged(qreal width);
    void heightChanged(qreal height);
    void rotationChanged(qreal rotation);
    void scaleChanged(qreal scale);
    void propertiesChanged(const QVariantMap &properties);
    void visibleChanged(bool visible);
    void zOrderChanged(int zOrder);
    void itemChanged();

private:
    QString m_itemId;
    QString m_name;
    QString m_qmlPath;
    qreal m_x = 0.0;
    qreal m_y = 0.0;
    qreal m_width = 100.0;
    qreal m_height = 100.0;
    qreal m_rotation = 0.0;
    qreal m_scale = 1.0;
    QVariantMap m_properties;
    bool m_visible = true;
    int m_zOrder = 0;
};

#endif // PRINTFILEITEM_H
