#ifndef PRINTFILEMANAGER_H
#define PRINTFILEMANAGER_H

#include <QObject>
#include <QMap>
#include <QList>
#include <QString>
#include <QVariantMap>
#include <QSharedPointer>

#include <models/PrintfileModel.h>
#include <models/PrintfileItemModel.h>
#include <ConfigManager.h>
#include "Printfile.h"
#include "PrintfileItem.h"

class PrintfileManager : public QObject
{
    Q_OBJECT
    Q_PROPERTY(PrintfileModel* printfileModel READ printfileModel CONSTANT)
    Q_PROPERTY(PrintfileItemModel* currentPrintfileItemModel READ currentPrintfileItemModel NOTIFY currentPrintfileChanged)
    Q_PROPERTY(Printfile* currentPrintfile READ currentPrintfile NOTIFY currentPrintfileChanged)
    Q_PROPERTY(QString currentPrintfileId READ currentPrintfileId WRITE setCurrentPrintfileId NOTIFY currentPrintfileChanged)
    Q_PROPERTY(QString selectedPrintfileId READ selectedPrintfileId WRITE setSelectedPrintfileId NOTIFY selectedPrintfileIdChanged)
    Q_PROPERTY(int printfileCount READ printfileCount NOTIFY printfileCountChanged)
    Q_PROPERTY(PrintfileItemModel* editPrintfileItemModel READ editPrintfileItemModel NOTIFY editPrintfileChanged)
    Q_PROPERTY(Printfile* editPrintfile READ editPrintfile NOTIFY editPrintfileChanged)
    Q_PROPERTY(bool editMode READ editMode NOTIFY editModeChanged)
    Q_PROPERTY(bool unsavedChanges READ unsavedChanges NOTIFY unsavedChangesChanged)

public:
    explicit PrintfileManager(ConfigManager* configManager = nullptr, QObject *parent = nullptr);
    virtual ~PrintfileManager();

    // Model access
    PrintfileModel* printfileModel() const { return m_printfileModel; }
    PrintfileItemModel* currentPrintfileItemModel() const { return m_currentPrintfileItemModel; }
    PrintfileItemModel* editPrintfileItemModel() const { return m_editPrintfileItemModel; }

    // Current printfile
    Printfile* currentPrintfile() const { return m_currentPrintfile; }
    QString currentPrintfileId() const;
    void setCurrentPrintfileId(const QString &printfileId);
    QString selectedPrintfileId() const;
    void setSelectedPrintfileId(const QString &printfileId);
    int printfileCount() const { return m_printfiles.count(); }
    Printfile* editPrintfile() const;
    bool editMode() const;
    bool unsavedChanges() const { return m_unsavedChanges; }

    // Printfile management
    Q_INVOKABLE QWeakPointer<Printfile> createPrintfile(const QString &printfileId = QString(), const QString &name = QString());
    Q_INVOKABLE bool deletePrintfile(const QString &printfileId);
    Q_INVOKABLE Printfile* getPrintfile(const QString &printfileId) const;
    Q_INVOKABLE QStringList getPrintfileIds() const;
    Q_INVOKABLE bool hasPrintfile(const QString &printfileId) const;

    // Printfile item management (for edit printfile)
    Q_INVOKABLE PrintfileItem* createEditPrintfileItem(const QString &itemId, const QString &name, const QString &qmlPath);
    Q_INVOKABLE bool deleteEditPrintfileItem(const QString &itemId);
    Q_INVOKABLE PrintfileItem* getEditPrintfileItem(const QString &itemId) const;
    Q_INVOKABLE PrintfileItem* duplicateEditPrintfileItem(const QString &sourceItemId, const QString &newItemId = QString());

    // Item positioning helpers (edit mode)
    Q_INVOKABLE void movePrintfileItem(const QString &itemId, qreal x, qreal y);
    Q_INVOKABLE void resizePrintfileItem(const QString &itemId, qreal width, qreal height);
    Q_INVOKABLE void transformPrintfileItem(const QString &itemId, qreal x, qreal y, qreal width, qreal height, qreal rotation = 0, qreal scale = 1.0);
    Q_INVOKABLE void setPrintfileItemProperty(const QString &itemId, const QString &key, const QVariant &value);

    // Serialization and persistence
    Q_INVOKABLE bool savePrintfile(const QString &printfileId);
    Q_INVOKABLE bool loadPrintfile(const QString &printfileId);
    Q_INVOKABLE bool saveAllPrintfiles();
    Q_INVOKABLE bool loadAllPrintfiles();
    Q_INVOKABLE QVariantMap exportPrintfile(const QString &printfileId) const;
    Q_INVOKABLE bool importPrintfile(const QVariantMap &data, const QString &newPrintfileId = QString());

    // File operations
    Q_INVOKABLE bool savePrintfileToFile(const QString &printfileId, const QString &filePath);
    Q_INVOKABLE bool loadPrintfileFromFile(const QString &filePath, const QString &newPrintfileId = QString());

    // Edit mode API
    Q_INVOKABLE void startEditPrintfile(const QString &printfileId);
    Q_INVOKABLE void startNewPrintfile();
    Q_INVOKABLE void saveEditPrintfile();
    Q_INVOKABLE void discardEditPrintfile();

    void setEditMode(bool newEditMode);
    void setUnsavedChanges(bool unsavedChanges);

signals:
    void currentPrintfileChanged();
    void selectedPrintfileIdChanged(const QString &printfileId);
    void printfileCountChanged();
    void printifleCreated(const QString &printfileId);
    void printfileDeleted(const QString &printfileId);
    void printfileItemCreated(const QString &printfileId, const QString &itemId);
    void printfileItemDeleted(const QString &printfileId, const QString &itemId);
    void printfilesLoaded();
    void printfilesSaved();
    void editPrintfileChanged(Printfile* editPrintfile);
    void editModeChanged();
    void unsavedChangesChanged(bool unsaved);

private slots:
    void onPrintfileChanged();
    void onPrintfileItemAdded(QWeakPointer<PrintfileItem> item);
    void onPrintfileItemRemoved(const QString &itemId);

private:
    QSharedPointer<Printfile> duplicatePrintfile(const QString &sourcePrintfileId, const QString &newPrintfileId = QString(), const QString &newName = QString());
    void registerPrintfile(const QSharedPointer<Printfile> &printfileObj);
    void unregisterPrintfile(const QSharedPointer<Printfile>& printfileObj);
    void updateCurrentPrintfileItemModel();
    QString generateUniquePrintfileId(const QString &basetName = "printfile") const;
    QString generateUniqueItemId(const QString printfileId = QString(), const QString &baseName = "item") const;

    QMap<QString, QSharedPointer<Printfile>> m_printfiles;
    PrintfileModel *m_printfileModel;
    PrintfileItemModel *m_currentPrintfileItemModel;
    Printfile *m_currentPrintfile;
    QString m_selectedPrintfileId;

    PrintfileItemModel* m_editPrintfileItemModel;
    QSharedPointer<Printfile> m_editPrintfile;
    QString m_editPrintfileOriginalId; // empty if new printfile
    bool m_editMode;
    bool m_unsavedChanges;

    ConfigManager* m_configManager;
};

#endif // PRINTFILEMANAGER_H
