#include "PrintfileItem.h"
#include <QDebug>

PrintfileItem::PrintfileItem(const QString &itemId, QObject *parent)
    : QObject(parent)
    , m_itemId(itemId)
    , m_name(itemId)
{
}

PrintfileItem::PrintfileItem(const QString &itemId, const QString &name, const QString &qmlPath, QObject *parent)
    : QObject(parent)
    , m_itemId(itemId)
    , m_name(name)
    , m_qmlPath(qmlPath)
{
}

void PrintfileItem::setName(const QString &name)
{
    if (m_name != name) {
        m_name = name;
        emit nameChanged(name);
        emit itemChanged();
    }
}

void PrintfileItem::setQmlPath(const QString &qmlPath)
{
    if (m_qmlPath != qmlPath) {
        m_qmlPath = qmlPath;
        emit qmlPathChanged(qmlPath);
        emit itemChanged();
    }
}

void PrintfileItem::setX(qreal x)
{
    if (qAbs(m_x - x) > 0.001) {
        m_x = x;
        emit xChanged(x);
        emit itemChanged();
    }
}

void PrintfileItem::setY(qreal y)
{
    if (qAbs(m_y - y) > 0.001) {
        m_y = y;
        emit yChanged(y);
        emit itemChanged();
    }
}

void PrintfileItem::setWidth(qreal width)
{
    if (qAbs(m_width - width) > 0.001) {
        m_width = width;
        emit widthChanged(width);
        emit itemChanged();
    }
}

void PrintfileItem::setHeight(qreal height)
{
    if (qAbs(m_height - height) > 0.001) {
        m_height = height;
        emit heightChanged(height);
        emit itemChanged();
    }
}

void PrintfileItem::setRotation(qreal rotation)
{
    if (qAbs(m_rotation - rotation) > 0.001) {
        m_rotation = rotation;
        emit rotationChanged(rotation);
        emit itemChanged();
    }
}

void PrintfileItem::setScale(qreal scale)
{
    if (qAbs(m_scale - scale) > 0.001) {
        m_scale = scale;
        emit scaleChanged(scale);
        emit itemChanged();
    }
}

void PrintfileItem::setProperties(const QVariantMap &properties)
{
    if (m_properties != properties) {
        m_properties = properties;
        emit propertiesChanged(properties);
        emit itemChanged();
    }
}

void PrintfileItem::setVisible(bool visible)
{
    if (m_visible != visible) {
        m_visible = visible;
        emit visibleChanged(visible);
        emit itemChanged();
    }
}

void PrintfileItem::setZOrder(int zOrder)
{
    if (m_zOrder != zOrder) {
        m_zOrder = zOrder;
        emit zOrderChanged(zOrder);
        emit itemChanged();
    }
}

void PrintfileItem::setProperty(const QString &key, const QVariant &value)
{
    if (m_properties.value(key) != value) {
        m_properties[key] = value;
        emit propertiesChanged(m_properties);
        emit itemChanged();
    }
}

QVariant PrintfileItem::getProperty(const QString &key, const QVariant &defaultValue) const
{
    return m_properties.value(key, defaultValue);
}

bool PrintfileItem::hasProperty(const QString &key) const
{
    return m_properties.contains(key);
}

void PrintfileItem::removeProperty(const QString &key)
{
    if (m_properties.contains(key)) {
        m_properties.remove(key);
        emit propertiesChanged(m_properties);
        emit itemChanged();
    }
}

void PrintfileItem::setPosition(qreal x, qreal y)
{
    bool changed = false;
    if (qAbs(m_x - x) > 0.001) {
        m_x = x;
        emit xChanged(x);
        changed = true;
    }
    if (qAbs(m_y - y) > 0.001) {
        m_y = y;
        emit yChanged(y);
        changed = true;
    }
    if (changed) {
        emit itemChanged();
    }
}

void PrintfileItem::setSize(qreal width, qreal height)
{
    bool changed = false;
    if (qAbs(m_width - width) > 0.001) {
        m_width = width;
        emit widthChanged(width);
        changed = true;
    }
    if (qAbs(m_height - height) > 0.001) {
        m_height = height;
        emit heightChanged(height);
        changed = true;
    }
    if (changed) {
        emit itemChanged();
    }
}

void PrintfileItem::setTransform(qreal x, qreal y, qreal width, qreal height, qreal rotation, qreal scale)
{
    bool changed = false;

    if (qAbs(m_x - x) > 0.001) {
        m_x = x;
        emit xChanged(x);
        changed = true;
    }
    if (qAbs(m_y - y) > 0.001) {
        m_y = y;
        emit yChanged(y);
        changed = true;
    }
    if (qAbs(m_width - width) > 0.001) {
        m_width = width;
        emit widthChanged(width);
        changed = true;
    }
    if (qAbs(m_height - height) > 0.001) {
        m_height = height;
        emit heightChanged(height);
        changed = true;
    }
    if (qAbs(m_rotation - rotation) > 0.001) {
        m_rotation = rotation;
        emit rotationChanged(rotation);
        changed = true;
    }
    if (qAbs(m_scale - scale) > 0.001) {
        m_scale = scale;
        emit scaleChanged(scale);
        changed = true;
    }

    if (changed) {
        emit itemChanged();
    }
}

QVariantMap PrintfileItem::toVariantMap() const
{
    QVariantMap data;
    data["itemId"] = m_itemId;
    data["name"] = m_name;
    data["qmlPath"] = m_qmlPath;
    data["x"] = m_x;
    data["y"] = m_y;
    data["width"] = m_width;
    data["height"] = m_height;
    data["rotation"] = m_rotation;
    data["scale"] = m_scale;
    data["properties"] = m_properties;
    data["visible"] = m_visible;
    data["zOrder"] = m_zOrder;
    return data;
}

void PrintfileItem::fromVariantMap(const QVariantMap &data)
{
    // Don't change itemId as it should be immutable
    setName(data.value("name", m_name).toString());
    setQmlPath(data.value("qmlPath", m_qmlPath).toString());
    setX(data.value("x", m_x).toReal());
    setY(data.value("y", m_y).toReal());
    setWidth(data.value("width", m_width).toReal());
    setHeight(data.value("height", m_height).toReal());
    setRotation(data.value("rotation", m_rotation).toReal());
    setScale(data.value("scale", m_scale).toReal());
    setProperties(data.value("properties", m_properties).toMap());
    setVisible(data.value("visible", m_visible).toBool());
    setZOrder(data.value("zOrder", m_zOrder).toInt());
}
