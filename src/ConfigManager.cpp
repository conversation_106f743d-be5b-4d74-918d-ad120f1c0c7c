#include "ConfigManager.h"
#include <QDebug>
#include <QCoreApplication>
#include <QDir>

ConfigManager::ConfigManager(QObject *parent) : QObject(parent) {
    // Use explicit path for config/ui.ini
    // Use QSettings in UserScope for user config (best practice)
    settings = new QSettings(QSettings::IniFormat, QSettings::UserScope, "Prospr", "Light", this);
    // If user config does not exist, optionally load defaults from app bundle
    QString userConfigPath = settings->fileName();
    QFileInfo userConfigFile(userConfigPath);
    if (!userConfigFile.exists()) {
        QString defaultConfigPath = QCoreApplication::applicationDirPath() + "/../Resources/config/ui.ini";
        QSettings defaultSettings(defaultConfigPath, QSettings::IniFormat);
        // Copy all keys from defaultSettings to user settings
        bool wroteValue = false;
        foreach (const QString &key, defaultSettings.allKeys()) {
            settings->setValue(key, defaultSettings.value(key));
            wroteValue = true;
        }
        // If no keys were copied (empty default), force file creation with a dummy value
        if (!wroteValue) {
            settings->setValue("__created__", true);
        }
        settings->sync();
    }
    loadSettings();
}

void ConfigManager::loadSettings()
{
    // Debug: print the INI file path being used
    qDebug() << "ConfigManager: Loading INI from:" << settings->fileName();
    m_theme = settings->value("UI/theme", "dark").toString();
    m_windowWidth = settings->value("UI/windowWidth", 1920).toInt();
    m_windowHeight = settings->value("UI/windowHeight", 1080).toInt();
    qDebug() << "ConfigManager: Loaded windowWidth:" << m_windowWidth << ", windowHeight:" << m_windowHeight;
    m_startUser = settings->value("User/startUser", "admin").toString();
    m_startRole = settings->value("User/startRole", "superuser").toString();

    // Virtual Keyboard settings
    m_vkTheme = settings->value("VirtualKeyboard/theme", "light").toString();
    m_vkLocale = settings->value("VirtualKeyboard/locale", "en_US").toString();
    m_vkActiveLocales = settings->value("VirtualKeyboard/activeLocales", "en_US").toString().split(",");
    m_vkWordCandidateListEnabled = settings->value("VirtualKeyboard/wordCandidateListEnabled", false).toBool();

    // Startup screen
    m_startScreen = settings->value("UI/startScreen", "LogIn").toString();
    m_selectedPrintfileId = settings->value("Printfile/selectedPrintfileId", "").toString();

    m_printCreateShowGridLines = settings->value("Printfile/printCreateShowGridLines", false).toBool();
}

bool ConfigManager::printCreateShowGridLines() const
{
    return m_printCreateShowGridLines;
}

QString ConfigManager::theme() const {
    return m_theme;
}

int ConfigManager::windowWidth() const {
    return m_windowWidth;
}

int ConfigManager::windowHeight() const {
    return m_windowHeight;
}

QString ConfigManager::startUser() const {
    return m_startUser;
}

QString ConfigManager::startRole() const {
    return m_startRole;
}

QString ConfigManager::vkTheme() const {
    return m_vkTheme;
}

QString ConfigManager::vkLocale() const {
    return m_vkLocale;
}

QStringList ConfigManager::vkActiveLocales() const {
    return m_vkActiveLocales;
}

bool ConfigManager::vkWordCandidateListEnabled() const {
    return m_vkWordCandidateListEnabled;
}

QString ConfigManager::startScreen() const {
    return m_startScreen;
}

QString ConfigManager::selectedPrintfileId() const {
    return m_selectedPrintfileId;
}

void ConfigManager::setSelectedPrintfileId(const QString &printfileId) {
    if (m_selectedPrintfileId != printfileId) {
        m_selectedPrintfileId = printfileId;
        settings->setValue("Printfile/selectedPrintfileId", printfileId);
        settings->sync();
        emit configChanged();
    }
}

void ConfigManager::setPrintCreateShowGridLines(bool newPrintCreateShowGridLines)
{
    if (m_printCreateShowGridLines != newPrintCreateShowGridLines) {
        m_printCreateShowGridLines = newPrintCreateShowGridLines;
        settings->setValue("Printfile/printCreateShowGridLines", newPrintCreateShowGridLines);
        settings->sync();
        emit configChanged();
    }
}

void ConfigManager::reload() {
    settings->sync();
    loadSettings();
    emit configChanged();
}
