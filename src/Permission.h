#ifndef PERMISSION_H
#define PERMISSION_H

#include <QObject>

class Permission
{
    Q_GADGET
    Q_PROPERTY(QString name READ name WRITE setName FINAL)
    Q_PROPERTY(QString group READ group WRITE setGroup FINAL)

public:
    Permission();
    Permission(const QString name, const QString group);
    friend bool operator==(const Permission& p1, const Permission& p2);

    QString name() const;
    void setName(const QString &newName);

    QString group() const;
    void setGroup(const QString &newGroup);

private:
    QString m_name;
    QString m_group;
};

Q_DECLARE_METATYPE(Permission)

inline uint qHash(const Permission &key, uint seed = 0) {
    return qHash(key.name(), seed) ^ qHash(key.group(), seed << 1);
}

#endif // PERMISSION_H
