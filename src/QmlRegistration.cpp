#include <QQmlEngine>

#include "TimeHandler.h"
#include "src/models/PermissionsModel.h"
#include "src/models/ErrorModel.h"
#include "src/ErrorManager.h"
#include "printer/ServiceFunction.h"
#include "printer/ServiceStatus.h"
#include "printfile/PrintfileItem.h"
#include "printfile/Printfile.h"

void registerQmlTypes()
{
    qmlRegisterType<PermissionsList>("Backend.UserManager", 1, 0, "PermissionsList");
    qmlRegisterType<TimeHandler>("timehandler", 1, 0, "TimeHandler");

    // Register ErrorModel for QML
    qmlRegisterType<ErrorModel>("Backend.Error", 1, 0, "ErrorModel");
    // Register ErrorManager singleton
    qmlRegisterSingletonInstance<ErrorManager>("Backend.Error", 1, 0, "ErrorManager", ErrorManager::instance());

    // Register service management types
    qmlRegisterUncreatableType<ServiceFunction>("Backend.PrinterManager", 1, 0, "ServiceFunction",
                                                "ServiceFunction is managed by ServiceManager");
    qmlRegisterUncreatableType<ServiceStatus>("Backend.PrinterManager", 1, 0, "ServiceStatus",
                                              "ServiceStatus is managed by ServiceManager");

    // Register enums
    qmlRegisterUncreatableMetaObject(ServiceFunction::staticMetaObject, "Backend.PrinterManager", 1, 0,
                                     "ServiceFunctionState", "Access to enums & flags only");
    qmlRegisterUncreatableMetaObject(ServiceStatus::staticMetaObject, "Backend.PrinterManager", 1, 0,
                                     "ServiceStatusLevel", "Access to enums & flags only");

    // Register printfile management types
    qmlRegisterUncreatableType<PrintfileItem>("Backend.PrintfileManager", 1, 0, "PrintfileItem",
                                              "PrintfileItem is managed by PrintfileManager");
    qmlRegisterUncreatableType<Printfile>("Backend.PrintfileManager", 1, 0, "Printfile",
                                          "Printfile is managed by PrintfileManager");
}
