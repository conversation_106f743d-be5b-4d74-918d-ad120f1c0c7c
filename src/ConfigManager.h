#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include <QObject>
#include <QSettings>
#include <QString>

class ConfigManager : public QObject {
    Q_OBJECT
    Q_PROPERTY(int windowWidth READ windowWidth NOTIFY configChanged)
    Q_PROPERTY(int windowHeight READ windowHeight NOTIFY configChanged)
    Q_PROPERTY(QString theme READ theme NOTIFY configChanged)
    Q_PROPERTY(QString startUser READ startUser NOTIFY configChanged)
    Q_PROPERTY(QString startRole READ startRole NOTIFY configChanged)
    Q_PROPERTY(QString vkTheme READ vkTheme NOTIFY configChanged)
    Q_PROPERTY(QString vkLocale READ vkLocale NOTIFY configChanged)
    Q_PROPERTY(QStringList vkActiveLocales READ vkActiveLocales NOTIFY configChanged)
    Q_PROPERTY(bool vkWordCandidateListEnabled READ vkWordCandidateListEnabled NOTIFY configChanged)
    Q_PROPERTY(QString startScreen READ startScreen NOTIFY configChanged)
    Q_PROPERTY(QString selectedPrintfileId READ selectedPrintfileId WRITE setSelectedPrintfileId NOTIFY configChanged)
    Q_PROPERTY(bool printCreateShowGridLines READ printCreateShowGridLines WRITE setPrintCreateShowGridLines NOTIFY configChanged)
public:
    explicit ConfigManager(QObject *parent = nullptr);

    Q_INVOKABLE QString theme() const;
    Q_INVOKABLE int windowWidth() const;
    Q_INVOKABLE int windowHeight() const;
    Q_INVOKABLE QString startUser() const;
    Q_INVOKABLE QString startRole() const;
    Q_INVOKABLE QString vkTheme() const;
    Q_INVOKABLE QString vkLocale() const;
    Q_INVOKABLE QStringList vkActiveLocales() const;
    Q_INVOKABLE bool vkWordCandidateListEnabled() const;
    Q_INVOKABLE QString startScreen() const;
    QString selectedPrintfileId() const;
    bool printCreateShowGridLines() const;

    void setSelectedPrintfileId(const QString &printfileId);
    void setPrintCreateShowGridLines(bool newPrintCreateShowGridLines);

    void reload();

signals:
    void configChanged();

    void printCreateShowGridLinesChanged();

private:
    QSettings *settings;
    void loadSettings();
    QString m_theme;
    int m_windowWidth;
    int m_windowHeight;
    QString m_startUser;
    QString m_startRole;
    QString m_vkTheme;
    QString m_vkLocale;
    QStringList m_vkActiveLocales;
    bool m_vkWordCandidateListEnabled;
    QString m_startScreen;
    QString m_selectedPrintfileId;
    bool m_printCreateShowGridLines;
};

#endif // CONFIGMANAGER_H
