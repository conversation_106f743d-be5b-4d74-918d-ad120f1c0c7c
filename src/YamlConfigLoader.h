#ifndef YAMLCONFIGLOADER_H
#define YAMLCONFIGLOADER_H

#include <QObject>
#include <QString>
#include <QVariant>

class YamlConfigLoader : public QObject {
    Q_OBJECT
public:
    explicit YamlConfigLoader(QObject *parent = nullptr);
    bool load(const QString &filePath);

    QVariant users() const;
    QVariant printers() const;
    QVariant settings() const;

private:
    QVariant m_users;
    QVariant m_printers;
    QVariant m_settings;
};

#endif // YAMLCONFIGLOADER_H
