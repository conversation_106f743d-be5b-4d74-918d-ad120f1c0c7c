#include "ServiceFunctionModel.h"
#include <QVariant>

ServiceFunctionModel::ServiceFunctionModel(QObject *parent)
    : QAbstractListModel(parent)
{
}

int ServiceFunctionModel::rowCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent)
    return m_functions.size();
}

QVariant ServiceFunctionModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() < 0 || index.row() >= m_functions.size()) {
        return QVariant();
    }

    ServiceFunction *function = m_functions.at(index.row());
    if (!function) {
        return QVariant();
    }

    switch (role) {
    case FunctionIdRole:
        return function->functionId();
    case TitleRole:
        return function->title();
    case DescriptionRole:
        return function->description();
    case StateRole:
        return static_cast<int>(function->state());
    case IsActiveRole:
        return function->isActive();
    case CanStartRole:
        return function->canStart();
    case ProgressRole:
        return function->progress();
    case StatusMessageRole:
        return function->statusMessage();
    case RemainingTimeRole:
        return function->remainingTime();
    case FunctionObjectRole:
        return QVariant::fromValue(function);
    default:
        return QVariant();
    }
}

QHash<int, QByteArray> ServiceFunctionModel::roleNames() const
{
    QHash<int, QByteArray> roles;
    roles[FunctionIdRole] = "functionId";
    roles[TitleRole] = "title";
    roles[DescriptionRole] = "description";
    roles[StateRole] = "state";
    roles[IsActiveRole] = "isActive";
    roles[CanStartRole] = "canStart";
    roles[ProgressRole] = "progress";
    roles[StatusMessageRole] = "statusMessage";
    roles[RemainingTimeRole] = "remainingTime";
    roles[FunctionObjectRole] = "functionObject";
    return roles;
}

void ServiceFunctionModel::setFunctions(const QList<ServiceFunction*> &functions)
{
    beginResetModel();

    // Disconnect old functions
    for (ServiceFunction *function : std::as_const(m_functions)) {
        if (function) {
            disconnect(function, nullptr, this, nullptr);
        }
    }

    m_functions = functions;

    // Connect new functions
    for (ServiceFunction *function : std::as_const(m_functions)) {
        if (function) {
            connect(function, &ServiceFunction::stateChanged, this, &ServiceFunctionModel::onFunctionChanged);
            connect(function, &ServiceFunction::canStartChanged, this, &ServiceFunctionModel::onFunctionChanged);
            connect(function, &ServiceFunction::progressChanged, this, &ServiceFunctionModel::onFunctionChanged);
            connect(function, &ServiceFunction::statusMessageChanged, this, &ServiceFunctionModel::onFunctionChanged);
        }
    }

    endResetModel();
}

void ServiceFunctionModel::addFunction(ServiceFunction *function)
{
    if (!function || m_functions.contains(function)) {
        return;
    }

    beginInsertRows(QModelIndex(), m_functions.size(), m_functions.size());
    m_functions.append(function);

    connect(function, &ServiceFunction::stateChanged, this, &ServiceFunctionModel::onFunctionChanged);
    connect(function, &ServiceFunction::canStartChanged, this, &ServiceFunctionModel::onFunctionChanged);
    connect(function, &ServiceFunction::progressChanged, this, &ServiceFunctionModel::onFunctionChanged);
    connect(function, &ServiceFunction::statusMessageChanged, this, &ServiceFunctionModel::onFunctionChanged);

    endInsertRows();
}

void ServiceFunctionModel::removeFunction(ServiceFunction *function)
{
    int index = m_functions.indexOf(function);
    if (index >= 0) {
        beginRemoveRows(QModelIndex(), index, index);

        if (function) {
            disconnect(function, nullptr, this, nullptr);
        }

        m_functions.removeAt(index);
        endRemoveRows();
    }
}

ServiceFunction* ServiceFunctionModel::functionAt(int index) const
{
    if (index >= 0 && index < m_functions.size()) {
        return m_functions.at(index);
    }
    return nullptr;
}

void ServiceFunctionModel::onFunctionChanged()
{
    ServiceFunction *function = qobject_cast<ServiceFunction*>(sender());
    if (function) {
        int index = m_functions.indexOf(function);
        if (index >= 0) {
            QModelIndex modelIndex = this->index(index);
            emit dataChanged(modelIndex, modelIndex);
        }
    }
}
