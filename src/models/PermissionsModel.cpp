#include "PermissionsModel.h"
#include <QDebug>

// PermissionsDataModel
// --------------------

PermissionsDataModel::PermissionsDataModel(QObject* parent)
    : QAbstractListModel(parent) {}

void PermissionsDataModel::setPermissions(const QList<Permission> &permissions) {
    beginResetModel();
    m_permissions = permissions;
    endResetModel();
}

int PermissionsDataModel::rowCount(const QModelIndex &parent) const {
    Q_UNUSED(parent);
    return m_permissions.size();
}

QVariant PermissionsDataModel::data(const QModelIndex &index, int role) const {
    if (!index.isValid() || index.row() < 0 || index.row() >= m_permissions.size())
        return QVariant();

    Permission perm = m_permissions.at(index.row());

    if (role == NameRole)
        return perm.name();
    if (role == GroupRole)
        return perm.group();

    return QVariant();
}

QHash<int, QByteArray> PermissionsDataModel::roleNames() const
{
    return {{PermissionRoles::NameRole, "name"}, {PermissionRoles::GroupRole, "group"}};
}

void PermissionsDataModel::addPermission(const Permission& permission) {
    int newRow = m_permissions.size();
    beginInsertRows(QModelIndex(), newRow, newRow);
    m_permissions.append(permission);
    endInsertRows();
}

void PermissionsDataModel::removePermissionAt(int row) {
    if (row < 0 || row >= m_permissions.size()) return;
    beginRemoveRows(QModelIndex(), row, row);
    m_permissions.removeAt(row);
    endRemoveRows();
}

// PermissionsGroupFilterProxyModel
// -------------------------------

PermissionsGroupFilterProxyModel::PermissionsGroupFilterProxyModel(QObject* parent)
    : QSortFilterProxyModel(parent) {
}

// PermissionsList
// --------------

PermissionsList::PermissionsList(QObject* parent)
    : QObject(parent), m_groupFilter("") {
    m_sourceModel = new PermissionsDataModel(this);
    m_proxyModel = new PermissionsGroupFilterProxyModel(this);
    m_proxyModel->setFilterRole(PermissionsDataModel::PermissionRoles::GroupRole);
    m_proxyModel->setFilterCaseSensitivity(Qt::CaseInsensitive);
    m_proxyModel->setSortRole(PermissionsDataModel::PermissionRoles::NameRole);
    m_proxyModel->setSourceModel(m_sourceModel);
}

QAbstractItemModel* PermissionsList::model() const {
    return m_proxyModel;
}

QString PermissionsList::groupFilter() const {
    return m_groupFilter;
}

void PermissionsList::initializePermissions(const QList<Permission> &permissions) {
    if (m_sourceModel) {
        m_sourceModel->setPermissions(permissions);
    }
}

void PermissionsList::setGroupFilter(const QString& group) {
    if (m_groupFilter != group) {
        m_groupFilter = group;
        emit groupFilterChanged();
        m_proxyModel->setFilterFixedString(group); // Use proxy's filter
    }
}

void PermissionsList::movePermissionTo(PermissionsList* target, int permIndex) {
    if (!target || !m_sourceModel || !target->m_sourceModel) return;
    QModelIndex proxyIdx = m_proxyModel->index(permIndex, 0);
    int srcRow = m_proxyModel->mapToSource(proxyIdx).row();
    if (srcRow < 0 || srcRow >= m_sourceModel->m_permissions.size()) return;

    Permission perm = m_sourceModel->m_permissions.at(srcRow);

    m_sourceModel->removePermissionAt(srcRow);
    target->addPermission(perm);
}

void PermissionsList::addPermission(const Permission& permission) {
    if (!m_sourceModel) return;
    m_sourceModel->addPermission(permission);
}
