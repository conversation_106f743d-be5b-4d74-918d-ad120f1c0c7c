#include <QDebug>

#include "PrintfileItemModel.h"

PrintfileItemModel::PrintfileItemModel(QObject *parent)
    : QAbstractListModel(parent)
{
}

int PrintfileItemModel::rowCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent)
    return m_items.count();
}

QVariant PrintfileItemModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() >= m_items.count()) {
        return QVariant();
    }

    auto item = m_items.at(index.row()).toStrongRef();
    if (!item) {
        return QVariant();
    }

    switch (role) {
    case ItemIdRole:
        return item->itemId();
    case NameRole:
        return item->name();
    case QmlPathRole:
        return item->qmlPath();
    case XRole:
        return item->x();
    case YRole:
        return item->y();
    case WidthRole:
        return item->width();
    case HeightRole:
        return item->height();
    case RotationRole:
        return item->rotation();
    case ScaleRole:
        return item->scale();
    case PropertiesRole:
        return item->properties();
    case VisibleRole:
        return item->visible();
    case ZOrderRole:
        return item->zOrder();
    case ItemObjectRole:
        return QVariant::fromValue(item);
    default:
        return QVariant();
    }
}

bool PrintfileItemModel::setData(const QModelIndex &index, const QVariant &value, int role)
{
    if (!index.isValid() || index.row() >= m_items.count()) {
        return false;
    }

    auto item = m_items.at(index.row()).toStrongRef();
    if (!item) {
        return false;
    }

    switch (role) {
    case NameRole:
        item->setName(value.toString());
        return true;
    case QmlPathRole:
        item->setQmlPath(value.toString());
        return true;
    case XRole:
        item->setX(value.toReal());
        return true;
    case YRole:
        item->setY(value.toReal());
        return true;
    case WidthRole:
        item->setWidth(value.toReal());
        return true;
    case HeightRole:
        item->setHeight(value.toReal());
        return true;
    case RotationRole:
        item->setRotation(value.toReal());
        return true;
    case ScaleRole:
        item->setScale(value.toReal());
        return true;
    case PropertiesRole:
        item->setProperties(value.toMap());
        return true;
    case VisibleRole:
        item->setVisible(value.toBool());
        return true;
    case ZOrderRole:
        item->setZOrder(value.toInt());
        return true;
    default:
        return false;
    }
}

Qt::ItemFlags PrintfileItemModel::flags(const QModelIndex &index) const
{
    if (!index.isValid()) {
        return Qt::NoItemFlags;
    }
    return Qt::ItemIsEnabled | Qt::ItemIsSelectable | Qt::ItemIsEditable;
}

QHash<int, QByteArray> PrintfileItemModel::roleNames() const
{
    QHash<int, QByteArray> roles;
    roles[ItemIdRole] = "itemId";
    roles[NameRole] = "name";
    roles[QmlPathRole] = "qmlPath";
    roles[XRole] = "x";
    roles[YRole] = "y";
    roles[WidthRole] = "width";
    roles[HeightRole] = "height";
    roles[RotationRole] = "rotation";
    roles[ScaleRole] = "scale";
    roles[PropertiesRole] = "properties";
    roles[VisibleRole] = "visible";
    roles[ZOrderRole] = "zOrder";
    roles[ItemObjectRole] = "itemObject";
    return roles;
}

void PrintfileItemModel::setItems(const QList<QWeakPointer<PrintfileItem>> &items)
{
    beginResetModel();

    // Disconnect old items
    for (const auto &item : std::as_const(m_items)) {
        auto itemPtr = item.toStrongRef();

        if (itemPtr) {
            disconnect(itemPtr.data(), nullptr, this, nullptr);
        }
    }

    m_items = items;

    // Connect new items
    for (const auto& item : std::as_const(m_items)) {
        updateItemConnections(item);
    }

    endResetModel();
}

void PrintfileItemModel::addItem(QWeakPointer<PrintfileItem> item)
{
    auto itemRef = item.toStrongRef();

    if (!itemRef || m_items.contains(item)) {
        return;
    }

    beginInsertRows(QModelIndex(), m_items.count(), m_items.count());
    m_items.append(item);
    updateItemConnections(item);
    endInsertRows();
}

void PrintfileItemModel::removeItem(const QString &itemId)
{
    for (int i = 0; i < m_items.count(); ++i) {
        auto itemPtr = m_items[i].toStrongRef();

        if (itemPtr && itemPtr->itemId() == itemId) {
            beginRemoveRows(QModelIndex(), i, i);
            disconnect(itemPtr.data(), nullptr, this, nullptr);
            m_items.removeAt(i);
            endRemoveRows();
            break;
        }
    }
}

QWeakPointer<PrintfileItem> PrintfileItemModel::itemAt(int index) const
{
    return m_items.value(index);
}

QWeakPointer<PrintfileItem> PrintfileItemModel::getItem(const QString &itemId) const
{
    for (auto& item : m_items) {
        auto itemPtr = item.toStrongRef();

        if (itemPtr && itemPtr->itemId() == itemId) {
            return item;
        }
    }
    return {};
}

void PrintfileItemModel::clear()
{
    if (m_items.isEmpty()) {
        return;
    }

    beginResetModel();

    // Disconnect all items
    for (auto& item : std::as_const(m_items)) {
        auto itemRef = item.toStrongRef();

        if (itemRef) {
            disconnect(itemRef.data(), nullptr, this, nullptr);
        }
    }

    m_items.clear();
    endResetModel();
}

int PrintfileItemModel::findItemIndex(const QString &itemId) const
{
    for (int i = 0; i < m_items.count(); ++i) {
        auto itemRef = m_items[i].toStrongRef();

        if (itemRef && itemRef->itemId() == itemId) {
            return i;
        }
    }
    return -1;
}

void PrintfileItemModel::moveItem(int fromIndex, int toIndex)
{
    if (fromIndex < 0 || fromIndex >= m_items.count() ||
        toIndex < 0 || toIndex >= m_items.count() ||
        fromIndex == toIndex) {
        return;
    }

    beginMoveRows(QModelIndex(), fromIndex, fromIndex, QModelIndex(),
                  toIndex > fromIndex ? toIndex + 1 : toIndex);
    m_items.move(fromIndex, toIndex);
    endMoveRows();
}

void PrintfileItemModel::onItemChanged(QWeakPointer<PrintfileItem> item)
{
    auto strongItem = item.toStrongRef();
    if (!strongItem) return;

    int index = m_items.indexOf(item);
    if (index >= 0) {
        QModelIndex modelIndex = this->index(index);
        emit dataChanged(modelIndex, modelIndex);
    }
}

void PrintfileItemModel::updateItemConnections(QWeakPointer<PrintfileItem> item)
{
    auto itemPtr = item.toStrongRef();
    if (itemPtr) {
        connect(itemPtr.data(), &PrintfileItem::itemChanged, this,
                [this, item]() { onItemChanged(item); });
    }
}
