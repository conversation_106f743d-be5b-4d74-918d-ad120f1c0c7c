#include "ServiceStatusModel.h"
#include <QVariant>

ServiceStatusModel::ServiceStatusModel(QObject *parent)
    : QAbstractListModel(parent)
{
}

int ServiceStatusModel::rowCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent)
    return m_statuses.size();
}

QVariant ServiceStatusModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() < 0 || index.row() >= m_statuses.size()) {
        return QVariant();
    }

    ServiceStatus *status = m_statuses.at(index.row());
    if (!status) {
        return QVariant();
    }

    switch (role) {
    case NameRole:
        return status->name();
    case CaptionRole:
        return status->caption();
    case ValueRole:
        return status->value();
    case UnitsRole:
        return status->units();
    case FormattedValueRole:
        return status->formattedValue();
    case LevelRole:
        return static_cast<int>(status->level());
    case LevelTextRole:
        return status->levelText();
    case StatusObjectRole:
        return QVariant::fromValue(status);
    default:
        return QVariant();
    }
}

QHash<int, QByteArray> ServiceStatusModel::roleNames() const
{
    QHash<int, QByteArray> roles;
    roles[NameRole] = "name";
    roles[CaptionRole] = "caption";
    roles[ValueRole] = "value";
    roles[UnitsRole] = "units";
    roles[FormattedValueRole] = "formattedValue";
    roles[LevelRole] = "level";
    roles[LevelTextRole] = "levelText";
    roles[StatusObjectRole] = "statusObject";
    return roles;
}

void ServiceStatusModel::setStatuses(const QList<ServiceStatus*> &statuses)
{
    beginResetModel();

    // Disconnect old statuses
    for (ServiceStatus *status : std::as_const(m_statuses)) {
        if (status) {
            disconnect(status, nullptr, this, nullptr);
        }
    }

    m_statuses = statuses;

    // Connect new statuses
    for (ServiceStatus *status : std::as_const(m_statuses)) {
        if (status) {
            connect(status, &ServiceStatus::captionChanged, this, &ServiceStatusModel::onStatusChanged);
            connect(status, &ServiceStatus::valueChanged, this, &ServiceStatusModel::onStatusChanged);
            connect(status, &ServiceStatus::unitsChanged, this, &ServiceStatusModel::onStatusChanged);
            connect(status, &ServiceStatus::levelChanged, this, &ServiceStatusModel::onStatusChanged);
        }
    }

    endResetModel();
}

void ServiceStatusModel::addStatus(ServiceStatus *status)
{
    if (!status || m_statuses.contains(status)) {
        return;
    }

    beginInsertRows(QModelIndex(), m_statuses.size(), m_statuses.size());
    m_statuses.append(status);

    connect(status, &ServiceStatus::captionChanged, this, &ServiceStatusModel::onStatusChanged);
    connect(status, &ServiceStatus::valueChanged, this, &ServiceStatusModel::onStatusChanged);
    connect(status, &ServiceStatus::unitsChanged, this, &ServiceStatusModel::onStatusChanged);
    connect(status, &ServiceStatus::levelChanged, this, &ServiceStatusModel::onStatusChanged);

    endInsertRows();
}

void ServiceStatusModel::removeStatus(ServiceStatus *status)
{
    int index = m_statuses.indexOf(status);
    if (index >= 0) {
        beginRemoveRows(QModelIndex(), index, index);

        if (status) {
            disconnect(status, nullptr, this, nullptr);
        }

        m_statuses.removeAt(index);
        endRemoveRows();
    }
}

ServiceStatus* ServiceStatusModel::statusAt(int index) const
{
    if (index >= 0 && index < m_statuses.size()) {
        return m_statuses.at(index);
    }
    return nullptr;
}

void ServiceStatusModel::onStatusChanged()
{
    ServiceStatus *status = qobject_cast<ServiceStatus*>(sender());
    if (status) {
        int index = m_statuses.indexOf(status);
        if (index >= 0) {
            QModelIndex modelIndex = this->index(index);
            emit dataChanged(modelIndex, modelIndex);
        }
    }
}
