#ifndef TEMPLATEITEMMODEL_H
#define TEMPLATEITEMMODEL_H

#include <QAbstractListModel>

#include <printfile/PrintfileItem.h>

class PrintfileItemModel : public QAbstractListModel
{
    Q_OBJECT

public:
    enum ItemRoles {
        ItemIdRole = Qt::UserRole + 1,
        NameRole,
        QmlPathRole,
        XRole,
        YRole,
        WidthRole,
        HeightRole,
        RotationRole,
        ScaleRole,
        PropertiesRole,
        VisibleRole,
        ZOrderRole,
        ItemObjectRole
    };

    explicit PrintfileItemModel(QObject *parent = nullptr);

    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole) override;
    Qt::ItemFlags flags(const QModelIndex &index) const override;
    QHash<int, QByteArray> roleNames() const override;

    void setItems(const QList<QWeakPointer<PrintfileItem>> &items);
    void addItem(QWeakPointer<PrintfileItem> item);
    void removeItem(const QString &itemId);
    QWeakPointer<PrintfileItem> itemAt(int index) const;
    QWeakPointer<PrintfileItem> getItem(const QString &itemId) const;
    void clear();

    Q_INVOKABLE int findItemIndex(const QString &itemId) const;
    Q_INVOKABLE void moveItem(int fromIndex, int toIndex);

private slots:
    void onItemChanged(QWeakPointer<PrintfileItem> item);

private:
    QList<QWeakPointer<PrintfileItem>> m_items;
    void updateItemConnections(QWeakPointer<PrintfileItem> item);
};

#endif // TEMPLATEITEMMODEL_H
