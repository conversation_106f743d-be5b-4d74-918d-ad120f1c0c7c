#include "PrintfileModel.h"
#include <QDebug>

PrintfileModel::PrintfileModel(QObject *parent)
    : QAbstractListModel(parent)
{
}

int PrintfileModel::rowCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent)
    return m_printfiles.count();
}

QVariant PrintfileModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() >= m_printfiles.count()) {
        return QVariant();
    }

    auto printfilePtr = m_printfiles.at(index.row()).toStrongRef();
    if (!printfilePtr) {
        return QVariant();
    }

    switch (role) {
    case PrintfileIdRole:
        return printfilePtr->printfileId();
    case NameRole:
        return printfilePtr->name();
    case DescriptionRole:
        return printfilePtr->description();
    case BackgroundColorRole:
        return printfilePtr->backgroundColor();
    case ItemCountRole:
        return printfilePtr->itemCount();
    case PrintfileObjectRole:
        return QVariant::fromValue(printfilePtr);
    default:
        return QVariant();
    }
}

bool PrintfileModel::setData(const QModelIndex &index, const QVariant &value, int role)
{
    if (!index.isValid() || index.row() >= m_printfiles.count()) {
        return false;
    }

    auto printfileRef = m_printfiles.at(index.row()).toStrongRef();
    if (!printfileRef) {
        return false;
    }

    switch (role) {
    case NameRole:
        printfileRef->setName(value.toString());
        return true;
    case DescriptionRole:
        printfileRef->setDescription(value.toString());
        return true;
    case BackgroundColorRole:
        printfileRef->setBackgroundColor(value.toString());
        return true;
    default:
        return false;
    }
}

Qt::ItemFlags PrintfileModel::flags(const QModelIndex &index) const
{
    if (!index.isValid()) {
        return Qt::NoItemFlags;
    }
    return Qt::ItemIsEnabled | Qt::ItemIsSelectable | Qt::ItemIsEditable;
}

QHash<int, QByteArray> PrintfileModel::roleNames() const
{
    QHash<int, QByteArray> roles;
    roles[PrintfileIdRole] = "printfileId";
    roles[NameRole] = "name";
    roles[DescriptionRole] = "description";
    roles[BackgroundColorRole] = "backgroundColor";
    roles[ItemCountRole] = "itemCount";
    roles[PrintfileObjectRole] = "printfileObject";
    return roles;
}

void PrintfileModel::setPrintfiles(const QList<QWeakPointer<Printfile> > &printfiles)
{
    beginResetModel();

    // Disconnect old printfiles   
    for (auto &printfileObj : std::as_const(m_printfiles)) {
        auto printfileRef = printfileObj.toStrongRef();

        if (printfileRef) {
            disconnect(printfileRef.data(), nullptr, this, nullptr);
        }
    }

    m_printfiles = printfiles;

    // Connect new printfiles
    for (auto &printfileObj : std::as_const(m_printfiles)) {
        updatePrintfileConnections(printfileObj);
    }

    endResetModel();
}

void PrintfileModel::addPrintfile(QWeakPointer<Printfile> printfileObj)
{
    auto printfilePtr = printfileObj.toStrongRef();

    if (!printfilePtr || m_printfiles.contains(printfileObj)) {
        return;
    }

    beginInsertRows(QModelIndex(), m_printfiles.count(), m_printfiles.count());
    m_printfiles.append(printfileObj);
    updatePrintfileConnections(printfileObj);
    endInsertRows();
}

void PrintfileModel::removePrintfile(const QString &printfileId)
{
    for (int i = 0; i < m_printfiles.count(); ++i) {
        auto printfileRef = m_printfiles[i].toStrongRef();

        if (printfileRef && printfileRef->printfileId() == printfileId) {
            beginRemoveRows(QModelIndex(), i, i);
            disconnect(printfileRef.data(), nullptr, this, nullptr);
            m_printfiles.removeAt(i);
            endRemoveRows();
            break;
        }
    }
}

QWeakPointer<Printfile> PrintfileModel::printFileAt(int index) const
{
    return m_printfiles.value(index);
}

QWeakPointer<Printfile> PrintfileModel::getPrintfile(const QString &printfileId) const
{
    for (auto& printfileObj : m_printfiles) {
        auto printfileRef = printfileObj.toStrongRef();

        if (printfileRef && printfileRef->printfileId() == printfileId) {
            return printfileObj;
        }
    }

    return {};
}

void PrintfileModel::clear()
{
    if (m_printfiles.isEmpty()) {
        return;
    }

    beginResetModel();

    // Disconnect all printfiles
    for (auto &printfileObj : std::as_const(m_printfiles)) {
        auto printfileRef = printfileObj.toStrongRef();

        if (printfileRef) {
            disconnect(printfileRef.data(), nullptr, this, nullptr);
        }
    }

    m_printfiles.clear();
    endResetModel();
}

void PrintfileModel::onPrintfileChanged(QWeakPointer<Printfile> printfileObj)
{
    auto printfileRef = printfileObj.toStrongRef();

    if (!printfileRef) {
        return;
    }

    int index = m_printfiles.indexOf(printfileObj);
    if (index >= 0) {
        QModelIndex modelIndex = this->index(index);
        emit dataChanged(modelIndex, modelIndex);
    }
}

void PrintfileModel::updatePrintfileConnections(QWeakPointer<Printfile> printfileObj)
{
    auto printfilePtr = printfileObj.toStrongRef();

    if (printfilePtr) {
        connect(printfilePtr.data(), &Printfile::printfileChanged, this,
                [this, printfileObj](){ onPrintfileChanged(printfileObj); });
    }
}
