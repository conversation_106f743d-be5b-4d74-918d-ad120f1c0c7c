#ifndef SERVICEFUNCTIONMODEL_H
#define SERVICEFUNCTIONMODEL_H

#include <QAbstractListModel>
#include "printer/ServiceFunction.h"

class ServiceFunctionModel : public QAbstractListModel
{
    Q_OBJECT

public:
    enum FunctionRoles {
        FunctionIdRole = Qt::UserRole + 1,
        TitleR<PERSON>,
        DescriptionR<PERSON>,
        StateRole,
        IsActiveRole,
        CanStartRole,
        ProgressRole,
        StatusMessageRole,
        RemainingTimeRole,
        FunctionObjectRole
    };

    explicit ServiceFunctionModel(QObject *parent = nullptr);

    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    QHash<int, QByteArray> roleNames() const override;

    void setFunctions(const QList<ServiceFunction*> &functions);
    void addFunction(ServiceFunction *function);
    void removeFunction(ServiceFunction *function);
    ServiceFunction* functionAt(int index) const;

private slots:
    void onFunctionChanged();

private:
    QList<ServiceFunction*> m_functions;
};

#endif // SERVICEFUNCTIONMODEL_H
