#ifndef PRINTFILEMODEL_H
#define PRINTFILEMODEL_H

#include <QAbstractListModel>

#include <printfile/Printfile.h>

class PrintfileModel : public QAbstractListModel
{
    Q_OBJECT

public:
    enum PrintfileRoles {
        PrintfileIdRole = Qt::UserRole + 1,
        NameR<PERSON>,
        DescriptionRole,
        BackgroundColorRole,
        ItemCountRole,
        PrintfileObjectRole
    };

    explicit PrintfileModel(QObject *parent = nullptr);

    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole) override;
    Qt::ItemFlags flags(const QModelIndex &index) const override;
    QHash<int, QByteArray> roleNames() const override;

    void setPrintfiles(const QList<QWeakPointer<Printfile>> &printfiles);
    void addPrintfile(QWeakPointer<Printfile> printfileObj);
    void removePrintfile(const QString &printfileId);
    QWeakPointer<Printfile> printFileAt(int index) const;
    QWeakPointer<Printfile> getPrintfile(const QString &printfileId) const;
    void clear();

private slots:
    void onPrintfileChanged(QWeakPointer<Printfile> printfileObj);

private:
    QList<QWeakPointer<Printfile>> m_printfiles;
    void updatePrintfileConnections(QWeakPointer<Printfile> printfileObj);
};

#endif // PRINTFILEMODEL_H
