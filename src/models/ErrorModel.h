#ifndef ERRORMODEL_H
#define ERRORMODEL_H

#include <QAbstractListModel>
#include "../ErrorManager.h"

class ErrorModel : public QAbstractListModel {
    Q_OBJECT
    Q_PROPERTY(bool hasErrors READ hasErrors NOTIFY errorsChanged)
public:
    enum ErrorRoles {
        ErrorRole = Qt::UserRole + 1,
        CodeRole,
        TimestampRole
    };

    explicit ErrorModel(QObject* parent = nullptr);
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    QHash<int, QByteArray> roleNames() const override;

public:
    bool hasErrors() const { return rowCount() > 0; }

signals:
    void errorsChanged();

public slots:
    void onErrorsChanged();

private:
    ErrorManager* m_manager;
};

#endif // ERRORMODEL_H
