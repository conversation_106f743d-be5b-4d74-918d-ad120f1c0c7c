#ifndef PERMISSIONSMODEL_H
#define PERMISSIONSMODEL_H

#include <QAbstractListModel>
#include <QObject>
#include <QSortFilterProxyModel>
#include <QString>
#include <QList>

#include <Permission.h>
#include <UserManager.h>

class PermissionsDataModel : public QAbstractListModel {
    Q_OBJECT

    friend class PermissionsList;
public:
    enum PermissionRoles {
        NameRole = Qt::UserRole + 1,
        GroupRole
    };
    Q_ENUM(PermissionRoles)

    PermissionsDataModel(QObject* parent = nullptr);
    void setPermissions(const QList<Permission>& permissions);
    void removePermissionAt(int row);
    void addPermission(const Permission& permission);
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = NameRole) const override;
    virtual QHash<int, QByteArray> roleNames() const override;

private:
    QList<Permission> m_permissions;

public:
};

class PermissionsGroupFilterProxyModel : public QSortFilterProxyModel {
    Q_OBJECT

public:
    PermissionsGroupFilterProxyModel(QObject* parent = nullptr);
};

class PermissionsList : public QObject {
    Q_OBJECT
    Q_PROPERTY(QAbstractItemModel* model READ model CONSTANT)
    Q_PROPERTY(QString groupFilter READ groupFilter WRITE setGroupFilter NOTIFY groupFilterChanged)

public:
    PermissionsList(QObject* parent = nullptr);

    QAbstractItemModel* model() const;
    QString groupFilter() const;

    Q_INVOKABLE void setGroupFilter(const QString& group);
    Q_INVOKABLE void initializePermissions(const QList<Permission>& permissions);
    Q_INVOKABLE void addPermission(const Permission& permission);
    Q_INVOKABLE void movePermissionTo(PermissionsList* target, int permIndex);

signals:
    void groupFilterChanged();

private:
    PermissionsDataModel* m_sourceModel;
    PermissionsGroupFilterProxyModel* m_proxyModel;
    QString m_groupFilter;
};

#endif // PERMISSIONSMODEL_H
