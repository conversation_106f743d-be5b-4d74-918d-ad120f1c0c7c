#include "ErrorModel.h"

ErrorModel::ErrorModel(QObject* parent)
    : QAbstractListModel(parent), m_manager(ErrorManager::instance()) {
    connect(m_manager, &ErrorManager::errorsChanged, this, &ErrorModel::onErrorsChanged);
}

int ErrorModel::rowCount(const QModelIndex &parent) const {
    Q_UNUSED(parent);
    return m_manager->errors().size();
}

QVariant ErrorModel::data(const QModelIndex &index, int role) const {
    if (!index.isValid() || index.row() >= m_manager->errors().size())
        return QVariant();
    const ErrorEntry &entry = m_manager->errors().at(index.row());
    switch (role) {
    case ErrorRole:
        return entry.error;
    case CodeRole:
        return entry.code;
    case TimestampRole:
        return entry.timestamp.toString("yyyy-MM-dd HH:mm:ss");
    default:
        return QVariant();
    }
}

QHash<int, QByteArray> ErrorModel::roleNames() const {
    QHash<int, QByteArray> roles;
    roles[ErrorRole] = "error";
    roles[CodeRole] = "code";
    roles[TimestampRole] = "timestamp";
    return roles;
}

void ErrorModel::onErrorsChanged() {
    beginResetModel();
    endResetModel();
}
