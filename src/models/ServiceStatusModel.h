#ifndef SERVICESTATUSMODEL_H
#define SERVICESTATUSMODEL_H

#include <QAbstractListModel>
#include "printer/ServiceStatus.h"

class ServiceStatusModel : public QAbstractListModel
{
    Q_OBJECT

public:
    enum StatusRoles {
        NameRole = Qt::UserRole + 1,
        CaptionRole,
        ValueRole,
        UnitsRole,
        FormattedValueRole,
        LevelRole,
        LevelTextRole,
        StatusObjectRole
    };

    explicit ServiceStatusModel(QObject *parent = nullptr);

    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    QHash<int, QByteArray> roleNames() const override;

    void setStatuses(const QList<ServiceStatus*> &statuses);
    void addStatus(ServiceStatus *status);
    void removeStatus(ServiceStatus *status);
    ServiceStatus* statusAt(int index) const;

private slots:
    void onStatusChanged();

private:
    QList<ServiceStatus*> m_statuses;
};

#endif // SERVICESTATUSMODEL_H
