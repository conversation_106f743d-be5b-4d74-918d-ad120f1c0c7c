#include <QFile>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDebug>
#include <QDir>
#include <QCoreApplication>
#include <QQmlEngine>

#include "Constants.h"
#include "UserManager.h"
#include "UserInfo.h"
#include "Permission.h"

namespace {
    const QString kUsers = QStringLiteral("users");
    const QString kUserName = QStringLiteral("username");
    const QString kPassword = QStringLiteral("password");
    const QString kFirstName = QStringLiteral("firstname");
    const QString kLastName = QStringLiteral("lastname");
    const QString kRole = QStringLiteral("role");

    const QString kPermissions = QStringLiteral("permissions");
    const QString kPermissionName = QStringLiteral("name");
    const QString kPermissionGroup = QStringLiteral("group");
    const QString kDefaultUserPermissions = QStringLiteral("default_user_permissions");
}

UserManager::UserManager(QObject *parent)
    : QObject{parent}
{
    qRegisterMetaType<UserInfo>();
    qRegisterMetaType<QList<UserInfo>>();
    qRegisterMetaType<Permission>();
    qRegisterMetaType<QList<Permission>>();

    reload();
}

UserInfo UserManager::currentUser() const
{
    if (m_users.contains(m_username)) {
        return m_users[m_username];
    }
    return {};
}

QList<UserInfo> UserManager::users() const
{
    return m_users.values();
}

QList<Permission> UserManager::getAllowedPermissions(const QString &username) const
{
    if (!m_users.contains(username)) {
        return m_defaultPermissions;
    }

    QList<Permission> result;
    const auto& userPermissions = m_users[username].permissions();
    for (const QString& perm: userPermissions) {
        result.push_back(m_permissionsInfo[perm]);
    }

    return result;
}

QList<Permission> UserManager::getDisallowedPermissions(const QString &username) const
{
    auto perms = m_permissionsInfo.values();
    QSet<Permission> allPermissions = QSet<Permission>(perms.begin(), perms.end());

    perms = getAllowedPermissions(username);
    QSet<Permission> allowedPermissions = QSet<Permission>(perms.begin(), perms.end());
    allPermissions.subtract(allowedPermissions);

    QList<Permission> result;
    for (const auto& perm : std::as_const(allPermissions)) {
        result.push_back(perm);
    }

    return result;
}

void UserManager::setCurrentUser(const QString &username)
{
    if (username != m_username) {
        qDebug() << "[UserManager] Set current user:" << m_username;
        m_username = username;

        emit currentUserChanged();
        emit currentUserPermissionsChanged();
    }
}

bool UserManager::testPermission(const QString &permission) const
{
    return testPermission(permission, m_username);
}

bool UserManager::testPermission(const QString &permission, const QString &username) const
{
    if (!m_users.contains(username)) {
        qDebug() << "[UserManager] User" << username << "does not exist in users:" << m_users.keys();
        return false;
    }
    // TODO also test against role permissions
    return m_users[username].testPermission(permission);
}

bool UserManager::isLoggedIn() const {
    return !m_username.isEmpty();
}

bool UserManager::addPermission(const QString &permission)
{
    return addPermission(permission, m_username);
}

bool UserManager::addPermission(const QString &permission, const QString &username)
{
    if (!m_users.contains(username)) {
        return false;
    }
    if (!m_permissionsInfo.contains(permission)) {
        qWarning() << "Permission does not exist:" << permission;
        return false;
    }
    auto& user = m_users[username];
    if (!user.testPermission(permission)) {
        user.addPermission(permission);
        if (username == m_username) {
            emit currentUserPermissionsChanged();
        }
        return true;
    }

    return false;
}

bool UserManager::removePermission(const QString &permission)
{
    return removePermission(permission, m_username);
}

bool UserManager::removePermission(const QString &permission, const QString &username)
{
    if (!m_users.contains(username)) {
        return false;
    }
    if (!m_permissionsInfo.contains(permission)) {
        qWarning() << "Permission does not exist:" << permission;
        return false;
    }
    auto& user = m_users[username];
    if (user.testPermission(permission)) {
        user.removePermission(permission);
        if (username == m_username) {
            emit currentUserPermissionsChanged();
        }
        return true;
    }

    return false;
}

void UserManager::reload()
{
    initializePermissionsInfo();
    loadUsers();
}

void UserManager::initializePermissionsInfo()
{
    auto permissionsFilePath = QDir(QCoreApplication::applicationDirPath()).filePath(Constants::PERMISSIONS_FILE);

    QFile file(permissionsFilePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Could not open permissions file:" << permissionsFilePath;
        return;
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    if (error.error != QJsonParseError::NoError) {
        qWarning() << "JSON parse error in permissions file:" << error.errorString();
        return;
    }

    if (!doc.isObject()) {
        qWarning() << "Permissions file does not contain a JSON object";
        return;
    }

    QJsonObject root = doc.object();
    const QJsonArray jsonPermissions = root[kPermissions].toArray();

    m_permissionsInfo.clear();

    for (const auto& jsonVal: jsonPermissions) {
        auto jsonObject = jsonVal.toObject();
        auto permissionName = jsonObject[kPermissionName].toString();
        auto permissionGroup = jsonObject[kPermissionGroup].toString();

        Permission permission(permissionName, permissionGroup);
        m_permissionsInfo[permissionName] = permission;
    }

    qDebug() << "Loaded" << m_permissionsInfo.size() << "permissions from" << permissionsFilePath;
}

void UserManager::loadUsers() {
    m_users.clear();

    auto usersFilePath = QDir(QCoreApplication::applicationDirPath()).filePath(Constants::USERS_FILE);

    QFile file(usersFilePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Could not open users file:" << usersFilePath;
        return;
    }
    QByteArray data = file.readAll();
    file.close();
    QJsonDocument doc = QJsonDocument::fromJson(data);
    if (!doc.isObject()) {
        return;
    }
    QJsonObject root = doc.object();

    // Load default_user_permissions
    m_defaultPermissions.clear();
    const QJsonArray defaultPerms = root[kDefaultUserPermissions].toArray();
    for (const auto& permVal : defaultPerms) {
        QString permName = permVal.toString();
        if (m_permissionsInfo.contains(permName)) {
            m_defaultPermissions.append(m_permissionsInfo[permName]);
        }
    }

    const QJsonArray users = root[kUsers].toArray();
    for (const QJsonValue &u : users) {
        QJsonObject userObj = u.toObject();
        QString username = userObj[kUserName].toString();
        QString password = userObj[kPassword].toString();
        QString firstName = userObj[kFirstName].toString();
        QString lastName = userObj[kLastName].toString();
        QString role = userObj[kRole].toString();

        UserInfo user(username, password, firstName, lastName, role);
        const auto permArray = userObj[kPermissions].toArray();
        for (const auto &permVal : permArray) {
            user.addPermission(permVal.toString());
        }
        user.setValid(true);
        m_users[username] = user;
    }
    qDebug() << "Loaded" << m_users.size() << "users from" << usersFilePath;
}

bool UserManager::saveUsers()
{
    auto usersFilePath = QDir(QCoreApplication::applicationDirPath()).filePath(Constants::USERS_FILE);

    // Load existing file
    QFile file(usersFilePath);
    QJsonObject root;
    if (file.open(QIODevice::ReadOnly)) {
        QByteArray data = file.readAll();
        file.close();
        QJsonDocument doc = QJsonDocument::fromJson(data);
        if (doc.isObject()) {
            root = doc.object();
        }
    } // If file can't be read, just continue with empty root

    // Update users section
    QJsonArray usersArray;
    for (auto it = m_users.begin(); it != m_users.end(); ++it) {
        const UserInfo& user = it.value();
        QJsonObject userObj;
        userObj[kUserName] = user.username();
        userObj[kPassword] = user.password();
        userObj[kFirstName] = user.firstName();
        userObj[kLastName] = user.lastName();
        userObj[kRole] = user.role();
        // Write permissions
        QJsonArray permArray;
        const auto permissions = user.permissions();
        for (const QString& perm : permissions) {
            permArray.append(perm);
        }
        userObj[kPermissions] = permArray;
        usersArray.append(userObj);
    }
    root[kUsers] = usersArray;

    // Optionally update default permissions if needed
    QJsonArray defaultPermsArray;
    for (const auto& perm : std::as_const(m_defaultPermissions)) {
        defaultPermsArray.append(perm.name());
    }
    root[kDefaultUserPermissions] = defaultPermsArray;

    // Save back to file
    if (!file.open(QIODevice::WriteOnly | QIODevice::Truncate)) {
        qWarning() << "Could not open users file for writing: " << usersFilePath;
        return false;
    }
    QJsonDocument newDoc(root);
    file.write(newDoc.toJson());
    file.close();
    return true;
}

UserInfo UserManager::createUserInfo(const QString &username, const QString &password) const
{
    UserInfo newUserInfo(username, password);

    for (const auto& defaultPermission: m_defaultPermissions) {
        newUserInfo.addPermission(defaultPermission.name());
    }

    return newUserInfo;
}

bool UserManager::addUser(const UserInfo &userInfo)
{
    QString username = userInfo.username();

    // Check if user already exists
    if (m_users.contains(username)) {
        qWarning() << "User already exists:" << username;
        return false;
    }

    // Validate input
    // TODO Add passwords support
    if (username.isEmpty()) {
        qWarning() << "Username or password cannot be empty";
        return false;
    }

    // Add the user to the map
    m_users[username] = userInfo;
    m_users[username].setValid(true);
    emit usersChanged();
    return true;
}

bool UserManager::userExists(const QString &username) const
{
    return m_users.contains(username);
}

bool UserManager::deleteUser(const QString &username)
{
    if (!m_users.contains(username)) {
        qWarning() << "User does not exist:" << username;
        return false;
    }

    // Don't allow deleting the current user
    if (username == m_username) {
        qWarning() << "Cannot delete current user:" << username;
        return false;
    }

    // Delete user
    m_users.remove(username);

    qDebug() << "Deleted user:" << username;

    // Emit signal to notify QML
    emit usersChanged();

    return true;
}

QStringList UserManager::currentUserPermissions() const
{
    // TODO Also add role permissions
    return m_users[m_username].permissions();
}

QList<Permission> UserManager::getDefaultPermissions() const
{
    return m_defaultPermissions;
}

QStringList UserManager::getAllPermissionGroups() const
{
    QSet<QString> groups;
    for (const auto &permission : m_permissionsInfo) {
        groups.insert(permission.group());
    }
    return QStringList(groups.begin(), groups.end());
}
