#include "TimeHandler.h"

TimeHandler::TimeHandler(QObject *parent) : QObject(parent) {
    // Initialize the timer to update every 60 seconds (1 minute)
    connect(&m_timer, &QTimer::timeout, this, &TimeHandler::updateDateTime);
    m_timer.start(60000);  // 60000 ms = 1 minute

    // Set initial values
    updateDateTime();
}

QString TimeHandler::currentTime() const {
    return m_currentTime;
}

QString TimeHandler::currentDate() const {
    return m_currentDate;
}

void TimeHandler::updateDateTime() {
    // Update time in "hh:mm AP" format (e.g., "12:05 PM")
    m_currentTime = QTime::currentTime().toString("hh:mm AP");

    // Update date in "ddd, MMM d, yyyy" format (e.g., "Sun, Nov 24, 2024")
    m_currentDate = QDate::currentDate().toString("ddd, MMM d, yyyy");
    emit timeChanged();
    emit dateChanged();
}
