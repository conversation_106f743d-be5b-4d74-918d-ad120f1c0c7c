#ifndef USERINFO_H
#define USERINFO_H

#include <QObject>
#include <QString>
#include <QSet>

class UserInfo
{
    Q_GADGET

    Q_PROPERTY(QString username READ username)
    Q_PROPERTY(QString password READ password)
    Q_PROPERTY(QString firstName READ firstName WRITE setFirstName)
    Q_PROPERTY(QString lastName READ lastName WRITE setLastName)
    Q_PROPERTY(QString role READ role WRITE setRole)
    Q_PROPERTY(QStringList permissions READ permissions)
    Q_PROPERTY(bool valid READ valid WRITE setValid)

public:
    UserInfo();
    UserInfo(const QString& username, const QString& password, const QString& firstName = "", const QString& lastName = "", const QString& role = "");

    QString username() const;
    QString password() const;
    QString firstName() const;
    QString lastName() const;
    QString role() const;
    QStringList permissions() const;

    void setUsername(const QString &username);
    void setPassword(const QString &newPassword);
    void setFirstName(const QString &firstName);
    void setLastName(const QString &lastName);
    void setRole(const QString &role);
    bool testPermission(const QString& permission) const;
    Q_INVOKABLE void addPermission(const QString& permission);
    Q_INVOKABLE void removePermission(const QString& permission);
    Q_INVOKABLE void resetPermissions(const QStringList& permissions);

    bool valid() const;
    void setValid(bool newValid);

private:
    QString m_username;
    QString m_password;
    QString m_firstName;
    QString m_lastName;
    QString m_role;
    bool m_valid = false;
    QSet<QString> m_permissions;
};

Q_DECLARE_METATYPE(UserInfo)

#endif // USERINFO_H
