#include "Permission.h"

Permission::Permission()
{

}

Permission::Permission(const QString name, const QString group)
    : m_name(name)
    , m_group(group)
{}

QString Permission::name() const
{
    return m_name;
}

void Permission::setName(const QString &newName)
{
    if (m_name == newName)
        return;
    m_name = newName;
}

QString Permission::group() const
{
    return m_group;
}

void Permission::setGroup(const QString &newGroup)
{
    if (m_group == newGroup)
        return;
    m_group = newGroup;
}

bool operator==(const Permission& p1, const Permission& p2) {
    return p1.m_name == p2.m_name && p1.m_group == p2.m_group;
}
