#include "YamlConfigLoader.h"
#ifndef Q_OS_WIN
#include <yaml-cpp/yaml.h>
#endif
#include <QFile>
#include <QDebug>

YamlConfigLoader::YamlConfigLoader(QObject *parent)
    : QObject(parent) {}

bool YamlConfigLoader::load(const QString &filePath) {
#ifndef Q_OS_WIN
    try {
        YAML::Node config = YAML::LoadFile(filePath.toStdString());
        // Convert YAML nodes to QVariant for QML/C++ access
        if (config["users"]) {
            QList<QVariant> usersList;
            for (const auto &user : config["users"]) {
                QVariantMap userMap;
                for (const auto &it : user) {
                    userMap[QString::fromStdString(it.first.as<std::string>())] = QString::fromStdString(it.second.as<std::string>());
                }
                // Handle permissions as a list
                if (user["permissions"]) {
                    QList<QVariant> perms;
                    for (const auto &perm : user["permissions"]) {
                        perms.append(QString::fromStdString(perm.as<std::string>()));
                    }
                    userMap["permissions"] = perms;
                }
                usersList.append(userMap);
            }
            m_users = usersList;
        }
        if (config["printers"]) {
            QVariantMap printersMap;
            if (config["printers"]["default"]) {
                printersMap["default"] = QString::fromStdString(config["printers"]["default"].as<std::string>());
            }
            if (config["printers"]["list"]) {
                QList<QVariant> printerList;
                for (const auto &printer : config["printers"]["list"]) {
                    printerList.append(QString::fromStdString(printer.as<std::string>()));
                }
                printersMap["list"] = printerList;
            }
            m_printers = printersMap;
        }
        if (config["settings"]) {
            QVariantMap settingsMap;
            for (const auto &it : config["settings"]) {
                settingsMap[QString::fromStdString(it.first.as<std::string>())] = QString::fromStdString(it.second.as<std::string>());
            }
            m_settings = settingsMap;
        }
        return true;
    } catch (const std::exception &e) {
        qWarning() << "Failed to load YAML config:" << e.what();
        return false;
    }
#else
    // TODO Make yaml-cpp library available for Windows build
    Q_UNUSED(filePath)
    return false;
#endif
}

QVariant YamlConfigLoader::users() const {
    return m_users;
}

QVariant YamlConfigLoader::printers() const {
    return m_printers;
}

QVariant YamlConfigLoader::settings() const {
    return m_settings;
}
