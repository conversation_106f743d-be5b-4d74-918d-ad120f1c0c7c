#include "BackendSingletons.h"
#include <QQmlEngine>
#include "UserManager.h"
#include "ConfigManager.h"
#include "printfile/PrintfileManager.h"
#ifdef DESKTOP_BUILD
#include "../tests/printer/PrinterManagerTest.h"
#else
#include <printer/PrinterManager.h>
#endif

static QObject *config_manager_provider(QQmlEngine *, QJSEngine *) {
    static ConfigManager *manager = nullptr;

    if (manager == nullptr) {
        manager = new ConfigManager();
    }

    return manager;
}

static QObject *user_manager_provider(QQmlEngine *, QJSEngine *) {
    static UserManager *manager = nullptr;

    if (manager == nullptr) {
        manager = new UserManager();
    }

    return manager;
}

static QObject *printer_manager_provider(QQmlEngine *, QJSEngine *) {
#ifdef DESKTOP_BUILD
    static PrinterManagerTest *manager = nullptr;
    if (manager == nullptr) {
        ConfigManager* config = qobject_cast<ConfigManager*>(config_manager_provider(nullptr, nullptr));
        manager = new PrinterManagerTest(config);
    }
#else
    static PrinterManager *manager = nullptr;
    if (manager == nullptr) {
        ConfigManager* config = qobject_cast<ConfigManager*>(config_manager_provider(nullptr, nullptr));
        manager = new PrinterManager(config);
    }
#endif
    return manager;
}

static QObject *printfile_manager_provider(QQmlEngine *, QJSEngine *) {
    static PrintfileManager *manager = nullptr;
    if (manager == nullptr) {
        ConfigManager* config = qobject_cast<ConfigManager*>(config_manager_provider(nullptr, nullptr));
        manager = new PrintfileManager(config);
    }
    return manager;
}

void registerBackendSingletons() {
    qmlRegisterSingletonType<ConfigManager>("Backend.ConfigManager", 1, 0, "ConfigManager", config_manager_provider);
    qmlRegisterSingletonType<UserManager>("Backend.UserManager", 1, 0, "UserManager", user_manager_provider);
    qmlRegisterSingletonType<PrintfileManager>("Backend.PrintfileManager", 1, 0, "PrintfileManager", printfile_manager_provider);
#ifdef DESKTOP_BUILD
    qmlRegisterSingletonType<PrinterManagerTest>("Backend.PrinterManager", 1, 0, "PrinterManager", printer_manager_provider);
#else
    qmlRegisterSingletonType<PrinterManager>("Backend.PrinterManager", 1, 0, "PrinterManager", printer_manager_provider);
#endif
}
