#include "PrintServiceManager.h"
#include <QRandomGenerator>
#include <QDebug>

PrintServiceManager::PrintServiceManager(QObject *parent)
    : ServiceManager(parent)
    , m_statusUpdateTimer(new QTimer(this))
    , m_simulationRunning(false)
    , m_pressureStatus(nullptr)
    , m_fillLevelStatus(nullptr)
    , m_pumpSpeedStatus(nullptr)
    , m_viscosityStatus(nullptr)
    , m_filterTimeStatus(nullptr)
    , m_temperatureStatus(nullptr)
{
    connect(m_statusUpdateTimer, &QTimer::timeout, this, &PrintServiceManager::simulateStatusUpdates);
    m_statusUpdateTimer->setInterval(2000); // Update every 2 seconds
}

void PrintServiceManager::startStatusSimulation()
{
    if (!m_simulationRunning) {
        m_simulationRunning = true;
        m_statusUpdateTimer->start();
        qDebug() << "Status simulation started";
    }
}

void PrintServiceManager::stopStatusSimulation()
{
    if (m_simulationRunning) {
        m_simulationRunning = false;
        m_statusUpdateTimer->stop();
        qDebug() << "Status simulation stopped";
    }
}

bool PrintServiceManager::isStatusSimulationRunning() const
{
    return m_simulationRunning;
}

void PrintServiceManager::updatePrinterStatuses()
{
    // This method can be called to manually update statuses from real hardware
    // For now, we'll just trigger a single update
    simulateStatusUpdates();
}

void PrintServiceManager::initializeDefaultFunctions()
{
    // Create and register printer service functions
    registerFunction(new JetFunction(this));
    registerFunction(new AddSolventFunction(this));
    registerFunction(new AddInkFunction(this));
    registerFunction(new CleanNozzleFunction(this));

    qDebug() << "Initialized" << m_functions.size() << "default printer functions";
}

void PrintServiceManager::initializeDefaultStatuses()
{
    createPrinterStatuses();
    setupStatusRanges();

    qDebug() << "Initialized" << m_statuses.size() << "default printer statuses";
}

void PrintServiceManager::createPrinterStatuses()
{
    // Create pressure status
    m_pressureStatus = new ServiceStatus("pressure", "Pressure (Bars)", this);
    m_pressureStatus->setUnits("Bars");
    m_pressureStatus->setPrecision(0);
    registerStatus(m_pressureStatus);

    // Create fill level status
    m_fillLevelStatus = new ServiceStatus("fillLevel", "Fill Level", this);
    m_fillLevelStatus->setUnits("%");
    m_fillLevelStatus->setPrecision(0);
    registerStatus(m_fillLevelStatus);

    // Create pump speed status
    m_pumpSpeedStatus = new ServiceStatus("pumpSpeed", "Pump Speed (RPM)", this);
    m_pumpSpeedStatus->setUnits("RPM");
    m_pumpSpeedStatus->setPrecision(1);
    registerStatus(m_pumpSpeedStatus);

    // Create viscosity status
    m_viscosityStatus = new ServiceStatus("viscosity", "Viscosity (+/-)", this);
    m_viscosityStatus->setUnits("");
    m_viscosityStatus->setPrecision(0);
    registerStatus(m_viscosityStatus);

    // Create filter time status
    m_filterTimeStatus = new ServiceStatus("filterTime", "Filter Time", this);
    m_filterTimeStatus->setUnits("hrs");
    m_filterTimeStatus->setPrecision(0);
    registerStatus(m_filterTimeStatus);

    // Create board temperature status
    m_temperatureStatus = new ServiceStatus("temperature", "Board Temperature (°C)", this);
    m_temperatureStatus->setUnits("°C");
    m_temperatureStatus->setPrecision(0);
    registerStatus(m_temperatureStatus);
}

void PrintServiceManager::setupStatusRanges()
{
    // Setup pressure ranges (normal: 2800-3600, warning: >3600, critical: >4000)
    if (m_pressureStatus) {
        m_pressureStatus->setRange(2800, 3600);
        m_pressureStatus->setWarningThreshold(3600);
        m_pressureStatus->setCriticalThreshold(4000);
        m_pressureStatus->setValue(3200); // Initial value
    }

    // Setup fill level ranges (normal: 20-100%, warning: <20%, critical: <10%)
    if (m_fillLevelStatus) {
        m_fillLevelStatus->setRange(20, 100);
        m_fillLevelStatus->setWarningThreshold(20); // Warning when low
        m_fillLevelStatus->setCriticalThreshold(10); // Critical when very low
        m_fillLevelStatus->setValue(65); // Initial value
    }

    // Setup pump speed ranges (normal: 80-120 RPM, warning: outside this range)
    if (m_pumpSpeedStatus) {
        m_pumpSpeedStatus->setRange(80, 120);
        m_pumpSpeedStatus->setWarningThreshold(120);
        m_pumpSpeedStatus->setCriticalThreshold(140);
        m_pumpSpeedStatus->setValue(100.3); // Initial value
    }

    // Setup viscosity ranges (normal: -200 to +200, warning: outside)
    if (m_viscosityStatus) {
        m_viscosityStatus->setRange(-200, 200);
        m_viscosityStatus->setWarningThreshold(200);
        m_viscosityStatus->setCriticalThreshold(300);
        m_viscosityStatus->setValue(500); // Initial value (in warning range)
    }

    // Setup filter time ranges (normal: 0-2000 hrs, warning: >2000, critical: >2400)
    if (m_filterTimeStatus) {
        m_filterTimeStatus->setRange(0, 2000);
        m_filterTimeStatus->setWarningThreshold(2000);
        m_filterTimeStatus->setCriticalThreshold(2400);
        m_filterTimeStatus->setValue(2400); // Initial value (critical)
    }

    // Setup temperature ranges (normal: 20-45°C, warning: >45°C, critical: >60°C)
    if (m_temperatureStatus) {
        m_temperatureStatus->setRange(20, 45);
        m_temperatureStatus->setWarningThreshold(45);
        m_temperatureStatus->setCriticalThreshold(60);
        m_temperatureStatus->setValue(38); // Initial value
    }
}

void PrintServiceManager::simulateStatusUpdates()
{
    QRandomGenerator *rng = QRandomGenerator::global();

    // Simulate pressure fluctuations
    if (m_pressureStatus) {
        double currentPressure = m_pressureStatus->value().toDouble();
        double delta = (rng->generateDouble() - 0.5) * 100; // +/- 50 bars variation
        double newPressure = qBound(2500.0, currentPressure + delta, 4200.0);
        m_pressureStatus->setValue(newPressure);
    }

    // Simulate fill level changes (slowly decreasing)
    if (m_fillLevelStatus) {
        double currentLevel = m_fillLevelStatus->value().toDouble();
        double decrease = rng->generateDouble() * 0.5; // 0-0.5% decrease per update
        double newLevel = qMax(0.0, currentLevel - decrease);
        m_fillLevelStatus->setValue(newLevel);
    }

    // Simulate pump speed variations
    if (m_pumpSpeedStatus) {
        double currentSpeed = m_pumpSpeedStatus->value().toDouble();
        double delta = (rng->generateDouble() - 0.5) * 10; // +/- 5 RPM variation
        double newSpeed = qBound(50.0, currentSpeed + delta, 150.0);
        m_pumpSpeedStatus->setValue(newSpeed);
    }

    // Simulate viscosity changes
    if (m_viscosityStatus) {
        double currentViscosity = m_viscosityStatus->value().toDouble();
        double delta = (rng->generateDouble() - 0.5) * 50; // +/- 25 variation
        double newViscosity = qBound(-500.0, currentViscosity + delta, 800.0);
        m_viscosityStatus->setValue(newViscosity);
    }

    // Simulate filter time increase
    if (m_filterTimeStatus) {
        double currentTime = m_filterTimeStatus->value().toDouble();
        double increase = rng->generateDouble() * 0.1; // Small increase per update
        double newTime = qMin(3000.0, currentTime + increase);
        m_filterTimeStatus->setValue(newTime);
    }

    // Simulate temperature variations
    if (m_temperatureStatus) {
        double currentTemp = m_temperatureStatus->value().toDouble();
        double delta = (rng->generateDouble() - 0.5) * 4; // +/- 2°C variation
        double newTemp = qBound(15.0, currentTemp + delta, 70.0);
        m_temperatureStatus->setValue(newTemp);
    }
}
