#ifndef ERRORMANAGER_H
#define ERRORMANAGER_H

#include <QObject>
#include <QVector>
#include <QString>
#include <QDateTime>
#include <QTimer>

struct ErrorEntry {
    QString error;
    QString code;
    QDateTime timestamp;
};

class ErrorManager : public QObject {
    Q_OBJECT
public:
    static ErrorManager* instance();

    const QVector<ErrorEntry>& errors() const;
    void addError(const QString& error, const QString& code);
    Q_INVOKABLE void clearErrors();
    Q_INVOKABLE void loadPendingErrors();
    Q_INVOKABLE bool hasPendingErrors() const;

    int pendingErrorCount() const;
    Q_PROPERTY(int pendingErrorCount READ pendingErrorCount NOTIFY pendingErrorsChanged);

    // Load errors from storage/errorlog.json
    // Q_INVOKABLE void reloadErrorsFromFile();
    // Optionally, save errors to file
    // Q_INVOKABLE void saveErrorsToFile();

    bool hasErrors() const;
    Q_PROPERTY(bool hasErrors READ hasErrors NOTIFY errorsChanged)

signals:
    void errorsChanged();
    void pendingErrorsChanged();

private:
    explicit ErrorManager(QObject* parent = nullptr);
    void loadErrorsFromFile();
    void startErrorSimulation();
    void simulateRandomError();
    QString randomErrorMessage() const;
    QString randomErrorCode() const;
    QVector<ErrorEntry> m_errors;
    QVector<ErrorEntry> m_pendingErrors; // New: stores errors before refresh
    static ErrorManager* s_instance;
    QTimer* m_errorSimTimer;

};

#endif // ERRORMANAGER_H
