#ifndef TIMEHANDLER_H
#define TIMEHANDLER_H

#include <QObject>
#include <QTimer>
#include <QDateTime>
#include <QtDebug>

class TimeHandler : public QObject {
    Q_OBJECT
    Q_PROPERTY(QString currentTime READ currentTime NOTIFY timeChanged)
    Q_PROPERTY(QString currentDate READ currentDate NOTIFY dateChanged)

public:
    explicit TimeHandler(QObject *parent = nullptr);

    QString currentTime() const;
    QString currentDate() const;

signals:
    void timeChanged();
    void dateChanged();

private slots:
    void updateDateTime();

private:
    QTimer m_timer;
    QString m_currentTime;
    QString m_currentDate;
};

#endif // TIMEHANDLER_H
