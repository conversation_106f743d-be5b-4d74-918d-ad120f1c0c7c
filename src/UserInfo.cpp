#include "UserInfo.h"

UserInfo::UserInfo()
{
}

UserInfo::UserInfo(const QString& username, const QString &password, const QString &firstName, const QString &lastName, const QString& role)
    : m_username(username)
    , m_password(password)
    , m_firstName(firstName)
    , m_lastName(lastName)
    , m_role(role)
{
}

QString UserInfo::firstName() const
{
    return m_firstName;
}

QString UserInfo::lastName() const
{
    return m_lastName;
}

QString UserInfo::username() const
{
    return m_username;
}

QString UserInfo::role() const
{
    return m_role;
}

void UserInfo::setFirstName(const QString &firstName)
{
    m_firstName = firstName;
}

void UserInfo::setLastName(const QString &lastName)
{
    m_lastName = lastName;
}

void UserInfo::setUsername(const QString &username)
{
    m_username = username;
}

void UserInfo::setRole(const QString &role)
{
    m_role = role;
}

bool UserInfo::testPermission(const QString &permission) const
{
    return m_permissions.contains(permission);
}

void UserInfo::addPermission(const QString &permission)
{
    m_permissions.insert(permission);
}

void UserInfo::removePermission(const QString &permission)
{
    m_permissions.remove(permission);
}

void UserInfo::resetPermissions(const QStringList &permissions)
{
    m_permissions = QSet<QString>(permissions.begin(), permissions.end());
}

bool UserInfo::valid() const
{
    return m_valid;
}

void UserInfo::setValid(bool newValid)
{
    m_valid = newValid;
}

QString UserInfo::password() const
{
    return m_password;
}

void UserInfo::setPassword(const QString &newPassword)
{
    m_password = newPassword;
}

QStringList UserInfo::permissions() const
{
    return m_permissions.values();
}
