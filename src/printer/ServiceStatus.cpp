#include "ServiceStatus.h"
#include <QDebug>

ServiceStatus::ServiceStatus(const QString &name, QObject *parent)
    : QObject(parent)
    , m_name(name)
    , m_caption(name)
    , m_level(Unknown)
    , m_hasRange(false)
    , m_precision(1)
{
}

ServiceStatus::ServiceStatus(const QString &name, const QString &caption, QObject *parent)
    : QObject(parent)
    , m_name(name)
    , m_caption(caption)
    , m_level(Unknown)
    , m_hasRange(false)
    , m_precision(1)
{
}

QString ServiceStatus::formattedValue() const
{
    return formatValue(m_value);
}

QString ServiceStatus::levelText() const
{
    switch (m_level) {
    case Normal:
        return "Normal";
    case Warning:
        return "Warning";
    case Critical:
        return "Critical";
    case Error:
        return "Error";
    case Unknown:
    default:
        return "Unknown";
    }
}

void ServiceStatus::setCaption(const QString &caption)
{
    if (m_caption != caption) {
        m_caption = caption;
        emit captionChanged();
    }
}

void ServiceStatus::setUnits(const QString &units)
{
    if (m_units != units) {
        m_units = units;
        emit unitsChanged();
    }
}

void ServiceStatus::setRange(const QVariant &minValue, const QVariant &maxValue)
{
    bool changed = false;

    if (m_minValue != minValue) {
        m_minValue = minValue;
        changed = true;
    }

    if (m_maxValue != maxValue) {
        m_maxValue = maxValue;
        changed = true;
    }

    if (!m_hasRange) {
        m_hasRange = true;
        changed = true;
    }

    if (changed) {
        emit rangeChanged();
        updateLevel(); // Recalculate level with new range
    }
}

void ServiceStatus::setWarningThreshold(const QVariant &threshold)
{
    if (m_warningThreshold != threshold) {
        m_warningThreshold = threshold;
        emit rangeChanged();
        updateLevel(); // Recalculate level with new threshold
    }
}

void ServiceStatus::setCriticalThreshold(const QVariant &threshold)
{
    if (m_criticalThreshold != threshold) {
        m_criticalThreshold = threshold;
        emit rangeChanged();
        updateLevel(); // Recalculate level with new threshold
    }
}

void ServiceStatus::setPrecision(int precision)
{
    if (m_precision != precision) {
        m_precision = qMax(0, precision);
        emit precisionChanged();
        emit valueChanged(); // Formatted value may change
    }
}

void ServiceStatus::clearRange()
{
    bool changed = m_hasRange;

    m_hasRange = false;
    m_minValue = QVariant();
    m_maxValue = QVariant();
    m_warningThreshold = QVariant();
    m_criticalThreshold = QVariant();

    if (changed) {
        emit rangeChanged();
        updateLevel();
    }
}

void ServiceStatus::setValue(const QVariant &value)
{
    if (m_value != value) {
        StatusLevel oldLevel = m_level;
        m_value = value;
        emit valueChanged();

        updateLevel();

        // Emit threshold exceeded signal if level worsened
        if (m_level > oldLevel && m_level != Unknown) {
            emit thresholdExceeded(m_level, value);
        }
        // Emit threshold restored signal if level improved
        else if (m_level < oldLevel && m_level != Unknown) {
            emit thresholdRestored(m_level, value);
        }
    }
}

void ServiceStatus::updateLevel()
{
    StatusLevel newLevel = calculateLevel(m_value);
    if (m_level != newLevel) {
        m_level = newLevel;
        emit levelChanged();
    }
}

ServiceStatus::StatusLevel ServiceStatus::calculateLevel(const QVariant &value) const
{
    if (!value.isValid()) {
        return Unknown;
    }

    // Convert to double for numeric comparisons
    bool ok;
    double numValue = value.toDouble(&ok);
    if (!ok) {
        return Error; // Non-numeric values are considered errors unless specifically handled
    }

    // Check critical threshold first
    if (m_criticalThreshold.isValid()) {
        double criticalVal = m_criticalThreshold.toDouble(&ok);
        if (ok && numValue >= criticalVal) {
            return Critical;
        }
    }

    // Check warning threshold
    if (m_warningThreshold.isValid()) {
        double warningVal = m_warningThreshold.toDouble(&ok);
        if (ok && numValue >= warningVal) {
            return Warning;
        }
    }

    // Check if value is within normal range
    if (m_hasRange && m_minValue.isValid() && m_maxValue.isValid()) {
        double minVal = m_minValue.toDouble(&ok);
        if (!ok) return Normal;

        double maxVal = m_maxValue.toDouble(&ok);
        if (!ok) return Normal;

        if (numValue < minVal || numValue > maxVal) {
            return Warning; // Out of range is at least a warning
        }
    }

    return Normal;
}

QString ServiceStatus::formatValue(const QVariant &value) const
{
    if (!value.isValid()) {
        return "N/A";
    }

    bool ok;
    double numValue = value.toDouble(&ok);
    if (ok) {
        QString formatted = QString::number(numValue, 'f', m_precision);
        if (!m_units.isEmpty()) {
            formatted += " " + m_units;
        }
        return formatted;
    }

    // For non-numeric values, just return string representation
    QString str = value.toString();
    if (!m_units.isEmpty() && !str.isEmpty()) {
        str += " " + m_units;
    }
    return str;
}
