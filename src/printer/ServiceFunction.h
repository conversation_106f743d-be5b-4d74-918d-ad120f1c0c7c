#ifndef PRINTERSERVICEFUNCTION_H
#define PRINTERSERVICEFUNCTION_H

#include <QObject>
#include <QString>
#include <QVariant>
#include <QTimer>
#include <QSet>

class ServiceFunction : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QString functionId READ functionId CONSTANT)
    Q_PROPERTY(QString title READ title CONSTANT)
    Q_PROPERTY(QString description READ description CONSTANT)
    Q_PROPERTY(FunctionState state READ state NOTIFY stateChanged)
    Q_PROPERTY(bool isActive READ isActive NOTIFY stateChanged)
    Q_PROPERTY(bool canStart READ canStart NOTIFY canStartChanged)
    Q_PROPERTY(int progress READ progress NOTIFY progressChanged)
    Q_PROPERTY(QString statusMessage READ statusMessage NOTIFY statusMessageChanged)
    Q_PROPERTY(int remainingTime READ remainingTime NOTIFY remainingTimeChanged)
    Q_PROPERTY(QStringList mutuallyExclusiveWith READ mutuallyExclusiveWith CONSTANT)

public:
    enum FunctionState {
        Stopped,
        Starting,
        Running,
        Stopping,
        Error,
        Completed
    };
    Q_ENUM(FunctionState)

    explicit ServiceFunction(const QString &functionId, const QString &title, QObject *parent = nullptr);
    virtual ~ServiceFunction() = default;

    // Property getters
    QString functionId() const { return m_functionId; }
    QString title() const { return m_title; }
    QString description() const { return m_description; }
    FunctionState state() const { return m_state; }
    bool isActive() const { return m_state == Running || m_state == Starting; }
    bool canStart() const { return m_canStart; }
    int progress() const { return m_progress; }
    QString statusMessage() const { return m_statusMessage; }
    int remainingTime() const { return m_remainingTime; }
    QStringList mutuallyExclusiveWith() const { return m_mutuallyExclusiveWith.values(); }
    int timeout() const;

    // Configuration methods
    void setDescription(const QString &description);
    void addMutuallyExclusiveFunction(const QString &functionId);
    void setCanStart(bool canStart);
    void setState(FunctionState newState);
    void setProgress(int progress);
    void setStatusMessage(const QString &message);
    void reportError(const QString &errorMessage);
    void setTimeout(int newTimeout);

public slots:
    bool start();
    bool stop();
    void reset();

private:
    // calls to middleware layer
    bool doStart();
    bool doStop();
    void doReset();

signals:
    void stateChanged();
    void canStartChanged();
    void progressChanged();
    void statusMessageChanged();
    void remainingTimeChanged();
    void functionStarted();
    void functionCompleted();
    void functionStopped();
    void functionError(const QString &errorMessage);

protected:
    void updateRemainingTime();

private slots:
    void onTimeout();

private:
    QString m_functionId;
    QString m_title;
    QString m_description;
    FunctionState m_state;
    bool m_canStart;
    int m_progress;
    QString m_statusMessage;
    QSet<QString> m_mutuallyExclusiveWith;

    QTimer *m_timeoutTimer;

    qint64 m_startTimestamp; // Unix timestamp in ms
    int m_remainingTime; // Remaining time in seconds
};

#endif // PRINTERSERVICEFUNCTION_H
