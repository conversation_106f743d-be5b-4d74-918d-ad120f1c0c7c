#ifndef SERVICESTATUS_H
#define SERVICESTATUS_H

#include <QObject>
#include <QString>
#include <QVariant>

class ServiceStatus : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QString name READ name CONSTANT)
    Q_PROPERTY(QString caption READ caption NOTIFY captionChanged)
    Q_PROPERTY(QVariant value READ value NOTIFY valueChanged)
    Q_PROPERTY(QString units READ units NOTIFY unitsChanged)
    Q_PROPERTY(QString formattedValue READ formattedValue NOTIFY valueChanged)
    Q_PROPERTY(StatusLevel level READ level NOTIFY levelChanged)
    Q_PROPERTY(QString levelText READ levelText NOTIFY levelChanged)
    Q_PROPERTY(QVariant minValue READ minValue NOTIFY rangeChanged)
    Q_PROPERTY(QVariant maxValue READ maxValue NOTIFY rangeChanged)
    Q_PROPERTY(QVariant warningThreshold READ warningThreshold NOTIFY rangeChanged)
    Q_PROPERTY(QVariant criticalThreshold READ criticalThreshold NOTIFY rangeChanged)
    Q_PROPERTY(bool hasRange READ hasRange NOTIFY rangeChanged)
    Q_PROPERTY(int precision READ precision NOTIFY precisionChanged)

public:
    enum StatusLevel {
        Normal,
        Warning,
        Critical,
        Error,
        Unknown
    };
    Q_ENUM(StatusLevel)

    explicit ServiceStatus(const QString &name, QObject *parent = nullptr);
    explicit ServiceStatus(const QString &name, const QString &caption, QObject *parent = nullptr);

    // Property getters
    QString name() const { return m_name; }
    QString caption() const { return m_caption; }
    QVariant value() const { return m_value; }
    QString units() const { return m_units; }
    QString formattedValue() const;
    StatusLevel level() const { return m_level; }
    QString levelText() const;
    QVariant minValue() const { return m_minValue; }
    QVariant maxValue() const { return m_maxValue; }
    QVariant warningThreshold() const { return m_warningThreshold; }
    QVariant criticalThreshold() const { return m_criticalThreshold; }
    bool hasRange() const { return m_hasRange; }
    int precision() const { return m_precision; }

    // Configuration methods
    void setCaption(const QString &caption);
    void setUnits(const QString &units);
    void setRange(const QVariant &minValue, const QVariant &maxValue);
    void setWarningThreshold(const QVariant &threshold);
    void setCriticalThreshold(const QVariant &threshold);
    void setPrecision(int precision);
    void clearRange();

public slots:
    void setValue(const QVariant &value);
    void updateValue(const QVariant &value) { setValue(value); }

signals:
    void captionChanged();
    void valueChanged();
    void unitsChanged();
    void levelChanged();
    void rangeChanged();
    void precisionChanged();
    void thresholdExceeded(StatusLevel level, const QVariant &value);
    void thresholdRestored(StatusLevel level, const QVariant &value);

private:
    void updateLevel();
    StatusLevel calculateLevel(const QVariant &value) const;
    QString formatValue(const QVariant &value) const;

    QString m_name;
    QString m_caption;
    QVariant m_value;
    QString m_units;
    StatusLevel m_level;
    QVariant m_minValue;
    QVariant m_maxValue;
    QVariant m_warningThreshold;
    QVariant m_criticalThreshold;
    bool m_hasRange;
    int m_precision;
};

#endif // SERVICESTATUS_H
