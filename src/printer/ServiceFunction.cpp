#include "ServiceFunction.h"
#include <QDateTime>
#include <QDebug>

ServiceFunction::ServiceFunction(const QString &functionId, const QString &title, QObject *parent)
    : QObject(parent)
    , m_functionId(functionId)
    , m_title(title)
    , m_state(Stopped)
    , m_canStart(true)
    , m_progress(0)
    , m_timeoutTimer(new QTimer(this))
    , m_startTimestamp(0)
    , m_remainingTime(0)
{
    m_timeoutTimer->setSingleShot(true);
    connect(m_timeoutTimer, &QTimer::timeout, this, &ServiceFunction::onTimeout);
}

void ServiceFunction::setDescription(const QString &description)
{
    m_description = description;
}

void ServiceFunction::addMutuallyExclusiveFunction(const QString &functionId)
{
    m_mutuallyExclusiveWith.insert(functionId);
}

void ServiceFunction::setCanStart(bool canStart)
{
    if (m_canStart != canStart) {
        m_canStart = canStart;
        emit canStartChanged();
    }
}

bool ServiceFunction::start()
{
    if (!m_canStart || isActive()) {
        qWarning() << "Cannot start function" << m_title << "- canStart:" << m_canStart << "isActive:" << isActive();
        return false;
    }

    setState(Starting);
    setProgress(0);
    setStatusMessage("Starting...");
    m_startTimestamp = QDateTime::currentMSecsSinceEpoch();
    updateRemainingTime();

    if (doStart()) {
        setState(Running);
        setStatusMessage("Running...");

        if (m_timeoutTimer->interval() > 0) {
            m_timeoutTimer->start();
        }
        emit functionStarted();
        return true;
    } else {
        setState(Error);
        setStatusMessage("Failed to start");
        emit functionError("Failed to start function");
        return false;
    }
}

bool ServiceFunction::stop()
{
    if (!isActive()) {
        return true; // Already stopped
    }

    setState(Stopping);
    setStatusMessage("Stopping...");
    m_timeoutTimer->stop();

    if (doStop()) {
        setState(Stopped);
        setProgress(0);
        setStatusMessage("Stopped");
        emit functionStopped();
        return true;
    } else {
        setState(Error);
        setStatusMessage("Failed to stop");
        emit functionError("Failed to stop function");
        return false;
    }
}

void ServiceFunction::reset()
{
    if (isActive()) {
        stop();
    }

    setState(Stopped);
    setProgress(0);
    setStatusMessage("");
    m_timeoutTimer->stop();

    doReset();
}

bool ServiceFunction::doStart()
{
    return true;
}

bool ServiceFunction::doStop()
{
    return true;
}

void ServiceFunction::doReset()
{

}

void ServiceFunction::setState(FunctionState newState)
{
    if (m_state != newState) {
        m_state = newState;
        emit stateChanged();

        // Emit specific signals based on state changes
        if (newState == Completed) {
            setProgress(100);
            setStatusMessage("Completed");
            emit functionCompleted();
        }
    }
}

void ServiceFunction::setProgress(int progress)
{
    progress = qBound(0, progress, 100);
    if (m_progress != progress) {
        m_progress = progress;
        emit progressChanged();
        updateRemainingTime();
        if (progress == 100 && m_state == Running) {
            setState(Completed);
        }
    }
}

void ServiceFunction::setStatusMessage(const QString &message)
{
    if (m_statusMessage != message) {
        m_statusMessage = message;
        emit statusMessageChanged();
    }
}

void ServiceFunction::reportError(const QString &errorMessage)
{
    setState(Error);
    setStatusMessage(errorMessage);
    m_timeoutTimer->stop();
    emit functionError(errorMessage);
}

void ServiceFunction::onTimeout()
{
    if (isActive()) {
        qWarning() << "Function" << m_title << "timed out";
        reportError("Operation timed out");
    }
}

int ServiceFunction::timeout() const
{
    return m_timeoutTimer->interval();
}

void ServiceFunction::setTimeout(int newTimeout)
{
    if (newTimeout == 0) {
        m_timeoutTimer->stop();
    }
    m_timeoutTimer->setInterval(newTimeout);
}

void ServiceFunction::updateRemainingTime()
{
    if (m_progress <= 0 || m_progress >= 100 || m_startTimestamp == 0) {
        m_remainingTime = 0;
    } else {
        qint64 elapsedMs = QDateTime::currentMSecsSinceEpoch() - m_startTimestamp;
        double rate = m_progress / 100.0;
        if (rate > 0) {
            int totalMs = static_cast<int>(elapsedMs / rate);
            m_remainingTime = (totalMs - elapsedMs) / 1000;
            if (m_remainingTime < 0) m_remainingTime = 0;
        } else {
            m_remainingTime = 0;
        }
    }
    emit remainingTimeChanged();
}
