#include "Constants.h"
#include "ErrorManager.h"
#include <QVector>

ErrorManager* ErrorManager::s_instance = nullptr;

ErrorManager* ErrorManager::instance() {
    if (!s_instance)
        s_instance = new ErrorManager();
    return s_instance;
}

#include <QFile>
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>
#include <QCoreApplication>
#include <QDir>

#include <QTimer>
#include <QRandomGenerator>

ErrorManager::ErrorManager(QObject* parent)
    : QObject(parent), m_errorSimTimer(new QTimer(this)) {
    loadErrorsFromFile();
    startErrorSimulation();
}

const QVector<ErrorEntry>& ErrorManager::errors() const {
    return m_errors;
}

bool ErrorManager::hasErrors() const {
    return !m_errors.isEmpty();
}

void ErrorManager::addError(const QString& error, const QString& code) {
    ErrorEntry entry{error, code, QDateTime::currentDateTime()};
    m_pendingErrors.append(entry); // Add to pending queue instead of directly to m_errors
    emit pendingErrorsChanged();
    // Do not emit errorsChanged here
    // saveErrorsToFile();
}

void ErrorManager::clearErrors() {
    m_errors.clear();
    emit errorsChanged();
    // saveErrorsToFile();
}

void ErrorManager::loadPendingErrors() {
    if (!m_pendingErrors.isEmpty()) {
        m_errors += m_pendingErrors;
        m_pendingErrors.clear();
        emit errorsChanged();
        emit pendingErrorsChanged();
    }
}

void ErrorManager::startErrorSimulation() {
    connect(m_errorSimTimer, &QTimer::timeout, this, &ErrorManager::simulateRandomError);
    int interval = QRandomGenerator::global()->bounded(5000, 15001); // 5-15s
    m_errorSimTimer->start(interval);
}

void ErrorManager::simulateRandomError() {
    addError(randomErrorMessage(), randomErrorCode());
    int interval = QRandomGenerator::global()->bounded(5000, 15001); // 5-15s
    m_errorSimTimer->start(interval);
}

int ErrorManager::pendingErrorCount() const {
    return m_pendingErrors.size();
}

bool ErrorManager::hasPendingErrors() const {
    return pendingErrorCount() > 0;
}


QString ErrorManager::randomErrorMessage() const {
    static const QStringList messages = {
        "Paper jam detected",
        "Low toner warning",
        "Printer offline",
        "Cover open",
        "Overheating detected",
        "Unknown error occurred"
    };
    int idx = QRandomGenerator::global()->bounded(messages.size());
    return messages.at(idx);
}

QString ErrorManager::randomErrorCode() const {
    static const QStringList codes = {
        "E101",
        "E202",
        "E303",
        "E404",
        "E505",
        "E999"
    };
    int idx = QRandomGenerator::global()->bounded(codes.size());
    return codes.at(idx);
}

void ErrorManager::loadErrorsFromFile() {
    m_errors.clear();
    auto errorlogFilePath = QDir(QCoreApplication::applicationDirPath()).filePath(Constants::ERRORLOG_FILE);
    QFile file(errorlogFilePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Could not open error log file:" << errorlogFilePath;
        return;
    }
    QByteArray data = file.readAll();
    file.close();
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    if (error.error != QJsonParseError::NoError) {
        qWarning() << "JSON parse error in error log file:" << error.errorString();
        return;
    }
    if (!doc.isArray()) {
        qWarning() << "Error log file does not contain a JSON array";
        return;
    }
    QJsonArray arr = doc.array();
    for (const auto& val : arr) {
        QJsonObject obj = val.toObject();
        ErrorEntry entry;
        entry.error = obj["error"].toString();
        entry.code = obj["code"].toString();
        entry.timestamp = QDateTime::fromString(obj["timestamp"].toString(), Qt::ISODate);
        if (!entry.error.isEmpty() && !entry.code.isEmpty())
            m_errors.append(entry);
    }
    emit errorsChanged();
}

// TODO with database (likley sqlite)

// void ErrorManager::reloadErrorsFromFile() {
//     loadErrorsFromFile();
// }

// void ErrorManager::saveErrorsToFile() {
//     auto errorlogFilePath = QDir(QCoreApplication::applicationDirPath()).filePath(Constants::ERRORLOG_FILE);
//     QFile file(errorlogFilePath);
//     if (!file.open(QIODevice::WriteOnly)) {
//         qWarning() << "Could not open error log file for writing:" << errorlogFilePath;
//         return;
//     }
//     QJsonArray arr;
//     for (const ErrorEntry& entry : m_errors) {
//         QJsonObject obj;
//         obj["error"] = entry.error;
//         obj["code"] = entry.code;
//         obj["timestamp"] = entry.timestamp.toString(Qt::ISODate);
//         arr.append(obj);
//     }
//     QJsonDocument doc(arr);
//     file.write(doc.toJson());
//     file.close();
// }
