Taken from: https://docs.google.com/document/d/13FNlHgQ1aOQZGKdW-RL0d4XV6P__Qekg21ni2KNQCcc/edit?tab=t.0

Project Proposal: Tablet Application Design for Industrial Factories


1. Introduction


This proposal outlines a project to design a tablet application named "light" for use in industrial factories. The application will be designed for a touchscreen interface with a 1920x1080 resolution, based on the provided Balsamiq wireframes and sitemap. The design will be developed in Figma.


2. Project Goals
* Translate the provided Balsamiq wireframes and sitemap into a high-fidelity Figma design and create a user-friendly tablet application ("light") optimized for industrial factory environments. This includes a total of about 47 designs broken down into.. *counts are subject to minor change*
   * 33 Pages
   * 13 Pop-ups
   * All various components include and reasonably interpreted from the design
* Ensure the design is intuitive, efficient, and suitable for factory workers using a 1920x1080 touchscreen.
* Incorporate creative and UI/UX best practices to enhance the application's usability and effectiveness. Including a color and font palette included in the Figma design.
3. Scope of Work
* Figma Design: Develop a complete design of the "light" tablet application in Figma, adhering to the provided wireframes and sitemap.
* Touchscreen Optimization: Design the interface specifically for a 1920x1080 touchscreen, considering touch targets, user interactions, and the industrial environment.
* UI/UX Expertise: Provide input on button placement, component sizing, pop-ups, page flow, structure, and navigation, based on the wireframes. Simplicity; big buttons and text, easy to use, simple language, easy logic in the workflow.
* Collaboration and Revisions: Work closely with the team to incorporate feedback and revisions into the design. Main focus is GREAT WORKFLOW - simple easy workflow
* Initial Mock-up: Create an initial mock-up of the "Print page" before proceeding with a designer candidate.
* Theme and Colors: Take inspiration from the Prospr Logo and the provided Fonts
4. Designer Responsibilities


In addition to the above, the designer should be able to assist with:
* Color palette and typography selection appropriate for an industrial setting. - To be provided
* Iconography and imagery that is clear and easily understandable.
* Creating a design system or component library to ensure consistency throughout the application.
* Accessibility considerations, ensuring the application is usable by all workers.
* Prototyping and interactive elements in Figma to demonstrate the application's functionality.
* Designing for error handling and user feedback within the application.
* Creating a clear visual hierarchy and layout for each page, based on the wireframes.
5. Deliverables
* Initial mock-up of the "Print Page"
* Figma design file of the complete "light" tablet application.
* Design system or component library created for the application.
6. Process
* Development of an initial mock-up of the "Print page."
* Review and analysis of the provided Balsamiq wireframes and sitemap.
* Iterative design process with feedback and revisions .
* Finalization of the Figma design.
7. Timeline


A detailed timeline will be initially discussed and then further discussed after review of the initial mock-up.


8. Budget


A budget will be determined based on the scope of work and designer's rate.


9. Next Steps


To proceed, please confirm if the above scope aligns with your expectations. Once confirmed, we can have an initial meeting and then move forward with the initial mock-up of the "Print page".