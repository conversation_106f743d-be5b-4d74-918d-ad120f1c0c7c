# T507 Touchscreen & Deployment Debugging - In Progress (2025-04-10) [UPDATED]

## Objective

Resolve issues with touchscreen input not registering on the T507 target device and fix persistent "Text file busy" errors encountered during deployment via the `./scripts/deploy/t507_file_path_solution.sh` script.

## Work Completed & Findings

1.  **Touchscreen Input Device:**
    *   Initial touch tests failed with the default `QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1`.
    *   Systematic testing identified `/dev/input/event2` as the correct input node for the touchscreen. Manually running the application binary `/root/light_qt5` via SSH confirmed that touch input works when the environment is configured to use `/dev/input/event2`.

2.  **Deployment "Text file busy" Error:**
    *   Repeated attempts to deploy using `./scripts/deploy/t507_file_path_solution.sh` failed with `scp: /root/light_qt5: Text file busy`.
    *   This error occurred even immediately after a full device reboot, indicating an aggressive auto-start mechanism was locking the binary.

3.  **Target Init System:**
    *   Investigated potential auto-start locations.
    *   Confirmed the target does **not** use `systemd` (`systemctl` command not found).
    *   Identified the target uses a SysVinit-style system (`/etc/init.d/`).

4.  **Auto-Start Mechanisms Investigated & Disabled:**
    *   Checked `/etc/rc.local` (Not Found).
    *   Checked `/etc/init.d/`:
        *   Found and disabled `/etc/init.d/S99prospr-light` (via `chmod -x`).
        *   Found and disabled `/etc/init.d/S99prosprlight` (via `chmod -x`).
    *   Checked `/etc/init.d/S15run_app`: Found it sources `/etc/embedsky_conf`.
    *   Checked `/etc/embedsky_conf`: Found it launched `/test_utils/T507_test &`. This line was commented out using `sed`.

## Changes Made

*   **On Host:**
    *   `scripts/deploy/t507_file_path_solution.sh`: Modified the generated launcher script (`/tmp/prospr_light_file_launcher.sh`) to set `QT_QPA_GENERIC_PLUGINS=evdevtouch:/dev/input/event2` and `QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event2:rotate=0`. ([Link to File](cci:7://file:///home/<USER>/prospr/light/scripts/deploy/t507_file_path_solution.sh))
*   **On Target (**************):**
    *   `/etc/init.d/S99prospr-light`: Removed execute permissions (`chmod -x`).
    *   `/etc/init.d/S99prosprlight`: Removed execute permissions (`chmod -x`).
    *   `/etc/embedsky_conf`: Commented out the line `/test_utils/T507_test &` using `sed`.

## Pickup Where You Left Off

1.  **Problem:** Despite disabling multiple apparent auto-start scripts and commenting out a direct launch command in `/etc/embedsky_conf`, the "Text file busy" error *still* occurred when attempting deployment immediately after these changes.
2.  **Next Step:** The immediate next step was to **reboot the target device (**************)** to ensure the changes to `/etc/embedsky_conf` were fully applied and no cached state remained.
3.  **Action After Reboot:** Attempt deployment again using the local script:
    ```bash
    ./scripts/deploy/t507_file_path_solution.sh
    ```
4.  **If Error Persists:**
    *   Re-examine the `/etc/init.d/S15run_app` script, specifically the line `sh /etc/run_autotest.sh &`.
    *   Investigate the contents of `/etc/run_autotest.sh` on the target (`ssh root@************** "cat /etc/run_autotest.sh"`) as this is now the most likely remaining source of the auto-start.
    *   Continue searching `/etc/init.d/` or other SysVinit configuration locations for anything else that might launch the application.

## Relevant Files

*   **Host Deployment Script:** [/home/<USER>/prospr/light/scripts/deploy/t507_file_path_solution.sh](cci:7://file:///home/<USER>/prospr/light/scripts/deploy/t507_file_path_solution.sh)
*   **Target Config/Scripts (via SSH to **************):**
    *   `/etc/init.d/S99prospr-light`
    *   `/etc/init.d/S99prosprlight`
    *   `/etc/init.d/S15run_app`
    *   `/etc/embedsky_conf`
    *   `/etc/run_autotest.sh` (Next suspect if problem persists)
    *   `/root/light_qt5` (The binary being locked)
    *   `/root/prospr_light_file_launcher.sh` (Launcher created by deployment script)
    *   `/root/prospr_launcher.sh` (Launched by S99prospr-light)
    *   `/root/t507_enhanced_launcher.sh` (Launched by S99prosprlight)
    *   `/test_utils/T507_test` (Launched by embedsky_conf, now commented out)

# T507 Touchscreen and QML Update Troubleshooting (2025-04-10)

## Objective
Resolve touchscreen input issues on the T507 device and ensure QML changes are reflected after deployment.

## Current Status (UPDATED 01:07)
- **[PROGRESS]** Successfully got touchscreen input working with modified environment variables.
  - Added QML cache disabling environment variables: `QML_DISABLE_DISK_CACHE=1` and `QV4_FORCE_INTERPRETER=1`
  - Added QML cache cleanup step to deployment script
  - Modified touchscreen parameters: `QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1:rotate=180:invertx`
  - Successfully observed touch input registering and button color change in response

- **[NEW ISSUE]** Screen blackout after successful touch interaction
  - After a successful touch was registered, the screen went black
  - Need to investigate potential causes: application crash, rendering issue, or display driver issue

- **[RESOLVED]** QML changes successfully reflected on device after disabling caching
  - Button size and UI changes now appearing correctly
  - Previous issues resolved by clearing QML cache and disabling caching mechanisms

## Previous Status
- **Touchscreen Input:** Confirmed `/dev/input/event1` is the correct device and Qt receives raw touch events (verified via `QT_LOGGING_RULES="qt.qpa.input*=true"`).
- **Coordinate Mapping:** Tested various `QT_QPA_EVDEV_TOUCHSCREEN_DEVICE` parameters without success in enabling consistent button interaction. 
- **Sporadic Input:** User observed the virtual keyboard appearing randomly after many touches, indicating *some* touch events were being processed by QML, but with incorrect coordinates.
- **QML Update Issue:** Button size changes were not reflected on target device despite rebuilding with `--fix-qml` flag.

## Next Steps
1. **Investigate Screen Blackout:**
   - Check application logs for potential crashes
   - Add error handling in QML to catch and report potential rendering issues
   - Test with a simpler QML file to isolate potential causes

2. **Optimize Touch Configuration:**
   - Current configuration `rotate=180:invertx` works for initial touch, but may need refinement
   - Consider adding touch feedback visuals to improve user experience
   - Test with multiple touch points to ensure consistent behavior

3. **Stability Testing:**
   - Once screen blackout issue is resolved, perform extensive touch testing across different areas of the interface
   - Verify the application remains stable after extended use
