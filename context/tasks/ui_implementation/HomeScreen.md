## Task: Implement Home Screen

**Source PNG(s):**
- `Home.png`
- `Home - Logged In.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/Home/HomePage.qml` (Proposed)

**Description:**
The main landing screen of the application. Displays key status information and provides quick access/overview. Content might differ slightly when logged in.

**Key UI Elements Visible:**
- Large Status Card/Area (e.g., "Printer Status: Ready", "Ink Level: 75%")
- Possibly quick action buttons (e.g., "Start Last Print Job"?)
- May show recent activity or message preview (less clear from PNG).
- Bottom Navigation Bar (`BottomBar`)

**Sub-Components Needed (Create or Reuse):**
- `LargeStatusCard`: [Component to display primary status prominently] - [Status: Create New]
- `QuickActionButton`: [Optional: Buttons for common actions] - [Status: Create New | Reuse `PrimaryButton`]
- `BottomBar`: [Global navigation bar] - [Status: Reuse Existing - Task defined]

**Icons Needed (Specific List):**
- `status_icon`: [Indicator within LargeStatusCard] - [Asset Source Suggestion: Needs identification based on status]
- `ink_icon`: [Indicator for ink level] - [Asset Source Suggestion: `vuesax/drop.svg`?]
- Icons for `BottomBar` (Home, Print, Service, System) - [Asset Source Suggestion: `vuesax/*`]

**Navigation:**
- **Accessed From:** Application start (default screen), `BottomBar`.
- **Navigates To:**
    - Other main sections (`Print`, `Service`, `System`) via `BottomBar`.
    - `LoginPage` (Task defined) potentially accessible via a dedicated button elsewhere if login is required for actions.
    - Potentially `PrintPage` via quick action button.

**Data Requirements/Interaction:**
- Displays key real-time printer status (e.g., Ready, Printing, Error).
- Displays ink/consumable levels.
- Tapping quick actions triggers corresponding backend commands.
- Content might slightly vary based on login status (though PNGs look similar here).

**Global Components Used:**
- `BottomBar`: Yes
- `VirtualKeyboard`: No

**Special Considerations:**
- Real-time updates for status information.
- Clear visual indication of printer status.
- No global App Header is used; content starts below the system status bar area.

**Dependencies:**
- `LargeStatusCard` component
- `QuickActionButton` component (Optional)
- `BottomBar` component
- Icon assets
- Backend integration for status data. 