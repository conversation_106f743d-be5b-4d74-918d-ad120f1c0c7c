## Task: Implement Service Reset Maintenance Screen

**Source PNG(s):**
- `Service - Reset Maintenance.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/Service/ResetMaintenancePage.qml` (Proposed)

**Description:**
Allows resetting the printer's maintenance counter, likely after performing maintenance tasks.

**Key UI Elements Visible:**
- Header/Title Bar ("Reset Maintenance" and Back Button)
- Display Area showing current maintenance value/status (e.g., "Maintenance Value: XXXX Hours")
- Button: Reset

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `StatusDisplayField`: [Component to show the current maintenance value] - [Status: Create New | Reuse `LabeledDisplayField`]
- `PrimaryButton`: [Reset button] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `reset_icon`: [May be used on the button or status display] - [Asset Source Suggestion: `vuesax/refresh-2.svg`?]

**Navigation:**
- **Accessed From:** Likely from the main `Service.png` screen (e.g., tapping a "Reset Maintenance" option).
- **Navigates To:**
    - Back to `ServicePage` using the Back button.
    - Possibly shows a confirmation popup after reset.

**Data Requirements/Interaction:**
- Displays the current maintenance counter value from the backend.
- Tapping the 'Reset' button sends a command to the backend to reset the counter.
- The displayed value should update after a successful reset.
- May require confirmation before resetting.

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: No

**Special Considerations:**
- Confirmation before resetting is highly recommended.
- Clear feedback on successful reset.
- Requires backend integration for retrieving and resetting the counter.

**Dependencies:**
- `ScreenHeader` component
- `StatusDisplayField` component
- `PrimaryButton` component
- Confirmation Popup (Task needed? Or reuse generic confirmation popup)
- `ServicePage` (for navigation context)
- Backend integration. 