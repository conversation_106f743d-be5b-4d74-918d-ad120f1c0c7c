## Task: Implement Users & Permissions List Screen

**Source PNG(s):**
- `System - Users & Permissions.png`
- `System - Users & Permissions - Edit Selected.png` (Shows Edit mode)
- `System - Users & Permissions - Delete Selected.png` (Shows Delete mode)

**Type:** Screen

**QML File Path:** `UI_new/Screens/System/UsersPermissions/SystemUserPermissionsPage.qml` (Proposed)

**Description:**
Displays a list of registered users and their permission levels. Allows adding new users, and entering modes to edit or delete existing users.

**Key UI Elements Visible:**
- Header/Title Bar ("Users & Permissions" and Back Button)
- Action Buttons in Header/Subheader? (Add User, Edit, Delete)
- List of Users
    - Each item shows Username, Permission Level.
    - In Edit/Delete modes, shows RadioButton/Checkbox respectively.
- Bottom Action Bar (Only visible in Edit/Delete modes)

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `HeaderActions`: [Container for action buttons like Add/Edit/Delete in header area] - [Status: Reuse Existing | Create New]
- `IconButton`: [For Add, Edit, Delete actions] - [Status: Reuse Existing]
- `UserListView`: [Scrollable list view for users] - [Status: Create New]
- `UserListItemDelegate`: [Delegate for list item, adaptable for normal/edit/delete modes] - [Status: Create New]
- `BottomActionBar`: [Container for bottom buttons, visible in edit/delete modes] - [Status: Reuse Existing | Create New]
- `StandardButton`: [Cancel button in action bar] - [Status: Reuse Existing]
- `PrimaryButton`: [Edit Selected button in action bar] - [Status: Reuse Existing]
- `DestructiveButton`: [Delete Selected button in action bar] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `add_user_icon`: [Add New User] - [Asset Source Suggestion: `vuesax/user-add.svg`]
- `edit_icon`: [Enter Edit Mode] - [Asset Source Suggestion: `vuesax/edit.svg`]
- `delete_icon`: [Enter Delete Mode] - [Asset Source Suggestion: `vuesax/trash.svg`]
- `radio_unchecked`/`radio_checked`: [Edit Mode] - [Asset Source Suggestion: Needs identification/creation]
- `checkbox_unchecked`/`checkbox_checked`: [Delete Mode] - [Asset Source Suggestion: `vuesax/tick-square.svg` variants]

**Navigation:**
- **Accessed From:** Likely from the main `System.png` screen (e.g., tapping "Users & Permissions").
- **Navigates To:**
    - `AddUserPage` (Task defined) when tapping Add User icon.
    - Enters 'Edit Mode' (UI change) when tapping Edit icon.
    - Enters 'Delete Mode' (UI change) when tapping Delete icon.
    - `EditPermissionsPopup` (Task defined) via 'Edit Selected' button in Edit Mode.
    - `DeleteUserPopup` (Task defined) via 'Delete Selected' button in Delete Mode.
    - Back to `SystemPage` using the Back button.

**Data Requirements/Interaction:**
- Fetches and displays the list of registered users and their permissions.
- Tapping Add User navigates to the add user form.
- Tapping Edit/Delete toggles the screen mode.
- In Edit mode, allows single selection and navigation to edit popup.
- In Delete mode, allows multiple selection and triggering delete confirmation.

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: No (Needed on AddUserPage)

**Special Considerations:**
- Managing the different view states (normal, edit selection, delete selection) within the same QML file.
- Conditionally displaying list item controls and the bottom action bar.
- Backend integration for user management (list, add, edit permissions, delete).

**Dependencies:**
- All sub-components listed above.
- `AddUserPage` (Task defined)
- `EditPermissionsPopup` (Task defined)
- `DeleteUserPopup` (Task defined)
- `SystemPage` (for navigation context)
- Backend integration. 