## Task: Implement Printer Setting Delete Popup

**Source PNG(s):**
- `Printer Setting - Delete.png`

**Type:** Popup

**QML File Path:** `UI_new/Components/Popups/PrinterSettingDeletePopup.qml` (Proposed)

**Description:**
A confirmation popup asking the user if they are sure they want to delete the selected printer setting profile.

**Key UI Elements Visible:**
- Popup Container
- Icon (Warning/Trash icon)
- Title: "Delete"
- Body Text: "Are you sure you want to delete this setting?"
- Button: Cancel
- Button: Delete (Primary destructive action)

**Sub-Components Needed (Create or Reuse):**
- `PopupBase`: [Base component for modal popups] - [Status: Reuse Existing]
- `StandardButton`: [Default action button (e.g., Cancel)] - [Status: Reuse Existing]
- `DestructiveButton`: [Primary action button styled for destructive actions] - [Status: Reuse Existing]
- `StatusIcon`: [Component to display icons like warning] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `warning_icon` or `trash_icon`: [Indicator for delete confirmation] - [Asset Source Suggestion: `vuesax/warning-2.svg`, `vuesax/trash.svg`]

**Navigation:**
- **Accessed From:** Tapping the Delete button on the `Printer Setting - Delete view.png` screen.
- **Navigates To:**
    - Closes the popup on 'Cancel' or 'Delete' button press, returning focus to the `PrinterSettingListPage` (or the delete view).

**Data Requirements/Interaction:**
- Displays confirmation text.
- 'Delete' action triggers the deletion of the selected printer setting profile via the backend.
- 'Cancel' action closes the popup without deleting.

**Global Components Used:**
- `VirtualKeyboard`: No

**Special Considerations:**
- Use appropriate warning color/icon for the destructive action.
- Ensure the correct setting profile context is passed for deletion.

**Dependencies:**
- `PopupBase` component
- `StandardButton` component
- `DestructiveButton` component
- `StatusIcon` component
- `PrinterSettingListPage` / `PrinterSettingDeleteView` (for context and triggering - needs definition)
- Backend integration for setting profile deletion. 