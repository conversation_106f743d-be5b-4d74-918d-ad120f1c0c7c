## Task: Implement Insert Text Screen

**Source PNG(s):**
- `Text.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/InsertData/InsertText.qml` (Proposed)

**Description:**
Allows configuration and insertion of a static text field into the print message. Provides options for text content, font, size, and potentially style (bold/italic).

**Key UI Elements Visible:**
- Header/Title Bar ("Text" and Back Button)
- Input Area: Large text input field for the content.
- Font Selection Dropdown
- Font Size Input/Dropdown
- Style Toggles? (Bold, Italic - not explicitly visible but common)
- Button: Cancel
- Button: Insert

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `LargeTextInputArea`: [Multi-line text input component] - [Status: Create New | Reuse standard TextArea]
- `LabeledDropdown`: [Standard label + dropdown pair for Font] - [Status: Reuse Existing]
- `LabeledNumericInput` or `LabeledDropdown`: [For Font Size] - [Status: Reuse Existing]
- `ToggleIconButton`: [Optional: For Bold/Italic style toggles] - [Status: Create New]
- `StandardButton`: [Default action button (e.g., Cancel)] - [Status: Reuse Existing]
- `PrimaryButton`: [Primary action button (e.g., Insert)] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `dropdown_arrow`: [Dropdown indicator] - [Asset Source Suggestion: `vuesax/arrow-down.svg`]
- `bold_icon`: [Optional: Bold toggle] - [Asset Source Suggestion: `vuesax/text-bold.svg`]
- `italic_icon`: [Optional: Italic toggle] - [Asset Source Suggestion: `vuesax/text-italic.svg`]

**Navigation:**
- **Accessed From:** Tapping a "Text" or "Add Text" option, likely on a message editing screen.
- **Navigates To:**
    - Back to the message editing screen on Cancel or Insert.
    - Back to the message editing screen using the Back button.

**Data Requirements/Interaction:**
- User enters text content.
- User selects font and size.
- User potentially toggles styles.
- 'Insert' action adds the configured text field to the message editor/layout.
- 'Cancel' discards input.

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: Yes (For text content input)

**Special Considerations:**
- Handling font availability and previews.
- Need a source for available fonts.
- Handling virtual keyboard overlay, especially for the large input area.

**Dependencies:**
- `ScreenHeader` component
- `LargeTextInputArea` component
- `LabeledDropdown` component
- `LabeledNumericInput` / `LabeledDropdown` (for size)
- `ToggleIconButton` (Optional)
- `StandardButton` component
- `PrimaryButton` component
- Message editing screen (for navigation context)
- Integration with message editor logic. 