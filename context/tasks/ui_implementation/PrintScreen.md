## Task: Implement Print Screen

**Source PNG(s):**
- `Print.png`
- `Print - Guest Mode.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/Print/PrintPage.qml` (Proposed)

**Description:**
The main screen for managing print jobs. Allows selecting a message, previewing/editing it in the dedicated top area, and starting/stopping the print process. Shows printer status and potentially print counts. Guest mode has reduced functionality.

**Key UI Elements Visible:**
- Message Preview/Editor Area (Large rectangle at the top for message layout)
- Message Selection Area (Dropdown or button below the preview)
- Status Indicator Area (e.g., "Printer Status: Ready", Print Counts)
- Action Buttons:
    - Start/Stop Print (Large primary button)
    - Test Print (Icon button?)
    - Delete Message (Icon button?)
    - Load Message (Icon button?, e.g., from USB)
- Bottom Navigation Bar (`BottomBar`)

**Sub-Components Needed (Create or Reuse):**
- `MessagePreview`: [Component to render and potentially interact with the print message layout in the top area] - [Status: Create New - Complex]
- `MessageSelector`: [Dropdown or button/popup mechanism for choosing print message] - [Status: Create New]
- `StatusIndicator`: [Component to display key-value status like "Printer Status: Ready"] - [Status: Create New | Reuse `StatusIndicatorCard`?]
- `LargeActionButton`: [Primary start/stop print button] - [Status: Create New | Reuse `PrimaryButton` with specific styling]
- `IconButton`: [Smaller buttons for actions like Test Print, Delete, Load] - [Status: Create New]
- `BottomBar`: [Global navigation bar] - [Status: Reuse Existing - Task defined]

**Icons Needed (Specific List):**
- `dropdown_arrow`: [Message selector dropdown] - [Asset Source Suggestion: `vuesax/arrow-down.svg`]
- `play_icon`: [Start Print] - [Asset Source Suggestion: `vuesax/play.svg`]
- `stop_icon`: [Stop Print] - [Asset Source Suggestion: `vuesax/stop-circle.svg`?]
- `test_print_icon`: [Test Print action] - [Asset Source Suggestion: Needs identification]
- `delete_icon`: [Delete Message action] - [Asset Source Suggestion: `vuesax/trash.svg`]
- `load_icon` / `usb_icon`: [Load Message action] - [Asset Source Suggestion: `vuesax/document-upload.svg`, `vuesax/usb.svg`?]
- Icons for `BottomBar` (Home, Print, Service, System) - [Asset Source Suggestion: `vuesax/*`]

**Navigation:**
- **Accessed From:** `BottomBar`.
- **Navigates To:**
    - `TestPrintPopup` (Task needed) via Test Print button.
    - `DeleteMessagePopup` (Task defined) via Delete Message button.
    - `PrintUSBPage` (Task defined) via Load Message button.
    - Other main sections (`Home`, `Service`, `System`) via `BottomBar`.
    - Potentially message editing screens/popups via interaction with `MessagePreview`.
    - `LoginPage` (Task defined) potentially accessible via a dedicated button elsewhere if login is required for actions.

**Data Requirements/Interaction:**
- Displays list of available print messages in selector.
- Renders a preview/editable view of the selected message in the top area.
- Shows real-time printer status.
- Shows print counts.
- Start/Stop buttons control the physical printing process.
- Guest mode disables certain actions (e.g., Delete Message, Load Message, potentially editing).

**Global Components Used:**
- `BottomBar`: Yes
- `VirtualKeyboard`: Yes (If message preview area allows direct text input/editing)

**Special Considerations:**
- Message preview rendering and interaction is complex.
- Real-time status updates from the printer backend.
- Handling guest mode vs. logged-in user permissions.
- Visual state changes for Start/Stop button based on printer status.
- No global App Header component is used; the top area is dedicated to message preview/editing.

**Dependencies:**
- `MessagePreview` component
- `MessageSelector` component
- `StatusIndicator` component
- `LargeActionButton` component
- `IconButton` component
- `BottomBar` component (Task defined)
- `TestPrintPopup` (Task needed)
- `DeleteMessagePopup` (Task defined)
- `PrintUSBPage` (Task defined)
- Backend integration for messages, status, print control. 