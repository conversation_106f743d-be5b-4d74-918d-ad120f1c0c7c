## Task: Implement Delete Message Popup

**Source PNG(s):**
- `Delete Message.png`

**Type:** Popup

**QML File Path:** `UI_new/Components/Popups/DeleteMessagePopup.qml` (Proposed)

**Description:**
A confirmation popup asking the user if they are sure they want to delete the currently selected message.

**Key UI Elements Visible:**
- Popup Container
- Icon (Warning/Trash icon)
- Title: "Delete Message"
- Body Text: "Are you sure you want to delete this message?"
- Button: Cancel
- Button: Delete (Primary destructive action)

**Sub-Components Needed (Create or Reuse):**
- `PopupBase`: [Base component for modal popups, maybe variant without close button] - [Status: Reuse Existing | Create New]
- `StandardButton`: [Default action button (e.g., Cancel)] - [Status: Reuse Existing]
- `DestructiveButton`: [Primary action button styled for destructive actions (e.g., Delete, likely red)] - [Status: Create New | Reuse `PrimaryButton` with different color]
- `StatusIcon`: [Component to display icons like warning, error, info] - [Status: Create New]

**Icons Needed (Specific List):**
- `warning_icon` or `trash_icon`: [Indicator for delete confirmation] - [Asset Source Suggestion: `vuesax/warning-2.svg`, `vuesax/trash.svg`]

**Navigation:**
- **Accessed From:** Tapping the Delete Message icon/button on the `Print.png` screen.
- **Navigates To:**
    - Closes the popup on 'Cancel' or 'Delete' button press, returning focus to the `PrintPage`.

**Data Requirements/Interaction:**
- Displays confirmation text.
- 'Delete' action triggers the deletion of the currently selected message via the backend.
- 'Cancel' action closes the popup without deleting.

**Global Components Used:**
- `VirtualKeyboard`: No

**Special Considerations:**
- Use appropriate warning color/icon for the destructive action.
- Ensure the correct message context is passed for deletion.

**Dependencies:**
- `PopupBase` component
- `StandardButton` component
- `DestructiveButton` component
- `StatusIcon` component
- `PrintPage` (for context and triggering)
- Backend integration for message deletion. 