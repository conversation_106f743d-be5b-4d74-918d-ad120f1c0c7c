## Task: Implement Insert Counter Screen

**Source PNG(s):**
- `Insert - Counter.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/InsertData/InsertCounter.qml` (Proposed)

**Description:**
Allows configuration and insertion of a counter field into the print message. Provides settings for start count, end count, step, and total digits.

**Key UI Elements Visible:**
- Header/Title Bar ("Counter" and Back Button)
- Preview Area (shows example counter value)
- Labeled Input Field: "Start Count" (Numeric input)
- Labeled Input Field: "End Count" (Numeric input)
- Labeled Input Field: "Step" (Numeric input)
- Labeled Input Field: "Total Digits" (Numeric input)
- Button: Cancel
- Button: Insert

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `PreviewDisplay`: [Area to show a dynamically updated preview] - [Status: Reuse Existing]
- `LabeledNumericInput`: [Label + input specifically for numbers] - [Status: Reuse Existing]
- `StandardButton`: [Default action button (e.g., Cancel)] - [Status: Reuse Existing]
- `PrimaryButton`: [Primary action button (e.g., Insert)] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]

**Navigation:**
- **Accessed From:** Tapping "Counter" option on `INSERT DATA.png` screen.
- **Navigates To:**
    - Back to `InsertDataPage` on Cancel or Insert.
    - Back to `InsertDataPage` using the Back button.

**Data Requirements/Interaction:**
- Displays current/default counter settings.
- User enters numbers into input fields.
- Preview updates dynamically based on input values (showing Start Count formatted to Total Digits).
- 'Insert' action adds the configured counter field to the message editor/layout.
- 'Cancel' discards changes.

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: Yes (For numeric inputs)

**Special Considerations:**
- Input validation (e.g., Start, End, Step, Total Digits must be valid numbers; End >= Start; Total Digits >= digits in Start/End Count).
- Real-time update of the preview area.
- Handling virtual keyboard overlay.

**Dependencies:**
- `ScreenHeader` component
- `PreviewDisplay` component
- `LabeledNumericInput` component
- `StandardButton` component
- `PrimaryButton` component
- `InsertDataPage` (for navigation context)
- Integration with message editor logic. 