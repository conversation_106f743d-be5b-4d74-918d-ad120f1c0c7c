## Task: Implement System Info Screen

**Source PNG(s):**
- `System - System Info.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/System/SystemInfoPage.qml` (Proposed)

**Description:**
Displays various pieces of system information, such as software versions, serial numbers, network status, etc.

**Key UI Elements Visible:**
- Header/Title Bar ("System Info" and Back Button)
- List or grouped sections of information fields.
- Each field has a Label (e.g., "Software Version", "IP Address", "Serial Number") and a Value.

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `InfoListView` or `InfoSection`: [Container for displaying the info fields] - [Status: Create New | Reuse standard ListView/ColumnLayout]
- `LabeledDisplayField`: [Component showing a label and its corresponding value] - [Status: Reuse Existing | Create New]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]

**Navigation:**
- **Accessed From:** Likely from the main `System.png` screen (e.g., tapping a "System Info" option).
- **Navigates To:**
    - Back to `SystemPage` using the Back button.

**Data Requirements/Interaction:**
- Fetches various system information values from the backend.
- Displays the fetched labels and values.
- Values are typically read-only.

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: No

**Special Considerations:**
- Ensure consistent formatting for labels and values.
- May need a scrollable view if there is a lot of information.
- Requires backend integration to retrieve all necessary info fields.

**Dependencies:**
- `ScreenHeader` component
- `InfoListView` / `InfoSection` component
- `LabeledDisplayField` component
- `SystemPage` (for navigation context)
- Backend integration for system data. 