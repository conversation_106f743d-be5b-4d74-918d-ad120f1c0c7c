## Task: Implement Printer Setting Edit View Screen

**Source PNG(s):**
- `Printer Setting - Edit view.png`

**Type:** Screen (variant of Printer Setting List)

**QML File Path:** `UI_new/Screens/Settings/PrinterSettingListPage.qml` (Likely modifies existing list page state)

**Description:**
A view of the Printer Settings list screen specifically for selecting a profile to edit. Radio buttons appear next to each item, and an 'Edit Selected' button is shown.

**Key UI Elements Visible:**
- Header/Title Bar ("Printer Setting" and Back Button)
- List of Printer Setting Profiles
    - Each item has a Radio Button, Profile Name, potentially other details.
- Button Bar at bottom:
    - Button: Cancel (exits edit selection mode)
    - Button: Edit Selected (navigates to edit form for the selected item)

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `PrinterSettingListView`: [Scrollable list view for settings] - [Status: Reuse Existing | Needs modification for edit mode]
- `PrinterSettingListItemDelegate`: [Delegate for list item] - [Status: Reuse Existing | Needs modification for radio button]
- `StandardButton`: [Cancel button] - [Status: Reuse Existing]
- `PrimaryButton`: ['Edit Selected' button] - [Status: Reuse Existing]
- `BottomActionBar`: [Container for bottom buttons] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `radio_unchecked`: [Radio button state] - [Asset Source Suggestion: `vuesax/radio.svg`? Needs identification/creation]
- `radio_checked`: [Radio button state] - [Asset Source Suggestion: `vuesax/tick-circle.svg` variant?]

**Navigation:**
- **Accessed From:** Tapping an 'Edit' action/button on the main `Printer Setting.png` screen.
- **Navigates To:**
    - `PrinterSettingEditForm` (Task needed) when tapping 'Edit Selected', passing the selected profile ID.
    - Back to the normal `PrinterSettingListPage` view when tapping 'Cancel'.
    - Back to the previous screen (likely `System` or `Service`) using the Back button.

**Data Requirements/Interaction:**
- Displays the list of printer setting profiles.
- Allows user to select exactly one profile via radio buttons.
- 'Edit Selected' button is likely enabled only when one item is selected.
- Tapping 'Edit Selected' navigates to the edit form for that profile.
- 'Cancel' returns the list view to its normal state.

**Global Components Used:**
- `BottomBar`: No (Replaced by `BottomActionBar` in this mode)
- `VirtualKeyboard`: No

**Special Considerations:**
- This is likely a *mode* of the main `PrinterSettingListPage`.
- Need to manage the single selection state (radio group behavior).
- Conditional visibility of radio buttons and the bottom action bar.

**Dependencies:**
- `ScreenHeader` component
- `PrinterSettingListView` component (and its delegate)
- `StandardButton` component
- `PrimaryButton` component
- `BottomActionBar` component
- `PrinterSettingEditForm` (Task needed)
- `PrinterSettingListPage` (needs definition, this task modifies it)
- Backend integration for retrieving settings list. 