## Task: Implement Print USB Screen

**Source PNG(s):**
- `Print - USB.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/Print/PrintUSBPage.qml` (Proposed)

**Description:**
Allows browsing and selecting print messages from a connected USB drive.

**Key UI Elements Visible:**
- Header/Title Bar ("USB" and Back Button)
- File Browser Area:
    - Likely a list or grid view of files/folders.
    - Shows file/folder names and potentially icons.
    - May show file details like size or date.
- Action Buttons:
    - Select/Load Button (enabled when a valid file is selected)
    - Cancel Button

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `FileBrowserView`: [Component to display files/folders from USB] - [Status: Create New]
- `FileListItem`: [Delegate component for displaying a single file/folder in the list] - [Status: Create New]
- `StandardButton`: [Default action button (e.g., Cancel)] - [Status: Reuse Existing]
- `PrimaryButton`: [Primary action button (e.g., Select/Load), conditionally enabled] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `folder_icon`: [Indicator for directories] - [Asset Source Suggestion: `vuesax/folder.svg`]
- `file_icon`: [Indicator for files (message files)] - [Asset Source Suggestion: `vuesax/document.svg`]

**Navigation:**
- **Accessed From:** Tapping the Load Message icon/button (with USB context) on the `Print.png` screen.
- **Navigates To:**
    - Back to `PrintPage` on Cancel.
    - Back to `PrintPage` on Select/Load (after loading the message).
    - Back to `PrintPage` using the Back button.

**Data Requirements/Interaction:**
- Lists files and folders from the connected USB drive (requires backend access).
- Allows user to navigate directories.
- Allows user to select a file.
- 'Select/Load' button is enabled only when a valid message file is selected.
- 'Select/Load' action triggers loading the message from USB via the backend.

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: No

**Special Considerations:**
- Requires backend integration to access USB storage and parse file listings.
- Handling cases where no USB drive is connected or readable.
- Filtering to show only valid message file types.
- Providing feedback during file loading.

**Dependencies:**
- `ScreenHeader` component
- `FileBrowserView` component
- `FileListItem` component
- `StandardButton` component
- `PrimaryButton` component
- `PrintPage` (for context and triggering)
- Backend integration for USB access and file operations. 