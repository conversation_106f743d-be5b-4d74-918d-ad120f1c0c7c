## Task: Implement Service Function Nozzle Popup

**Source PNG(s):**
- `Service - Function - Nozzle.png`

**Type:** Popup

**QML File Path:** `UI_new/Components/Popups/ServiceFunctionNozzlePopup.qml` (Proposed)

**Description:**
A popup providing specific controls related to the printer nozzle function within Service mode, likely for cleaning or testing.

**Key UI Elements Visible:**
- Popup Container
- Title: "Nozzle"
- Close Button (X icon)
- Action Buttons (e.g., "Nozzle Open", "Nozzle Close", "Nozzle Cleaning")
- Possibly status indicators related to nozzle state.

**Sub-Components Needed (Create or Reuse):**
- `PopupBase`: [Base component for modal popups] - [Status: Reuse Existing]
- `ActionButton`: [Buttons for specific nozzle actions] - [Status: Reuse Existing `StandardButton`/`PrimaryButton`]
- `StatusIndicator`: [Optional: To show nozzle status] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `close_icon`: [Popup close] - [Asset Source Suggestion: `vuesax/close-circle.svg`]
- Icons for nozzle actions (open, close, clean) - [Asset Source Suggestion: Needs identification]

**Navigation:**
- **Accessed From:** Tapping a "Nozzle" button/option on the `Service - Function.png` screen.
- **Navigates To:**
    - Closes the popup on Close (X) button press, returning focus to the `ServiceFunctionPage`.
    - Actions might close the popup or keep it open while action is performed.

**Data Requirements/Interaction:**
- Buttons trigger specific nozzle control commands via the backend (Open, Close, Clean).
- May display nozzle status feedback from the backend.

**Global Components Used:**
- `VirtualKeyboard`: No

**Special Considerations:**
- Safety implications of nozzle control actions.
- Clear feedback on action success/failure.
- Requires backend integration for nozzle control and status.

**Dependencies:**
- `PopupBase` component
- `ActionButton` / `StandardButton` / `PrimaryButton` components
- `StatusIndicator` (Optional)
- `ServiceFunctionPage` (for context and triggering - needs definition)
- Backend integration. 