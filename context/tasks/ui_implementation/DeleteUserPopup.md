## Task: Implement Delete User Popup

**Source PNG(s):**
- `System - Users & Permissions - Delete User.png`

**Type:** Popup

**QML File Path:** `UI_new/Components/Popups/DeleteUserPopup.qml` (Proposed)

**Description:**
A confirmation popup asking the user if they are sure they want to delete the selected user(s).

**Key UI Elements Visible:**
- Popup Container
- Icon (Warning/Trash icon)
- Title: "Delete User"
- Body Text: "Are you sure you want to delete this user?" (or similar, maybe plural)
- Button: Cancel
- Button: Delete (Primary destructive action)

**Sub-Components Needed (Create or Reuse):**
- `PopupBase`: [Base component for modal popups] - [Status: Reuse Existing]
- `StandardButton`: [Default action button (e.g., Cancel)] - [Status: Reuse Existing]
- `DestructiveButton`: [Primary action button styled for destructive actions] - [Status: Reuse Existing]
- `StatusIcon`: [Component to display icons like warning] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `warning_icon` or `trash_icon`: [Indicator for delete confirmation] - [Asset Source Suggestion: `vuesax/warning-2.svg`, `vuesax/trash.svg`]

**Navigation:**
- **Accessed From:** Tapping the 'Delete Selected' button on the `System - Users & Permissions - Delete Selected.png` screen mode.
- **Navigates To:**
    - Closes the popup on 'Cancel' or 'Delete' button press, returning focus to the `SystemUserPermissionsPage` (likely refreshing the list after delete).

**Data Requirements/Interaction:**
- Displays confirmation text.
- 'Delete' action triggers the deletion of the selected user(s) via the backend.
- 'Cancel' action closes the popup without deleting.

**Global Components Used:**
- `VirtualKeyboard`: No

**Special Considerations:**
- Use appropriate warning color/icon.
- Ensure the correct User ID(s) are passed for deletion.
- May need slightly different text if multiple users can be deleted at once.

**Dependencies:**
- `PopupBase` component
- `StandardButton` component
- `DestructiveButton` component
- `StatusIcon` component
- `SystemUserPermissionsPage` / Delete Mode (for context and triggering)
- Backend integration for user deletion. 