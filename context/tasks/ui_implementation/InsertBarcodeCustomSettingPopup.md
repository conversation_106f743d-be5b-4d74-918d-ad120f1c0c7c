## Task: Implement Insert Barcode Custom Setting Popup

**Source PNG(s):**
- `Insert - Barcode - Custom Setting.png`

**Type:** Popup

**QML File Path:** `UI_new/Components/Popups/InsertBarcodeCustomSettingPopup.qml` (Proposed)

**Description:**
A popup dialog allowing configuration of custom settings for barcode insertion. Contains various input fields and dropdowns.

**Key UI Elements Visible:**
- Popup Container (likely with shadow/overlay)
- Title: "Custom Setting"
- Close Button (X icon)
- Multiple Labeled Input Fields (e.g., "Module Size", "Magnification")
- Multiple Labeled Dropdowns (e.g., "Error Correction Level")
- Checkbox? (e.g., "Process Tilde")
- Button: Cancel
- Button: Save

**Sub-Components Needed (Create or Reuse):**
- `PopupBase`: [Base component for modal popups with background overlay, title, close button] - [Status: Create New]
- `LabeledTextInput`: [Standard label + text input pair] - [Status: Create New | Reuse Existing]
- `LabeledDropdown`: [Standard label + dropdown pair] - [Status: Create New | Reuse Existing `DropdownSelector` with label]
- `LabeledCheckbox`: [Standard label + checkbox pair] - [Status: Create New | Reuse Existing standard CheckBox]
- `StandardButton`: [Default action button (e.g., Cancel)] - [Status: Reuse Existing]
- `PrimaryButton`: [Primary action button (e.g., Save)] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `close_icon`: [Popup close] - [Asset Source Suggestion: `vuesax/close-circle.svg`]
- `dropdown_arrow`: [Dropdown indicator] - [Asset Source Suggestion: `vuesax/arrow-down.svg`]

**Navigation:**
- **Accessed From:** Tapping a "Custom Setting" button on the `Insert - Barcode.png` screen.
- **Navigates To:**
    - Closes the popup on 'Cancel', 'Save', or Close (X) button press, returning focus to the `InsertBarcodePage`.

**Data Requirements/Interaction:**
- Displays current custom barcode settings.
- Allows user to modify settings via inputs, dropdowns, checkbox.
- 'Save' action confirms changes and likely updates the configuration state for the barcode field.
- 'Cancel' discards changes.

**Global Components Used:**
- `VirtualKeyboard`: Yes (For numeric/text input fields)

**Special Considerations:**
- Input validation for numeric fields (e.g., Module Size).
- Popup should overlay the parent screen (`InsertBarcodePage`).
- Ensure content scrolls if it exceeds popup height.
- Handle virtual keyboard interactions within the popup.

**Dependencies:**
- `PopupBase` component
- `LabeledTextInput` component
- `LabeledDropdown` component
- `LabeledCheckbox` component
- `StandardButton` component
- `PrimaryButton` component
- `InsertBarcodePage` (for context and triggering) 