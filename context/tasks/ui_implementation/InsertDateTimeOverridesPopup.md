## Task: Implement Insert Date & Time Overrides Popup

**Source PNG(s):**
- `Insert - Date & Time - Overrides.png`

**Type:** Popup

**QML File Path:** `UI_new/Components/Popups/InsertDateTimeOverridesPopup.qml` (Proposed)

**Description:**
A popup dialog for configuring date and time overrides. Allows setting offsets for days, months, years, hours, minutes, and seconds.

**Key UI Elements Visible:**
- Popup Container
- Title: "Overrides"
- Close Button (X icon)
- Multiple Labeled Input Fields with Steppers (+/- buttons): "Days", "Months", "Years", "Hours", "Minutes", "Seconds"
- Button: Cancel
- Button: Save

**Sub-Components Needed (Create or Reuse):**
- `PopupBase`: [Base component for modal popups] - [Status: Reuse Existing]
- `LabeledStepperInput`: [Label + numeric input with plus/minus stepper buttons] - [Status: Create New]
- `StandardButton`: [Default action button (e.g., Cancel)] - [Status: Reuse Existing]
- `PrimaryButton`: [Primary action button (e.g., Save)] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `close_icon`: [Popup close] - [Asset Source Suggestion: `vuesax/close-circle.svg`]
- `plus_icon`: [Stepper increment] - [Asset Source Suggestion: `vuesax/add.svg`]
- `minus_icon`: [Stepper decrement] - [Asset Source Suggestion: `vuesax/minus.svg`]

**Navigation:**
- **Accessed From:** Tapping an "Overrides" button on the `Insert - Date & Time` screen (presumed).
- **Navigates To:**
    - Closes the popup on 'Cancel', 'Save', or Close (X) button press, returning focus to the `InsertDateTimePage`.

**Data Requirements/Interaction:**
- Displays current date/time offset values.
- Allows user to modify offsets using stepper buttons or direct input.
- 'Save' action confirms changes and updates the date/time configuration.
- 'Cancel' discards changes.

**Global Components Used:**
- `VirtualKeyboard`: Yes (If direct input into stepper fields is allowed)

**Special Considerations:**
- Input validation (numeric ranges for offsets).
- Popup overlay behavior.
- Handling virtual keyboard if direct input is enabled.

**Dependencies:**
- `PopupBase` component
- `LabeledStepperInput` component
- `StandardButton` component
- `PrimaryButton` component
- `InsertDateTimePage` (for context and triggering - needs creation task) 