## Task: Implement Login Screen

**Source PNG(s):**
- `Log In.png`
- `Log In - Logged In.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/Login/LoginPage.qml` (Proposed)

**Description:**
Provides the interface for users to log in to the application using a username and password.

**Key UI Elements Visible:**
- Application Logo (`LogoBrand.qml` likely already exists or is needed)
- Input Field: Username (with user icon)
- Input Field: Password (with lock icon, masked input)
- Button: Log In
- Possibly a "Forgot Password?" link (not visible, but common)
- The 'Logged In' PNG likely shows the main App Header state after login, not part of the Login screen itself.

**Sub-Components Needed (Create or Reuse):**
- `LogoBrand`: [Displays the application logo] - [Status: Reuse Existing | Create New]
- `TextInputWithIcon`: [Input field for username] - [Status: Reuse Existing]
- `PasswordInputWithIcon`: [Input field for password] - [Status: Reuse Existing]
- `PrimaryButton`: [Log In button] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `user`: [Username field indicator] - [Asset Source Suggestion: `User.svg`, `vuesax/user.svg`]
- `lock`: [Password field indicator] - [Asset Source Suggestion: `vuesax/lock.svg`]

**Navigation:**
- **Accessed From:** Application start (if login required), or tapping User/Login button in header when logged out.
- **Navigates To:**
    - The default screen after successful login (e.g., `HomePage` or `PrintPage`).

**Data Requirements/Interaction:**
- User enters username and password.
- Input fields interact with Virtual Keyboard.
- 'Log In' button sends credentials to the backend for authentication.
- Displays error messages on authentication failure (e.g., "Invalid username or password").

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: Yes

**Special Considerations:**
- Secure handling of passwords.
- Clear error feedback on login failure.
- Input validation (e.g., non-empty fields).

**Dependencies:**
- `LogoBrand` component
- `TextInputWithIcon` component
- `PasswordInputWithIcon` component
- `PrimaryButton` component
- `VirtualKeyboard` component
- Backend integration for authentication. 