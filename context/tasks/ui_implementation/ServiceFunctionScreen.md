## Task: Implement Service Function Screen

**Source PNG(s):**
- `Service - Function.png`
- `Service - Function - Nozzle.png` (Referenced for Nozzle action)

**Type:** Screen

**QML File Path:** `UI_new/Screens/Service/ServiceFunctionPage.qml` (Proposed)

**Description:**
Provides access to various service functions related to printer components like pumps, valves, and nozzles.

**Key UI Elements Visible:**
- Header/Title Bar ("Function" and Back Button)
- Grid or List of Buttons/Cards for different functions:
    - Pump
    - Ink Drop
    - Valve
    - Nozzle
    - Etc.
- Each option likely has an icon and text label.

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `OptionCard`: [Clickable card/button component with icon and label] - [Status: Reuse Existing | Needs Verification]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `pump_icon`: [Pump function] - [Asset Source Suggestion: Needs identification]
- `ink_drop_icon`: [Ink Drop function] - [Asset Source Suggestion: Needs identification]
- `valve_icon`: [Valve function] - [Asset Source Suggestion: Needs identification]
- `nozzle_icon`: [Nozzle function] - [Asset Source Suggestion: Needs identification]
- Other function icons...

**Navigation:**
- **Accessed From:** Likely from the main `Service.png` screen (e.g., tapping a "Function" option).
- **Navigates To:**
    - Specific function control popups/screens when tapping an option (e.g., `ServiceFunctionNozzlePopup` when tapping "Nozzle").
    - Back to `ServicePage` using the Back button.

**Data Requirements/Interaction:**
- Displays available service functions.
- Tapping an option opens the corresponding control popup/screen.

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: No

**Special Considerations:**
- Layout should adapt nicely (likely a grid).
- Ensure clear touch targets for each function.
- Need to define the target popup/screen for each function button.

**Dependencies:**
- `ScreenHeader` component
- `OptionCard` component
- All specific function popups/screens (e.g., `ServiceFunctionNozzlePopup`)
- Icon assets for each function.
- `ServicePage` (for navigation context). 