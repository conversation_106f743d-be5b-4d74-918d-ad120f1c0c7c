## Task: Implement Insert Barcode Screen

**Source PNG(s):**
- `Insert - Barcode.png`
- `Insert - Barcode - Custom Setting.png` (Referenced for Custom Setting button action)

**Type:** Screen

**QML File Path:** `UI_new/Screens/InsertData/InsertBarcode.qml` (Proposed)

**Description:**
Allows configuration and insertion of a barcode field into the print message. Provides settings for barcode type, data source, and potentially custom settings.

**Key UI Elements Visible:**
- Header/Title Bar ("Barcode" and Back Button)
- Preview Area (shows sample barcode)
- Labeled Dropdown: "Barcode Type" (e.g., Code 128, QR Code, etc.)
- Labeled Input Field: "Data" (Source of barcode data)
- Button: "Custom Setting" (Likely opens `InsertBarcodeCustomSettingPopup`)
- Button: Cancel
- Button: Insert

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `PreviewDisplay`: [Area to show a dynamically updated preview] - [Status: Reuse Existing]
- `LabeledDropdown`: [Standard label + dropdown pair] - [Status: Reuse Existing]
- `LabeledTextInput`: [Standard label + text input pair] - [Status: Reuse Existing]
- `StandardButton`: [Default action button (e.g., Cancel, Custom Setting)] - [Status: Reuse Existing]
- `PrimaryButton`: [Primary action button (e.g., Insert)] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `dropdown_arrow`: [Dropdown indicator] - [Asset Source Suggestion: `vuesax/arrow-down.svg`]

**Navigation:**
- **Accessed From:** Tapping "Barcode" option on `INSERT DATA.png` screen.
- **Navigates To:**
    - `InsertBarcodeCustomSettingPopup` when tapping "Custom Setting".
    - Back to `InsertDataPage` on Cancel or Insert.
    - Back to `InsertDataPage` using the Back button.

**Data Requirements/Interaction:**
- Displays current/default barcode settings.
- User selects Barcode Type from dropdown.
- User enters Data into input field.
- Preview updates dynamically based on type and data (may require barcode generation library).
- Tapping "Custom Setting" opens the corresponding popup.
- 'Insert' action adds the configured barcode field to the message editor/layout.
- 'Cancel' discards changes.

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: Yes (For Data input)

**Special Considerations:**
- Requires a barcode generation mechanism for the preview.
- Dropdown needs to be populated with supported barcode types.
- Handling virtual keyboard overlay.

**Dependencies:**
- `ScreenHeader` component
- `PreviewDisplay` component
- `LabeledDropdown` component
- `LabeledTextInput` component
- `StandardButton` component
- `PrimaryButton` component
- `InsertBarcodeCustomSettingPopup` (Task defined)
- `InsertDataPage` (for navigation context)
- Integration with message editor logic.
- Barcode generation library/backend. 