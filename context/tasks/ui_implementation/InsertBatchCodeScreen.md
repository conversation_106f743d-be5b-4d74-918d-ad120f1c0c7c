## Task: Implement Insert Batch Code Screen

**Source PNG(s):**
- `Insert - Batch code.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/InsertData/InsertBatchCode.qml` (Proposed)

**Description:**
Allows configuration and insertion of a batch code field into the print message. Provides settings for prefix, start count, step, and total digits.

**Key UI Elements Visible:**
- Header/Title Bar ("Batch Code" and Back Button)
- Preview Area (shows example batch code)
- Labeled Input Field: "Prefix" (Text input)
- Labeled Input Field: "Start Count" (Numeric input)
- Labeled Input Field: "Step" (Numeric input)
- Labeled Input Field: "Total Digits" (Numeric input)
- Button: Cancel
- Button: Insert

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `PreviewDisplay`: [Area to show a dynamically updated preview based on settings] - [Status: Create New]
- `LabeledTextInput`: [Standard label + text input pair] - [Status: Reuse Existing]
- `LabeledNumericInput`: [Label + input specifically for numbers, possibly with steppers] - [Status: Create New | Reuse `LabeledTextInput` with validation]
- `StandardButton`: [Default action button (e.g., Cancel)] - [Status: Reuse Existing]
- `PrimaryButton`: [Primary action button (e.g., Insert)] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]

**Navigation:**
- **Accessed From:** Tapping "Batch Code" option on `INSERT DATA.png` screen.
- **Navigates To:**
    - Back to `InsertDataPage` on Cancel or Insert.
    - Back to `InsertDataPage` using the Back button.

**Data Requirements/Interaction:**
- Displays current/default batch code settings.
- User enters text/numbers into input fields.
- Preview updates dynamically based on input values.
- 'Insert' action adds the configured batch code field to the message editor/layout.
- 'Cancel' discards changes.

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: Yes (For text and numeric inputs)

**Special Considerations:**
- Input validation (e.g., Start Count, Step, Total Digits must be valid numbers, Total Digits >= digits in Start Count).
- Real-time update of the preview area.
- Handling virtual keyboard overlay.

**Dependencies:**
- `ScreenHeader` component
- `PreviewDisplay` component
- `LabeledTextInput` component
- `LabeledNumericInput` component
- `StandardButton` component
- `PrimaryButton` component
- `InsertDataPage` (for navigation context)
- Integration with message editor logic. 