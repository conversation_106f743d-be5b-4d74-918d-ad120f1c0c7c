## Task: Implement Service Status Screen

**Source PNG(s):**
- `Service - Status.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/Service/ServiceStatusPage.qml` (Proposed)

**Description:**
Displays a dashboard of various printer status parameters relevant during service mode.

**Key UI Elements Visible:**
- Header/Title Bar ("Status" and Back Button)
- Grid or list of status indicators/cards.
- Each indicator shows a Label (e.g., "Ink Level", "Pump Pressure", "Voltage") and its current Value.
- May include status icons or color coding.

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `StatusGrid` or `StatusList`: [Container for the indicators] - [Status: Create New]
- `StatusIndicatorCard`: [Component displaying label, value, potentially icon/unit/color] - [Status: Reuse Existing | Create New]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- Icons for specific statuses (ink, pressure, voltage, temperature etc.) - [Asset Source Suggestion: Needs identification]

**Navigation:**
- **Accessed From:** Likely from the main `Service.png` screen (e.g., tapping a "Status" option).
- **Navigates To:**
    - Back to `ServicePage` using the Back button.

**Data Requirements/Interaction:**
- Fetches and displays various real-time status values from the backend.
- Values are read-only and update dynamically.

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: No

**Special Considerations:**
- Real-time data updates are crucial.
- Layout should accommodate numerous status values clearly.
- Consider using color or icons to highlight abnormal statuses.
- Requires backend integration to retrieve all status values.

**Dependencies:**
- `ScreenHeader` component
- `StatusGrid` / `StatusList` component
- `StatusIndicatorCard` component
- Icon assets for statuses
- `ServicePage` (for navigation context)
- Backend integration for status data. 