## Task: Implement Printer Setting List Screen

**Source PNG(s):**
- `Printer Setting.png`
- `Printer Setting - Edit view.png` (Shows Edit mode)
- `Printer Setting - Delete view.png` (Shows Delete mode)

**Type:** Screen

**QML File Path:** `UI_new/Screens/Settings/PrinterSettingListPage.qml` (Proposed)

**Description:**
Displays a list of saved printer setting profiles. Allows creating new profiles, and entering modes to edit or delete existing profiles.

**Key UI Elements Visible:**
- Header/Title Bar ("Printer Setting" and Back Button)
- Action Buttons in Header/Subheader? (Create, Edit, Delete)
- List of Printer Setting Profiles
    - Each item shows Profile Name, possibly some key settings.
    - In Edit/Delete modes, shows RadioButton/Checkbox respectively.
- Bottom Action Bar (Only visible in Edit/Delete modes)

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `HeaderActions`: [Container for action buttons like Create/Edit/Delete in header area] - [Status: Create New]
- `IconButton`: [For Create, Edit, Delete actions] - [Status: Reuse Existing]
- `PrinterSettingListView`: [Scrollable list view for settings] - [Status: Create New]
- `PrinterSettingListItemDelegate`: [Delegate for list item, adaptable for normal/edit/delete modes] - [Status: Create New]
- `BottomActionBar`: [Container for bottom buttons, visible in edit/delete modes] - [Status: Reuse Existing]
- `StandardButton`: [Cancel button in action bar] - [Status: Reuse Existing]
- `PrimaryButton`: [Edit Selected button in action bar] - [Status: Reuse Existing]
- `DestructiveButton`: [Delete Selected button in action bar] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `add_icon`: [Create New Setting] - [Asset Source Suggestion: `vuesax/add-square.svg`]
- `edit_icon`: [Enter Edit Mode] - [Asset Source Suggestion: `vuesax/edit.svg`]
- `delete_icon`: [Enter Delete Mode] - [Asset Source Suggestion: `vuesax/trash.svg`]
- `radio_unchecked`/`radio_checked`: [Edit Mode] - [Asset Source Suggestion: Needs identification/creation]
- `checkbox_unchecked`/`checkbox_checked`: [Delete Mode] - [Asset Source Suggestion: `vuesax/tick-square.svg` variants]

**Navigation:**
- **Accessed From:** Likely from `System` or `Service` screen.
- **Navigates To:**
    - `PrinterSettingCreateForm` (Task needed) when tapping Create icon.
    - Enters 'Edit Mode' (UI change) when tapping Edit icon.
    - Enters 'Delete Mode' (UI change) when tapping Delete icon.
    - `PrinterSettingEditForm` (Task needed) via 'Edit Selected' button in Edit Mode.
    - `PrinterSettingDeletePopup` (Task defined) via 'Delete Selected' button in Delete Mode.
    - Back to previous screen using the Back button.

**Data Requirements/Interaction:**
- Fetches and displays the list of saved printer setting profiles.
- Tapping Create navigates to a form.
- Tapping Edit/Delete toggles the screen mode (showing checkboxes/radios and bottom bar).
- In Edit mode, allows single selection and navigation to edit form.
- In Delete mode, allows multiple selection and triggering delete confirmation.

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: No (Needed on Create/Edit forms)

**Special Considerations:**
- Managing the different view states (normal, edit selection, delete selection) within the same QML file.
- Conditionally displaying list item controls (none, radio, checkbox) and the bottom action bar.
- Backend integration for retrieving, creating, editing, deleting setting profiles.

**Dependencies:**
- All sub-components listed above.
- `PrinterSettingCreateForm` (Task needed)
- `PrinterSettingEditForm` (Task needed)
- `PrinterSettingDeletePopup` (Task defined)
- Backend integration. 