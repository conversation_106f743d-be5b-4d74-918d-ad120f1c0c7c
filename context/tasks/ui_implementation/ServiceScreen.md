## Task: Implement Service Main Screen

**Source PNG(s):**
- `Service.png`
- `Service - For Guest User.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/Service/ServicePage.qml` (Proposed)

**Description:**
The main entry point for service-related functions. Provides access to status, specific functions, settings adjustments, etc. Functionality is reduced for Guest users.

**Key UI Elements Visible:**
- Grid or List of Service Options/Cards:
    - Status
    - Function
    - Phase
    - Reset Maintenance
    - Service Mode
    - (Other options?)
- Each option has an icon and text label.
- Some options might be disabled/hidden in Guest Mode (`Service - For Guest User.png`).
- Bottom Navigation Bar (`BottomBar`)

**Sub-Components Needed (Create or Reuse):**
- `OptionCard`: [Clickable card/button component] - [Status: Reuse Existing | Needs Verification]
- `BottomBar`: [Global navigation bar] - [Status: Reuse Existing - Task defined]

**Icons Needed (Specific List):**
- `status_icon`: [Service Status option] - [Asset Source Suggestion: `vuesax/activity.svg`?]
- `function_icon`: [Service Function option] - [Asset Source Suggestion: `vuesax/setting-3.svg`?]
- `phase_icon`: [Phase option] - [Asset Source Suggestion: Needs identification]
- `reset_maintenance_icon`: [Reset Maintenance option] - [Asset Source Suggestion: `vuesax/refresh-circle.svg`?]
- `service_mode_icon`: [Service Mode option] - [Asset Source Suggestion: `vuesax/slider.svg`?]
- Other service option icons...
- Icons for `BottomBar` (Home, Print, Service, System) - [Asset Source Suggestion: `vuesax/*`]

**Navigation:**
- **Accessed From:** `BottomBar`, potentially Login screen if service requires login.
- **Navigates To:**
    - `ServiceStatusPage` (Task defined) via Status option.
    - `ServiceFunctionPage` (Task defined) via Function option.
    - `ServicePhaseScreen` (Task defined) via Phase option.
    - `ServiceResetMaintenanceScreen` (Task defined) via Reset Maintenance option.
    - `ServiceModeScreen` (Task defined) via Service Mode option.
    - Other main sections (`Home`, `Print`, `System`) via `BottomBar`.
    - `LoginPage` (Task defined) potentially accessible via a dedicated button elsewhere if login is required for actions.
    - Possibly `EnteringServiceModePopup` before navigating to sub-screens if login/mode switch required.

**Data Requirements/Interaction:**
- Displays available service categories.
- Tapping an option navigates to the corresponding sub-screen.
- Checks user permissions (Guest vs. Logged-in) to enable/disable/hide options.

**Global Components Used:**
- `BottomBar`: Yes
- `VirtualKeyboard`: No

**Special Considerations:**
- Handling permissions and disabling/hiding options for Guest users.
- Requires clear visual distinction for disabled options.
- No global App Header is used; content starts below the system status bar area.

**Dependencies:**
- `OptionCard` component
- `BottomBar` component
- All Service sub-screens (`ServiceStatusPage`, `ServiceFunctionPage`, etc.)
- Icon assets for each service option.
- Login/Permission system integration. 