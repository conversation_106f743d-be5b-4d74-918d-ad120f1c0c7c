## Task: Implement Users & Permissions Delete View Screen

**Source PNG(s):**
- `System - Users & Permissions - Delete Selected.png`

**Type:** Screen (variant of Users & Permissions List)

**QML File Path:** `UI_new/Screens/System/UsersPermissions/SystemUserPermissionsPage.qml` (Likely modifies existing list page state)

**Description:**
A view of the Users & Permissions list screen specifically for deleting users. Checkboxes appear next to each user, and a 'Delete Selected' button is shown.

**Key UI Elements Visible:**
- Header/Title Bar ("Users & Permissions" and Back Button)
- List of Users
    - Each item has a Checkbox, Username, Permission Level.
- Button Bar at bottom:
    - Button: Cancel (exits delete mode)
    - Button: Delete Selected (triggers deletion popup)

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `UserListView`: [Scrollable list view for users] - [Status: Reuse Existing | Needs modification for delete mode]
- `UserListItemDelegate`: [Delegate for list item] - [Status: Reuse Existing | Needs modification for checkbox]
- `StandardButton`: [Cancel button] - [Status: Reuse Existing]
- `DestructiveButton`: ['Delete Selected' button] - [Status: Reuse Existing]
- `BottomActionBar`: [Container for bottom buttons] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `checkbox_unchecked`: [Checkbox state] - [Asset Source Suggestion: `vuesax/tick-square.svg` variant]
- `checkbox_checked`: [Checkbox state] - [Asset Source Suggestion: `vuesax/tick-square.svg` variant]

**Navigation:**
- **Accessed From:** Tapping a 'Delete' action/button on the main `System - Users & Permissions.png` screen.
- **Navigates To:**
    - `DeleteUserPopup` (Task defined) when tapping 'Delete Selected'.
    - Back to the normal `SystemUserPermissionsPage` view when tapping 'Cancel'.
    - Back to the `SystemPage` using the Back button.

**Data Requirements/Interaction:**
- Displays the list of users.
- Allows user to select multiple users via checkboxes.
- Cannot select the currently logged-in user? (Rule to consider)
- 'Delete Selected' button is likely enabled only when at least one item is checked.
- Tapping 'Delete Selected' triggers the confirmation popup, passing the selected user IDs.
- 'Cancel' returns the list view to its normal state.

**Global Components Used:**
- `BottomBar`: No (Replaced by `BottomActionBar` in this mode)
- `VirtualKeyboard`: No

**Special Considerations:**
- This is likely a *mode* of the main `SystemUserPermissionsPage`.
- Need to manage the selection state of list items.
- Conditional visibility of checkboxes and the bottom action bar.
- Prevent deletion of the current user or the last admin user.

**Dependencies:**
- `ScreenHeader` component
- `UserListView` component (and its delegate)
- `StandardButton` component
- `DestructiveButton` component
- `BottomActionBar` component
- `DeleteUserPopup` (Task defined)
- `SystemUserPermissionsPage` (needs definition, this task modifies it)
- Backend integration for retrieving user list. 