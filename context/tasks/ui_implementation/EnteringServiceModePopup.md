## Task: Implement Entering Service Mode Popup

**Source PNG(s):**
- `Entering Service Mode - Pop up.png`

**Type:** Popup

**QML File Path:** `UI_new/Components/Popups/EnteringServiceModePopup.qml` (Proposed)

**Description:**
A popup indicating that the system is transitioning into Service Mode. It might show a progress indicator or simply a status message.

**Key UI Elements Visible:**
- Popup Container
- Icon (Settings/Gear icon?)
- Title: "Entering Service Mode"
- Body Text/Status: "Please wait..." or similar.
- Animated Indicator (Spinner?)

**Sub-Components Needed (Create or Reuse):**
- `PopupBase`: [Base component for modal popups] - [Status: Reuse Existing]
- `StatusIcon`: [Component to display icons like settings/gear] - [Status: Reuse Existing]
- `AnimatedIndicator`: [Spinner or progress animation] - [Status: Reuse Existing | Create New]

**Icons Needed (Specific List):**
- `settings_icon`: [Indicator for service/settings action] - [Asset Source Suggestion: `vuesax/setting-2.svg`]
- `spinner_icon`: [Animated indicator] - [Asset Source Suggestion: Needs creation or library]

**Navigation:**
- **Accessed From:** Tapping the "Service" option on the `BottomBar` or potentially a specific button elsewhere (e.g., on `ServicePage` if login is required first).
- **Navigates To:**
    - Closes automatically and likely navigates to the main `ServicePage` once the mode transition is complete.

**Data Requirements/Interaction:**
- Displays a status message while the backend prepares service mode.
- Triggered by user action requesting service mode.
- Closes upon receiving a signal from the backend that service mode is ready.

**Global Components Used:**
- `VirtualKeyboard`: No

**Special Considerations:**
- This popup acts as a loading/transition indicator.
- Requires backend signal to know when to close and proceed.
- Should block interaction with the underlying screen.

**Dependencies:**
- `PopupBase` component
- `StatusIcon` component
- `AnimatedIndicator` component
- `ServicePage` (Target screen)
- `BottomBar` or other triggering component.
- Backend integration for service mode transition. 