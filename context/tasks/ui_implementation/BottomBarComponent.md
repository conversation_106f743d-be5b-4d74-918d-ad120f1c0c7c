## Task: Implement Bottom Navigation Bar

**Source PNG(s):**
- `bottomBarVarients.png`
- Referenced in: `Home.png`, `Print.png`, `Service.png`, `System.png`

**Type:** Global Component

**QML File Path:** `UI_new/Components/Navigation/BottomBar.qml` (Proposed)

**Description:**
The main navigation component located at the bottom of the primary application screens (Home, Print, Service, System).

**Key UI Elements Visible:**
- Container bar (background color, possibly border/shadow).
- Four Navigation Buttons/Tabs:
    - Home
    - Print
    - Service
    - System
- Each button has an Icon and a Text Label.
- Visual indication for the currently active/selected screen (e.g., different icon/text color, background highlight).

**Sub-Components Needed (Create or Reuse):**
- `BottomBarButton`: [Individual button component containing icon and label, handles active state] - [Status: Create New]

**Icons Needed (Specific List):**
- `home_icon`: [Home navigation] - [Asset Source Suggestion: `vuesax/home-2.svg`]
- `print_icon`: [Print navigation] - [Asset Source Suggestion: `vuesax/printer.svg`]
- `service_icon`: [Service navigation] - [Asset Source Suggestion: `vuesax/setting-2.svg`]
- `system_icon`: [System navigation] - [Asset Source Suggestion: `vuesax/cpu-setting.svg`? or `sidebar-left.svg`?]

**Navigation:**
- **Accessed From:** Visible on main screens (`HomePage`, `PrintPage`, `ServicePage`, `SystemPage`).
- **Navigates To:**
    - Corresponding main screen (`HomePage`, `PrintPage`, `ServicePage`, `SystemPage`) when a button is tapped.

**Data Requirements/Interaction:**
- Needs to know the currently active screen to highlight the correct button.
- Tapping a button signals the main layout/navigation controller to switch screens.

**Global Components Used:**
- This *is* a global component.

**Special Considerations:**
- Consistent styling across all main screens.
- Clear visual feedback for the active state.
- Touch targets for buttons must meet minimum size requirements.
- Needs integration with the main application layout (`MainLayout.qml`) for navigation control.

**Dependencies:**
- `BottomBarButton` component.
- Icon assets for each section.
- `MainLayout.qml` or navigation controller. 