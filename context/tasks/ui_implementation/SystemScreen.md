## Task: Implement System Main Screen

**Source PNG(s):**
- `System.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/System/SystemPage.qml` (Proposed)

**Description:**
The main entry point for system-level settings and information.

**Key UI Elements Visible:**
- Grid or List of System Options/Cards:
    - Date & Time
    - Language
    - Users & Permissions
    - System Info
    - QR Data
    - Printer Setting
    - Error log
    - Others
- Each option has an icon and text label.
- Bottom Navigation Bar (`BottomBar`)

**Sub-Components Needed (Create or Reuse):**
- `OptionCard`: [Clickable card/button component] - [Status: Reuse Existing | Needs Verification]
- `BottomBar`: [Global navigation bar] - [Status: Reuse Existing - Task defined]

**Icons Needed (Specific List):**
- `datetime_icon`: [Date & Time option] - [Asset Source Suggestion: `vuesax/calendar.svg`]
- `language_icon`: [Language option] - [Asset Source Suggestion: `vuesax/language-square.svg`]
- `users_icon`: [Users & Permissions option] - [Asset Source Suggestion: `vuesax/profile-2user.svg`]
- `info_icon`: [System Info option] - [Asset Source Suggestion: `vuesax/info-circle.svg`]
- `qr_icon`: [QR Data option] - [Asset Source Suggestion: `vuesax/scan.svg`]
- `printer_settings_icon`: [Printer Setting option] - [Asset Source Suggestion: `vuesax/printer.svg`]
- `errorlog_icon`: [Error log option] - [Asset Source Suggestion: `vuesax/document-text.svg`? or `warning-2.svg`?]
- `others_icon`: [Others option] - [Asset Source Suggestion: `vuesax/element-3.svg`? or `menu.svg`?]
- Icons for `BottomBar` (Home, Print, Service, System) - [Asset Source Suggestion: `vuesax/*`]

**Navigation:**
- **Accessed From:** `BottomBar`.
- **Navigates To:**
    - `SystemDateTimePage` (Task defined) via Date & Time option.
    - `SystemLanguagePage` (Task defined) via Language option.
    - `SystemUserPermissionsPage` (Task defined) via Users & Permissions option.
    - `SystemInfoPage` (Task defined) via System Info option.
    - `SystemQRDataPage` (Task defined) via QR Data option.
    - `PrinterSettingListPage` (Task defined) via Printer Setting option.
    - `ErrorLogPage` (Task defined) via Error log option.
    - `SystemOthersPage` (Task defined) via Others option.
    - Other main sections (`Home`, `Print`, `Service`) via `BottomBar`.
    - `LoginPage` (Task defined) potentially accessible via a dedicated button elsewhere if login is required for actions.

**Data Requirements/Interaction:**
- Displays available system setting categories.
- Tapping an option navigates to the corresponding sub-screen.
- May need to check permissions for certain options (e.g., Users & Permissions).

**Global Components Used:**
- `BottomBar`: Yes
- `VirtualKeyboard`: No

**Special Considerations:**
- Handling permissions for accessing different system sections.
- No global App Header is used; content starts below the system status bar area.

**Dependencies:**
- `OptionCard` component
- `BottomBar` component
- All System sub-screens.
- Icon assets for each system option.
- Login/Permission system integration. 