# UI Implementation Plan Overview

## Goal

The primary goal is to systematically implement the user interface screens, popups, and global components for the Light application based on the provided PNG mockups located in `context/ui_png`. This process aims to translate the visual designs into functional QML code adhering to the project's established standards and guidelines.

## Process & Guidelines

To ensure an efficient, tracked, and maintainable implementation:

1.  **Task-Based Approach:** Each major screen, popup, or distinct view mode has a dedicated task file (e.g., `HomeScreen.md`, `AddUserPopup.md`) within this directory. These files contain detailed requirements, identified sub-components, needed icons, and navigation context based on the source PNG(s).
2.  **Detailed Task Files:** Refer to the individual Markdown files for the specific requirements of each implementation task.
3.  **Theming:** Strictly adhere to the centralized Theme system (`UI_new/Theme/`). Use `Theme.Colors`, `Theme.Typography`, `Theme.Spacing`, and `Theme.Shadows` for all styling. **Do not use hardcoded colors, fonts, or dimensions.**
4.  **Reusable Components:** Prioritize creating and reusing components (e.g., `StandardButton`, `LabeledTextInput`, `ScreenHeader`). Identify common elements early and build them as separate, reusable QML files, placing them in `UI_new/Components/`.
5.  **Global Components:** Implement global components like `BottomBar` first, as it is used across multiple screens. **Note:** There is no global "App Header"; screens typically start with specific content (like print preview) or section grids directly below the system status bar.
6.  **Asset Usage:** Utilize icons and other assets referenced in the task files, primarily from directories like `context/ui_png/vuesax/` and provided SVGs.
7.  **Tracking:** Use the checklist below to track the completion status of each task.
8.  **Documentation & Rules:** Refer to `docs/ui_guidelines.md` and `.codeium/rules.md` for detailed UI standards and project rules.
9.  **Figma CSS Reference:** If the PNGs and task specifications lack sufficient detail for accurate implementation of an element, I will prompt the USER to provide the specific style details (colors, dimensions, fonts, shadows) from the Figma Inspect tab (or copied CSS) for that element.
10. **Enhancing Clarity for Implementation:** To further reduce ambiguity for the implementing agent/developer:
    *   Confirm exact **component names** and reuse strategy before starting a task.
    *   Provide specific **layout hints** (e.g., `GridLayout`, `Theme.Spacing.medium` between items) where appropriate.
    *   Define expected **signals/slots** for component interactions.
    *   Clarify **state variables** for screens with multiple modes (e.g., normal/edit/delete).
    *   Verify the exact **icon asset paths**.
    *   Specify input **properties** and output **signal parameters** for data flow.
11. Use nav.pdf in this dir to understand the nav of the app. It is mostly up-to-date. If you find a contradition ask me about it.
12. Before you start making changes or adding new files, please list and explore dir and sub dirs of `UI_new/` so you don't recreate files
13. Look inside context/ui_png for PNGs and SVGs and any other assests.
14. 

## Implementation Checklist

- [ ] AddUserPage
- [ ] BottomBarComponent
- [ ] DeleteMessagePopup
- [ ] DeleteUserPopup
- [ ] EditPermissionsPopup
- [ ] EnteringServiceModePopup
- [ ] ErrorLogScreen
- [ ] HomeScreen
- [ ] InsertBarcodeCustomSettingPopup
- [ ] InsertBarcodeScreen
- [ ] InsertBatchCodeScreen
- [ ] InsertCounterScreen
- [ ] InsertDataScreen
- [ ] InsertDateTimeJulianPopup
- [ ] InsertDateTimeOverridesPopup
- [ ] InsertMeteringScreen
- [ ] InsertShiftCodeScreen
- [ ] InsertTextScreen
- [ ] LoginScreen
- [ ] PrintScreen
- [ ] PrintUSBScreen
- [ ] PrinterSettingDeletePopup
- [ ] PrinterSettingDeleteView
- [ ] PrinterSettingEditView
- [ ] PrinterSettingListScreen
- [ ] ServiceFunctionNozzlePopup
- [ ] ServiceFunctionScreen
- [ ] ServiceModeScreen
- [ ] ServicePhaseScreen
- [ ] ServiceResetMaintenanceScreen
- [ ] ServiceScreen
- [ ] ServiceStatusScreen
- [ ] ShutDownPopup
- [ ] SystemDateTimeScreen
- [ ] SystemInfoScreen
- [ ] SystemLanguageScreen
- [ ] SystemOthersScreen
- [ ] SystemQRDataScreen
- [ ] SystemScreen
- [ ] TestPrintPopup
- [ ] UserPermissionsDeleteView
- [ ] UserPermissionsEditView
- [ ] UserPermissionsListScreen 