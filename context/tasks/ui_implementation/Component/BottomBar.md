## Task 0004: Implement Bottom Bar Component (`BottomBar.qml`)

**Figma Reference:**  
- [Bottom Bar Variants Screenshot](https://www.figma.com/design/IHjEQ1obTPsKI2XMUVkawt/354---ilaue_prospr---Software?node-id=227-1222&t=Ygqcu3Mf3r63YHhu-4)
- [Prototype](https://www.figma.com/proto/IHjEQ1obTPsKI2XMUVkawt/354---ilaue_prospr---Software?node-id=629-7075&t=86ZZ0y0S44iDx0za-1&scaling=scale-down&content-scaling=fixed&page-id=1%3A5&starting-point-node-id=1054%3A10554)

**Type:** Global Component

**QML File Path:**  
- `UI/Components/BottomBar.qml`  
- Subcomponents: `UI/Components/bottombar/NavigationButtons.qml`, `ActionIcons.qml`, `StatusIndicators.qml`, `TimeAndDateDisplay.qml`, `BackButton.qml`

---

### Description

The Bottom Bar is a core, global navigation and status component visible on nearly every main screen of the Prospr Light application. It provides primary navigation, system status, and quick access to error logs, user profile, and home. Its visibility and content are context-sensitive, adapting to the needs of each screen (e.g., hidden on login, minimal on user info).

---

### Key UI Elements & Layout

- **Status Indicators:**  
  Always visible when the Bottom Bar is shown. Shows system, warning, and error states with colored dots (green/yellow/red).
- **Navigation Buttons:**  
  - SERVICE, PRINT, SYSTEM (with icons and labels)
  - Highlight the active section in Prospr Orange (`Theme.Colors.primary`)
  - Buttons may be hidden or disabled on certain screens (e.g., Login, UserInfo)
- **Action Icons (always shown):**  
  - Error Log (warning triangle, orange)
  - User (solid/outline depending on login state)
  - Home (house icon, always shown)
- **Date & Time:**  
  - Always visible in the rightmost section, formatted as per Figma
- **Back Button:**  
  - Shown instead of nav buttons on certain screens (e.g., AddUser, UserInfo)
- **Layout:**  
  - Responsive, with proper spacing, touch targets (min 50-60px), and high-contrast colors per industrial UI/UX standards

---

### Sub-Components Needed (Create or Reuse)

- `StatusIndicators.qml`: 3-dot status (system, warning, error)
- `NavigationButtons.qml`: Handles SERVICE, PRINT, SYSTEM; highlights active
- `ActionIcons.qml`: Error, User, Home (always present)
- `TimeAndDateDisplay.qml`: Date/time, auto-updating
- `BackButton.qml`: For screens with back navigation only

---

### Icons Needed

- SERVICE: grid/wrench icon
- PRINT: printer icon
- SYSTEM: gear/cpu icon
- ERROR: warning triangle
- USER: person (solid/outline)
- HOME: house

---

### Navigation & State

- **Accessed From:**  
  All main screens except where explicitly hidden (e.g., Login, certain popups)
- **Navigation:**  
  - Tapping a nav button switches screens (emit `navigateToRequested(to, from)` signal)
  - Back button triggers `goBack()`
  - Error/User/Home icons emit their respective signals
- **State:**  
  - Only one nav button can be active at a time (highlighted in orange)
  - Nav buttons may be hidden/disabled (e.g., on Login/UserInfo)
  - Status indicators and action icons always visible when BottomBar is shown
  - Date/time always shown when BottomBar is visible

---

### Data Requirements/Interaction

- Receives current screen name to determine which nav button is active
- Receives status data (system/warning/error) for indicators
- Receives login state for user icon (solid/outline)
- Date/time updates automatically
- Emits signals for navigation and actions

---

### Special Considerations

- **Touch targets:** All interactive elements must be at least 50px high for glove compatibility
- **Contrast:** Use high-contrast colors for icons/text per industrial/factory UI/UX
- **Responsiveness:** Layout must adapt to 1920x1080 and maintain spacing/size
- **Abstraction:**  
  - The component must be programmable: props for hiding nav, showing back, disabling buttons, etc.
  - Designed for re-use in nearly every screen; must not assume any specific parent or navigation logic
  - Should degrade gracefully if certain props/data are missing

---

### States & Variants

- **Default:**  
  Status indicators, nav buttons, action icons, date/time all visible
- **Back Only:**  
  Back button replaces nav buttons, action icons and date/time still shown
- **Minimal:**  
  Only status indicators, action icons, and date/time (e.g., on UserInfo)
- **Hidden:**  
  Entire BottomBar hidden (e.g., Login page)
- **Disabled:**  
  Nav buttons may be disabled (e.g., after logout)

---

### In General

- Match Figma for spacing, color, and typography
- Do not implement backend logic—focus on UI, navigation, and signals
- Reference [Theme Info](../ThemeInfo.md) for color/typography standards
- Ensure extensibility for future buttons or states
