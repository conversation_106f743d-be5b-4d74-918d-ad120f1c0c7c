## Task 0003: Implement Home Page

**Figma Reference:**
- [Home Page Screenshot](https://www.figma.com/design/IHjEQ1obTPsKI2XMUVkawt/354---ilaue_prospr---Software?node-id=216-797&t=Ygqcu3Mf3r63YHhu-4)  

**Type:** Screen

**QML File Path:** `UI/Screens/Home.qml`

---

### Description
The Home Page is the main landing screen of the Prospr Light application. It displays the Prospr logo and branding over a background image or video, with a prominent power button for shutdown, and a bottom navigation bar for main app sections. The layout is optimized for a 1920x1080 industrial touchscreen, with large touch targets and high contrast for factory environments.

---

### Key UI Elements Visible
- Prospr logo (centered, large, white, with tagline and slogan)
- Background image or video (factory/industrial context, slightly dimmed for contrast)
- Power button (large, orange, bottom right, with white power icon and drop shadow)
- BottomBar navigation:
  - Service, Print, System buttons (large, touch-optimized, with icons)
  - Status indicators (e.g., network, printer)
  - Alert/warning icon
  - Home icon
  - Date and time display (bottom right)
- REMOVE Help button (orange circle with question mark, top right)


---

### Sub-Components Needed (Create or Reuse)
- `LogoBrand`: [Displays Prospr logo, tagline, slogan]
- `PowerButton`: [Large circular button with power icon and shadow]
- `BottomBar`: [Navigation bar with main section buttons, status, and time]
- `StatusIndicator`: [For network/printer status, if not in BottomBar]

---

### Icons Needed (Specific List)
- `power`: [White power icon for shutdown button]
- `service`, `print`, `system`, `home`: [Navigation icons for BottomBar]
- `warning`: [Alert icon]
- Status indicator icons (network, printer, etc.)

---

[Figma Prototype](https://www.figma.com/proto/IHjEQ1obTPsKI2XMUVkawt/354---ilaue_prospr---Software?node-id=629-7075&t=86ZZ0y0S44iDx0za-1&scaling=scale-down&content-scaling=fixed&page-id=1%3A5&starting-point-node-id=1054%3A10554) 
### Navigation
- **Accessed From:**
  - After login, or as the default landing screen if no login required.
  - Navigable from anywhere via the Home icon in BottomBar.
- **Navigates To:**
  - Service, Print, System pages (via BottomBar)
  - Login page (if user logs out)
  - Shutdown dialog (via Power button)

---

### Data Requirements/Interaction
- Displays current date and time (auto-updating)
- Shows current status of printer/network (if available)
- Power button triggers shutdown dialog and system shutdown
- BottomBar buttons navigate between main app sections
- Touch targets must be large and accessible (min 50-60px height)

---

### Global Components Used
- `BottomBar`: Yes
- `LogoBrand`: Yes

---

### Special Considerations
- All touch targets must be at least 50px high for glove compatibility
- Use high-contrast colors and large fonts per industrial UI/UX standards
- Responsive layout for 1920x1080 touchscreen
- Background should not obscure text/logo (dim or blur as needed)
- Power button and Home navigation must be easily accessible
- Status indicators and alerts should be clear and prominent

---

### In General
- Match the Figma design for spacing, color, and typography
- Do not implement backend logic—focus on UI, navigation, and signals
- Reference [Theme Info](../ThemeInfo.md) for color/typography standards
