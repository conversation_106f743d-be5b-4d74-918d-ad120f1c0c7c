## Task: Implement System Date & Time Screen

**Source PNG(s):**
- `System - Date & Time.png`
- `System - Date & Time-1.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/System/SystemDateTimePage.qml` (Proposed)

**Description:**
Allows viewing and setting the system's date, time, and time zone.

**Key UI Elements Visible:**
- Header/Title Bar ("Date & Time" and Back Button)
- Date Setting Area:
    - Displays current date.
    - Buttons/Inputs to change Day, Month, Year (possibly via calendar popup).
- Time Setting Area:
    - Displays current time.
    - Buttons/Inputs to change Hour, Minute, Second (possibly via time picker popup).
- Time Zone Selection Dropdown.
- Toggle Switch: "Automatic Date & Time" (disables manual settings when ON).
- Button: Save/Apply

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `DateTimeField`: [Component to display/edit date or time parts] - [Status: Create New]
- `DatePickerPopup`: [Popup for selecting date from a calendar] - [Status: Create New]
- `TimePickerPopup`: [Popup for selecting time] - [Status: Create New]
- `LabeledDropdown`: [Dropdown for Time Zone] - [Status: Reuse Existing]
- `LabeledToggleSwitch`: [Toggle for Automatic Date & Time] - [Status: Create New | Reuse Standard Switch]
- `PrimaryButton`: [Save/Apply button] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `calendar_icon`: [Button to open DatePicker] - [Asset Source Suggestion: `vuesax/calendar-edit.svg`?]
- `clock_icon`: [Button to open TimePicker] - [Asset Source Suggestion: `vuesax/clock.svg`?]
- `dropdown_arrow`: [Time Zone dropdown] - [Asset Source Suggestion: `vuesax/arrow-down.svg`]

**Navigation:**
- **Accessed From:** Likely from the main `System.png` screen (e.g., tapping "Date & Time").
- **Navigates To:**
    - `DatePickerPopup` (Task needed) when tapping date fields.
    - `TimePickerPopup` (Task needed) when tapping time fields.
    - Back to `SystemPage` using the Back button (possibly after saving).

**Data Requirements/Interaction:**
- Displays current system date, time, and time zone.
- Allows enabling/disabling automatic time synchronization.
- When automatic is OFF, allows manual setting of date, time, and time zone.
- Manual setting might involve popups or direct input.
- 'Save/Apply' button saves the settings to the system via the backend.

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: Yes (Potentially for direct input in popups)

**Special Considerations:**
- Handling the enable/disable state of manual controls based on the toggle switch.
- Populating the Time Zone dropdown.
- Requires backend integration to get/set system time and settings.

**Dependencies:**
- `ScreenHeader` component
- `DateTimeField` component
- `DatePickerPopup` (Task needed)
- `TimePickerPopup` (Task needed)
- `LabeledDropdown` component
- `LabeledToggleSwitch` component
- `PrimaryButton` component
- `SystemPage` (for navigation context)
- Backend integration for time settings. 