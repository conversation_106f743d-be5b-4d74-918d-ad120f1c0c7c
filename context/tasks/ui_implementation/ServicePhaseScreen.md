## Task: Implement Service Phase Screen

**Source PNG(s):**
- `Service - Phase.png`
- `Service - Phase-1.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/Service/ServicePhasePage.qml` (Proposed)

**Description:**
Allows viewing and potentially adjusting printer phase settings or performing phase-related service operations.

**Key UI Elements Visible:**
- Header/Title Bar ("Phase" and Back Button)
- Multiple Input Fields/Display Fields with Labels (e.g., "Phase Adjust", "Current Phase")
- Action Buttons (e.g., "Adjust", "Start Sequence"?)
- Possibly status indicators or graphs (less clear from static PNGs).

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `LabeledNumericInput` or `LabeledDisplayField`: [Component for showing/editing phase values] - [Status: Reuse Existing | Create New]
- `ActionButton`: [Buttons for phase adjustments or actions] - [Status: Reuse Existing `StandardButton`/`PrimaryButton`]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- Potentially icons related to phase or adjustment - [Asset Source Suggestion: Needs identification]

**Navigation:**
- **Accessed From:** Likely from the main `Service.png` screen (e.g., tapping a "Phase" option).
- **Navigates To:**
    - Back to `ServicePage` using the Back button.

**Data Requirements/Interaction:**
- Displays current phase values/status retrieved from the backend.
- Allows user to input new values for adjustment (if applicable).
- Buttons trigger specific phase-related service actions via the backend.
- Values may update dynamically based on printer state or actions.

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: Yes (If phase values are directly editable)

**Special Considerations:**
- Real-time data updates for phase values/status.
- Input validation if values are adjustable.
- Safety implications of phase adjustments/actions.
- Handling virtual keyboard overlay.

**Dependencies:**
- `ScreenHeader` component
- `LabeledNumericInput` / `LabeledDisplayField` component
- `ActionButton` / `StandardButton` / `PrimaryButton` components
- `ServicePage` (for navigation context)
- Backend integration for phase data and control. 