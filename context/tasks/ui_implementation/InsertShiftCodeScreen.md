## Task: Implement Insert Shift Code Screen

**Source PNG(s):**
- `Insert - Shift.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/InsertData/InsertShiftCode.qml` (Proposed)

**Description:**
Allows configuration and insertion of a shift code field into the print message. Seems to involve setting up multiple shifts with start/end times and corresponding codes.

**Key UI Elements Visible:**
- Header/Title Bar ("Shift Code" and Back Button)
- List or Table Area displaying defined shifts (e.g., "Shift 1", "Shift 2", "Shift 3")
- Each shift entry shows: Name, Start Time, End Time, Code
- Buttons: Edit (per shift?), Add Shift?
- Button: Cancel
- Button: Insert

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `ShiftListItem`: [Component to display details of one shift in the list, potentially with an edit button] - [Status: Create New]
- `StandardButton`: [Default action button (e.g., Cancel)] - [Status: Reuse Existing]
- `PrimaryButton`: [Primary action button (e.g., Insert)] - [Status: Reuse Existing]
- `AddButton`: [Button to add a new shift] - [Status: Create New | Reuse Existing]
- `EditButton`: [Button to edit an existing shift] - [Status: Create New | Reuse Existing]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `edit_icon`: [Edit shift] - [Asset Source Suggestion: `vuesax/edit.svg`]
- `add_icon`: [Add shift] - [Asset Source Suggestion: `vuesax/add-square.svg`]

**Navigation:**
- **Accessed From:** Tapping "Shift Code" option on `INSERT DATA.png` screen.
- **Navigates To:**
    - Potentially an "Edit Shift" screen when tapping Edit.
    - Potentially an "Add Shift" screen when tapping Add.
    - Back to `InsertDataPage` on Cancel or Insert.
    - Back to `InsertDataPage` using the Back button.

**Data Requirements/Interaction:**
- Displays a list of currently configured shifts with their details.
- Allows editing or adding shifts (navigating to other screens/popups).
- 'Insert' action likely adds a placeholder for the *currently active* shift code based on the system time and configured shifts.
- 'Cancel' discards changes.

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: Likely needed on the Add/Edit Shift screens.

**Special Considerations:**
- Requires logic to determine the currently active shift based on time.
- Need Add/Edit Shift screens or popups defined.
- List might need to be scrollable if many shifts are defined.

**Dependencies:**
- `ScreenHeader` component
- `ShiftListItem` component
- `StandardButton` component
- `PrimaryButton` component
- `AddButton` / `EditButton` components
- Add/Edit Shift screens/popups (needs definition)
- `InsertDataPage` (for navigation context)
- Integration with message editor logic.
- Backend/System time access. 