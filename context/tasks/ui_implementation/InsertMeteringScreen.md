## Task: Implement Insert Metering Screen

**Source PNG(s):**
- `Insert - Metering.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/InsertData/InsertMetering.qml` (Proposed)

**Description:**
Allows configuration and insertion of a metering field (potentially related to print count or usage) into the print message. Provides settings for type, start value, and upper/lower limits.

**Key UI Elements Visible:**
- Header/Title Bar ("Metering" and Back Button)
- Labeled Dropdown: "Type" (e.g., Production Count, Print Count)
- Labeled Input Field: "Start Value" (Numeric input)
- Labeled Input Field: "Upper Limit" (Numeric input)
- Labeled Input Field: "Lower Limit" (Numeric input)
- Button: Cancel
- Button: Insert

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `LabeledDropdown`: [Standard label + dropdown pair] - [Status: Reuse Existing]
- `LabeledNumericInput`: [Label + input specifically for numbers] - [Status: Reuse Existing]
- `StandardButton`: [Default action button (e.g., Cancel)] - [Status: Reuse Existing]
- `PrimaryButton`: [Primary action button (e.g., Insert)] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `dropdown_arrow`: [Dropdown indicator] - [Asset Source Suggestion: `vuesax/arrow-down.svg`]

**Navigation:**
- **Accessed From:** Tapping "Metering" option on `INSERT DATA.png` screen.
- **Navigates To:**
    - Back to `InsertDataPage` on Cancel or Insert.
    - Back to `InsertDataPage` using the Back button.

**Data Requirements/Interaction:**
- Displays current/default metering settings.
- User selects Type from dropdown.
- User enters Start Value, Upper Limit, Lower Limit.
- 'Insert' action adds the configured metering field to the message editor/layout.
- 'Cancel' discards changes.

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: Yes (For numeric inputs)

**Special Considerations:**
- Input validation (e.g., Upper Limit >= Start Value >= Lower Limit).
- Dropdown needs to be populated with available metering types.
- Handling virtual keyboard overlay.

**Dependencies:**
- `ScreenHeader` component
- `LabeledDropdown` component
- `LabeledNumericInput` component
- `StandardButton` component
- `PrimaryButton` component
- `InsertDataPage` (for navigation context)
- Integration with message editor logic.
- Backend logic for handling metering values. 