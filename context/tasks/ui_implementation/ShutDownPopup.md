## Task: Implement Shut Down Confirmation Popup

**Source PNG(s):**
- `Shut Down.png`

**Type:** Popup

**QML File Path:** `UI_new/Components/Popups/ShutDownPopup.qml` (Proposed)

**Description:**
A confirmation popup asking the user if they want to shut down the device.

**Key UI Elements Visible:**
- Popup Container
- Icon (Power icon?)
- Title: "Shut Down"
- Body Text: "Are you sure you want to shut down the device?"
- Button: Cancel
- Button: Shut Down (Primary destructive/action)

**Sub-Components Needed (Create or Reuse):**
- `PopupBase`: [Base component for modal popups] - [Status: Reuse Existing]
- `StandardButton`: [Cancel button] - [Status: Reuse Existing]
- `DestructiveButton` or `PrimaryButton`: [Shut Down button, styling depends on emphasis] - [Status: Reuse Existing]
- `StatusIcon`: [Component to display power icon] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `power_icon`: [Indicator for shut down action] - [Asset Source Suggestion: `vuesax/power.svg`]

**Navigation:**
- **Accessed From:** Likely via a dedicated power button in the `AppHeader` or potentially within the `System` screen.
- **Navigates To:**
    - Closes the popup on 'Cancel'.
    - Initiates system shutdown via backend on 'Shut Down' press.

**Data Requirements/Interaction:**
- Displays confirmation text.
- 'Shut Down' action triggers the system shutdown command via the backend.
- 'Cancel' action closes the popup.

**Global Components Used:**
- `VirtualKeyboard`: No

**Special Considerations:**
- This action will turn off the device, ensure proper confirmation.
- Requires backend integration for system shutdown command.

**Dependencies:**
- `PopupBase` component
- `StandardButton` component
- `DestructiveButton` / `PrimaryButton` component
- `StatusIcon` component
- Triggering mechanism (e.g., button in `AppHeader`)
- Backend integration. 