## Task: Implement System Others Screen

**Source PNG(s):**
- `System -Others.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/System/SystemOthersPage.qml` (Proposed)

**Description:**
Provides access to miscellaneous system settings or functions not categorized elsewhere, such as screen brightness, calibration, potentially software updates or factory reset.

**Key UI Elements Visible:**
- Header/Title Bar ("Others" and Back Button)
- List or Grid of options/buttons:
    - Screen Brightness (likely leads to slider/input)
    - Touch Calibration (likely triggers a calibration sequence)
    - Software Update (triggers update check/process?)
    - Factory Reset (triggers confirmation/process)
- Each option likely has an icon and text label.

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `OptionCard`: [Clickable card/button component with icon and label] - [Status: Reuse Existing | Needs Verification]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `brightness_icon`: [Screen Brightness] - [Asset Source Suggestion: `vuesax/sun.svg`]
- `calibration_icon`: [Touch Calibration] - [Asset Source Suggestion: `vuesax/location-crosshairs.svg`? Needs identification]
- `update_icon`: [Software Update] - [Asset Source Suggestion: `vuesax/document-download.svg`]
- `reset_icon`: [Factory Reset] - [Asset Source Suggestion: `vuesax/refresh-circle.svg`?]

**Navigation:**
- **Accessed From:** Likely from the main `System.png` screen (e.g., tapping "Others").
- **Navigates To:**
    - Brightness adjustment screen/popup (Task needed).
    - Touch calibration sequence (handled by backend/OS?).
    - Software update screen/popup (Task needed).
    - Factory reset confirmation popup (Task needed).
    - Back to `SystemPage` using the Back button.

**Data Requirements/Interaction:**
- Displays miscellaneous system options.
- Tapping an option triggers the corresponding action/navigation.
    - Brightness: Navigates to control.
    - Calibration: Initiates OS/backend calibration.
    - Update: Initiates backend check/update process.
    - Reset: Initiates confirmation popup.

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: No (Potentially in Brightness screen)

**Special Considerations:**
- Actions like Calibration, Update, and Reset have significant system impact and require careful implementation and confirmation.
- Need definitions for the target screens/popups (Brightness, Update, Reset Confirmation).

**Dependencies:**
- `ScreenHeader` component
- `OptionCard` component
- Brightness Control Screen/Popup (Task needed)
- Software Update Screen/Popup (Task needed)
- Factory Reset Confirmation Popup (Task needed)
- Icon assets for each option.
- `SystemPage` (for navigation context)
- Backend integration for brightness, calibration, update, reset. 