## Task: Implement [Screen/Popup/Component Name]

**Source PNG(s):**
- `[filename1.png]`
- `[filename2.png]` (Include variations like 'Logged In' or specific states)

**Type:** [Screen | Popup | Global Component | Local Component]

**QML File Path:** `UI_new/[Screens|Components]/[ComponentName].qml` (Proposed path)

**Description:**
[Briefly describe the purpose and functionality shown in the PNG(s).]

**Key UI Elements Visible:**
- [List major distinct visual elements: e.g., Title Bar, Main Content Area, List, Grid, Specific Buttons, Input Fields, Tabs, Custom Controls]

**Sub-Components Needed (Create or Reuse):**
- `ComponentName`: [Brief description, e.g., Standard action button with primary color] - [Status: Create New | Reuse Existing | Needs Identification]
- `Icon`: [Description, e.g., Back arrow icon] - [Asset Source: e.g., `Back/back-arrow.svg`, `vuesax/arrow-left`] - [Status: Find Asset | Create Component]
- `ListItemDelegate`: [For which list?, e.g., User list] - [Status: Create New | Reuse Existing | Needs Identification]
- ... (Identify potential reusable parts)

**Icons Needed (Specific List):**
- `[icon_purpose]`: [e.g., Home navigation] - [Asset Source Suggestion: e.g., `vuesax/home.svg`]
- `[icon_purpose]`: [e.g., Settings gear] - [Asset Source Suggestion: e.g., `Icon/settings.svg`]
- ...

**Navigation:**
- **Accessed From:** [How does the user reach this view? e.g., Login Screen, BottomBar, Tapping item on System Screen]
- **Navigates To:** [What actions lead away from this view? e.g., Tapping 'Save' button navigates back, Tapping list item navigates to Detail Screen]

**Data Requirements/Interaction:**
- [What data is displayed? e.g., List of printers, User permissions, System time]
- [What user interactions modify data or state? e.g., Toggling a setting, Entering text, Saving changes]

**Global Components Used:**
- `BottomBar` (As applicable)
- `VirtualKeyboard` (If text input is present)
- [Any other potentially global elements like a standard header]

**Special Considerations:**
- [Touch target sizes (min 50px), Handling different states (e.g., logged in/out, guest mode, error states), Virtual keyboard overlay behavior, Dark mode implications, Responsiveness notes if any.]

**Dependencies:**
- [List other tasks that should ideally be completed first. e.g., Implement `ReusableButton` component, Implement `BottomBar`]