## Task: Implement Insert Data Screen

**Source PNG(s):**
- `INSERT DATA.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/InsertData/InsertDataPage.qml` (Proposed)

**Description:**
Provides options for inserting different types of dynamic data fields into the print message (e.g., Date/Time, Counter, Batch Code, Shift Code, Barcode, Metering).

**Key UI Elements Visible:**
- Header/Title Bar ("INSERT DATA" and Back Button)
- Grid or List of Buttons/Cards, each representing a data type:
    - Date / Time
    - Counter
    - Batch Code
    - Shift Code
    - Barcode
    - Metering
- Each button/card likely has an icon and text label.

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `OptionCard`: [Clickable card/button component with icon and label for menu options] - [Status: Create New]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `calendar_icon`: [Date / Time option] - [Asset Source Suggestion: `vuesax/calendar.svg`]
- `counter_icon`: [Counter option] - [Asset Source Suggestion: `Huge-icon/counter.svg`? Needs identification]
- `batch_code_icon`: [Batch Code option] - [Asset Source Suggestion: Needs identification]
- `shift_code_icon`: [Shift Code option] - [Asset Source Suggestion: Needs identification]
- `barcode_icon`: [Barcode option] - [Asset Source Suggestion: `vuesax/scan-barcode.svg`]
- `metering_icon`: [Metering option] - [Asset Source Suggestion: Needs identification]

**Navigation:**
- **Accessed From:** Likely from a message editing screen (tapping an "Insert Data" button).
- **Navigates To:**
    - `InsertDateTimePage` (tapping Date / Time)
    - `InsertCounterScreen` (tapping Counter)
    - `InsertBatchCodeScreen` (tapping Batch Code)
    - `InsertShiftCodeScreen` (tapping Shift Code - needs creation task)
    - `InsertBarcodePage` (tapping Barcode - needs creation task)
    - `InsertMeteringScreen` (tapping Metering - needs creation task)
    - Back to the previous (message editing) screen using the Back button.

**Data Requirements/Interaction:**
- Displays available dynamic data field types.
- Tapping an option navigates to the corresponding configuration screen.

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: No

**Special Considerations:**
- Layout should adapt nicely (likely a grid).
- Ensure clear touch targets for each option.

**Dependencies:**
- `ScreenHeader` component
- `OptionCard` component
- All specific data insertion screens (`InsertDateTimePage`, `InsertCounterScreen`, etc.)
- Icon assets for each data type.
- Message editing screen (for navigation context).

**Status:**
- [ ] Completed
- [ ] In Progress
- [ ] Not Started
- [ ] Blocked
- [ ] Awaiting Review
- [ ] Approved
- [ ] Rejected
- [ ] Deprecated

**Notes:**
- Additional notes or comments about the task.

**Assignee:**
- [ ] John Doe
- [ ] Jane Smith

**Due Date:**
- [ ] 2024-04-30
- [ ] 2024-05-15

**Priority:**
- [ ] High
- [ ] Medium
- [ ] Low

**Related Tasks:**
- [ ] Task 1
- [ ] Task 2

**Related Issues:**
- [ ] Issue 1
- [ ] Issue 2

**Related Documentation:**
- [ ] Documentation 1
- [ ] Documentation 2 