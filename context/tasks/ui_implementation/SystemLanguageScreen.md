## Task: Implement System Language Screen

**Source PNG(s):**
- `System -Language.png`
- `System -Language-1.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/System/SystemLanguagePage.qml` (Proposed)

**Description:**
Allows the user to view and select the display language for the application interface.

**Key UI Elements Visible:**
- Header/Title Bar ("Language" and Back Button)
- List of available languages (e.g., "English", "Spanish", "German", etc.)
- Each language likely has a radio button or similar indicator to show the current selection.
- Potentially a "Save" or "Apply" button, or selection might apply immediately.

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `LanguageListView`: [Scrollable list view for languages] - [Status: Create New | Reuse standard ListView]
- `LanguageListItemDelegate`: [Delegate for list item, showing language name and selection indicator (radio button)] - [Status: Create New]
- `PrimaryButton`: [Optional: Save/Apply button if needed] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `radio_unchecked`: [Language selection indicator] - [Asset Source Suggestion: Needs identification/creation]
- `radio_checked`: [Language selection indicator] - [Asset Source Suggestion: Needs identification/creation]

**Navigation:**
- **Accessed From:** Likely from the main `System.png` screen (e.g., tapping a "Language" option).
- **Navigates To:**
    - Back to `SystemPage` using the Back button.
    - If Apply/Save button exists, applies setting and then navigates back or stays.

**Data Requirements/Interaction:**
- Displays the list of available UI languages supported by the application.
- Highlights the currently selected language.
- Allows the user to select a different language.
- Selection triggers an update to the application's language setting (may require app restart or dynamic reloading of UI text).

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: No

**Special Considerations:**
- Requires a mechanism for managing language translations (e.g., Qt Linguist `.ts` files).
- Need to determine if language change requires restart or happens dynamically.
- List should clearly indicate the current selection.

**Dependencies:**
- `ScreenHeader` component
- `LanguageListView` component
- `LanguageListItemDelegate` component
- `PrimaryButton` (Optional)
- `SystemPage` (for navigation context)
- Translation framework/backend. 