## Task: Implement Edit Permissions Popup/Screen

**Source PNG(s):**
- `System - Users & Permissions - Edit Permissions.png`

**Type:** Popup or dedicated Screen (Appears like a popup)

**QML File Path:** `UI_new/Screens/System/UsersPermissions/EditPermissions.qml` or `UI_new/Components/Popups/EditPermissionsPopup.qml` (Proposed)

**Description:**
Allows editing the permission level associated with a specific user.

**Key UI Elements Visible:**
- Container (Popup style)
- Title: "Edit Permissions"
- Close Button (X icon)
- User Identifier (e.g., "Username: [username]")
- Labeled Dropdown: "Permission" (showing current and allowing selection)
- Button: Cancel
- Button: Save

**Sub-Components Needed (Create or Reuse):**
- `PopupBase` or `ScreenBase`: [Depending on if it's a true popup or styled screen] - [Status: Reuse Existing | Create New]
- `DisplayField`: [To show the static username] - [Status: Create New | Reuse `LabeledDisplayField`]
- `LabeledDropdown`: [Standard label + dropdown pair for Permissions] - [Status: Reuse Existing]
- `StandardButton`: [Cancel button] - [Status: Reuse Existing]
- `PrimaryButton`: [Save button] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `close_icon`: [Popup close] - [Asset Source Suggestion: `vuesax/close-circle.svg`]
- `dropdown_arrow`: [Dropdown indicator] - [Asset Source Suggestion: `vuesax/arrow-down.svg`]

**Navigation:**
- **Accessed From:** Tapping an "Edit" action associated with a user on the `System - Users & Permissions.png` screen (likely via the `System - Users & Permissions - Edit Selected.png` mode).
- **Navigates To:**
    - Closes/navigates back to `SystemUserPermissionsPage` on Cancel, Save, or Close (X) press.

**Data Requirements/Interaction:**
- Displays the username of the user being edited.
- Displays the current permission level in the dropdown.
- Allows selecting a new permission level.
- 'Save' action updates the user's permission level via the backend.
- 'Cancel' discards changes.

**Global Components Used:**
- `VirtualKeyboard`: No

**Special Considerations:**
- Need to pass the specific User ID and current permissions to this screen/popup.
- Dropdown needs to be populated with available permission levels.

**Dependencies:**
- `PopupBase`/`ScreenBase` component
- `DisplayField` component
- `LabeledDropdown` component
- `StandardButton` component
- `PrimaryButton` component
- `SystemUserPermissionsPage` / Edit Mode (for context and triggering)
- Backend integration for fetching/updating user permissions. 