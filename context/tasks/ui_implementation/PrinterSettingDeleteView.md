## Task: Implement Printer Setting Delete View Screen

**Source PNG(s):**
- `Printer Setting - Delete view.png`

**Type:** Screen (variant of Printer Setting List)

**QML File Path:** `UI_new/Screens/Settings/PrinterSettingListPage.qml` (Likely modifies existing list page state)

**Description:**
A view of the Printer Settings list screen specifically for deleting profiles. Checkboxes appear next to each item, and a 'Delete Selected' button is shown.

**Key UI Elements Visible:**
- Header/Title Bar ("Printer Setting" and Back Button)
- List of Printer Setting Profiles
    - Each item has a Checkbox, Profile Name, potentially other details.
- Button Bar at bottom:
    - Button: Cancel (exits delete mode)
    - Button: Delete Selected (triggers deletion popup)

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `PrinterSettingListView`: [Scrollable list view for settings] - [Status: Reuse Existing | Needs modification for delete mode]
- `PrinterSettingListItemDelegate`: [Delegate for list item] - [Status: Reuse Existing | Needs modification for checkbox]
- `StandardButton`: [Cancel button] - [Status: Reuse Existing]
- `DestructiveButton`: ['Delete Selected' button] - [Status: Reuse Existing]
- `BottomActionBar`: [Container for bottom buttons like Cancel/Delete Selected] - [Status: Create New]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `checkbox_unchecked`: [Checkbox state] - [Asset Source Suggestion: `vuesax/tick-square.svg` variant]
- `checkbox_checked`: [Checkbox state] - [Asset Source Suggestion: `vuesax/tick-square.svg` variant]

**Navigation:**
- **Accessed From:** Tapping a 'Delete' action/button on the main `Printer Setting.png` screen.
- **Navigates To:**
    - `PrinterSettingDeletePopup` when tapping 'Delete Selected'.
    - Back to the normal `PrinterSettingListPage` view when tapping 'Cancel'.
    - Back to the previous screen (likely `System` or `Service`) using the Back button.

**Data Requirements/Interaction:**
- Displays the list of printer setting profiles.
- Allows user to select multiple profiles via checkboxes.
- 'Delete Selected' button is likely enabled only when at least one item is checked.
- Tapping 'Delete Selected' triggers the confirmation popup, passing the selected items.
- 'Cancel' returns the list view to its normal state.

**Global Components Used:**
- `BottomBar`: No (Replaced by `BottomActionBar` in this mode)
- `VirtualKeyboard`: No

**Special Considerations:**
- This is likely a *mode* of the main `PrinterSettingListPage` rather than a completely separate screen.
- Need to manage the selection state of list items.
- Conditional visibility of checkboxes and the bottom action bar.

**Dependencies:**
- `ScreenHeader` component
- `PrinterSettingListView` component (and its delegate)
- `StandardButton` component
- `DestructiveButton` component
- `BottomActionBar` component
- `PrinterSettingDeletePopup` (Task defined)
- `PrinterSettingListPage` (needs definition, this task modifies it)
- Backend integration for retrieving settings list. 