## Task: Implement System QR Data Screen

**Source PNG(s):**
- `System - QR Data.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/System/SystemQRDataPage.qml` (Proposed)

**Description:**
Displays QR codes containing system or diagnostic information, potentially for scanning with an external device.

**Key UI Elements Visible:**
- Header/Title Bar ("QR Data" and Back Button)
- Multiple Sections/Tabs? (e.g., "System Info", "Network Info", "Error Log Summary"?)
- QR Code Display Area (large, prominent)
- Associated text label describing the QR code content.

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `TabSelector`: [Optional: If multiple QR codes are presented via tabs] - [Status: Create New | Reuse Existing]
- `QRCodeDisplay`: [Component to generate and display a QR code image based on input data] - [Status: Create New]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]

**Navigation:**
- **Accessed From:** Likely from the main `System.png` screen (e.g., tapping a "QR Data" or "Diagnostics" option).
- **Navigates To:**
    - Back to `SystemPage` using the Back button.

**Data Requirements/Interaction:**
- Fetches data from the backend to be encoded in the QR code(s) (e.g., system serial number, IP address, recent errors).
- Generates QR code images from the fetched data.
- Displays the generated QR codes and descriptive labels.
- If tabs are used, switching tabs updates the displayed QR code and label.

**Global Components Used:**
- `BottomBar`: No
- `VirtualKeyboard`: No

**Special Considerations:**
- Requires a QR code generation library or backend service.
- Ensure QR codes are large and clear enough for reliable scanning.
- Determine the specific data sets required for each QR code.

**Dependencies:**
- `ScreenHeader` component
- `TabSelector` component (Optional)
- `QRCodeDisplay` component
- `SystemPage` (for navigation context)
- Backend integration for data retrieval.
- QR code generation library/service. 