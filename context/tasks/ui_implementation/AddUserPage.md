## Task: Implement Add User Page

**Source PNG(s):**
- `Add User.png`
- `Add User - Permission drop down.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/System/UsersPermissions/AddUser.qml` (Proposed)

**Description:**
Provides a form to add a new user to the system. Includes fields for username, password (with confirmation), and permission level selection via a dropdown.

**Key UI Elements Visible:**
- Header/Title Bar ("Add User" and Back Button)
- Form Area
- Input Field: Username (with user icon)
- Input Field: Password (with lock icon, likely masked)
- Input Field: Confirm Password (with lock icon, likely masked)
- Dropdown: Permission (with arrow icon)
- Button: Cancel
- Button: Save

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Create New | Reuse Existing]
- `TextInputWithIcon`: [Input field with icon prefix] - [Status: Create New]
- `PasswordInputWithIcon`: [Input field specifically for passwords, with masking toggle?] - [Status: Create New]
- `DropdownSelector`: [Custom dropdown component] - [Status: Create New]
- `StandardButton`: [Default action button (e.g., Cancel)] - [Status: Create New | Reuse Existing]
- `PrimaryButton`: [Primary action button (e.g., Save)] - [Status: Create New | Reuse Existing]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `user`: [Username field indicator] - [Asset Source Suggestion: `User.svg`, `vuesax/user.svg`]
- `lock`: [Password field indicator] - [Asset Source Suggestion: `vuesax/lock.svg`]
- `dropdown_arrow`: [Dropdown indicator] - [Asset Source Suggestion: `vuesax/arrow-down.svg`]

**Navigation:**
- **Accessed From:** Tapping "Add User" button on `System - Users & Permissions.png` screen.
- **Navigates To:**
    - Back to `SystemUserPermissionsPage` on Cancel or Save.
    - Back to `SystemUserPermissionsPage` using the Back button.

**Data Requirements/Interaction:**
- Displays input fields for new user credentials.
- Displays permission levels in the dropdown.
- User enters text into input fields.
- User selects a permission level from the dropdown.
- 'Save' action likely sends new user data to the backend/C++.
- 'Cancel' action discards input.

**Global Components Used:**
- `BottomBar`: No (Usually hidden in sub-screens/forms)
- `VirtualKeyboard`: Yes (For text input fields)

**Special Considerations:**
- Input validation (username format, password matching, minimum length).
- Password fields should mask input.
- Dropdown needs to display available permission levels dynamically.
- Ensure all touch targets meet the minimum size requirements (50px).
- Handle virtual keyboard overlaying input fields.

**Dependencies:**
- `ScreenHeader` component
- `TextInputWithIcon` component
- `PasswordInputWithIcon` component
- `DropdownSelector` component
- `StandardButton` component
- `PrimaryButton` component
- `SystemUserPermissionsPage` (for navigation context) 