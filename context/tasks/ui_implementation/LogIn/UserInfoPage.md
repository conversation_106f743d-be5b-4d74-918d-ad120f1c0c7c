## Task 0002: Implement User Profile Page

[Figma Design](https://www.figma.com/design/IHjEQ1obTPsKI2XMUVkawt/354---ilaue_prospr---Software?node-id=446-2592&t=Ygqcu3Mf3r63YHhu-4)

**Type:** Screen

**QML File Path:** `UI/Screens/UserProfile.qml`

---

### Description
Displays the current user's profile information, permission level, and provides actions for switching users, logging out, and navigating home. The left side features the Prospr logo and branding, while the right side shows the user's avatar, name, and permission link, all styled according to the industrial UI/UX guidelines.

---

### Key UI Elements Visible
- Prospr logo, tagline, separator, and slogan (left section)
- User avatar (large icon)
- User name (large, bold text)
- "View Permission" link/button (styled in orange, underlined)
- Three main action buttons:
  - **Switch User** (icon + label, blue)
  - **Log Out** (icon + label, red)
  - **Go to Home** (icon + label, orange)
- Bottom bar with status indicators, alert icon, home icon, and time/date

---

### Sub-Components Needed (Create or Reuse)
- `UserAvatar`: [Large user icon, can be SVG or QML shape]
- `PrimaryButton`: [For "Go to Home"]
- `DangerButton`: [For "Log Out"]
- `SecondaryButton`: [For "Switch User"]
- `PermissionLink`: [Clickable "View Permission" text]
- `BottomBar`: [Status, alerts, navigation, time/date]

---

### Icons Needed (Specific List)
- `user_avatar`: [Large profile icon]
- `switch_user`: [User icon for button]
- `logout`: [Lock or logout icon for button]
- `home`: [Home icon for button]
- `warning`: [Alert icon in bottom bar]

---

[Figma Prototype](https://www.figma.com/proto/IHjEQ1obTPsKI2XMUVkawt/354---ilaue_prospr---Software?node-id=629-7127&t=86ZZ0y0S44iDx0za-1&scaling=scale-down&content-scaling=fixed&page-id=1%3A5&starting-point-node-id=1054%3A10554) 
### Navigation
1. **Accessed From:**  
   - By tapping the solid user icon in the bottom bar when logged in. This is possible to touch once a user logs in and is on the HOME page.

2. **Navigates To:**
   - **Switch User:** Returns to Log In page.
   - **Log Out:** Returns to Log In page and sets logged-in state to false.
   - **Go to Home:** Navigates to Home screen.

---

### Data Requirements/Interaction
- Displays current user's name and permission level (static).
- "View Permission" link can open a dialog or page (placeholder for now).
- Action buttons emit signals or call navigation handlers (no backend logic required for now).

---

### Global Components Used
- `BottomBar`: Yes (visible at bottom)
- `VirtualKeyboard`: No (not needed on this page)
- `UserAvatar`: Yes

---

### Special Considerations
- All touch targets must be at least 50px high for glove compatibility.
- Use high-contrast colors and large fonts per industrial UI/UX standards.
- Ensure the layout is responsive and visually balanced for 1920x1080 touchscreens.
- "View Permission" should be styled as a link (orange, underline, hover/press feedback).
- Buttons must have icons and clear visual feedback for pressed/active states.
- Follow the Prospr color palette and branding guidelines.

---

### In General
- Match the Figma design for spacing, color, and typography.
- Do not implement backend logic—focus on UI, navigation, and data submission signals.

[Theme Info](context/tasks/ui_implementation/ThemeInfo.md)