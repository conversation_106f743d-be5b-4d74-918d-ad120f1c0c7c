## Task 0001: Clean Up Log In Page

[Figma Design](https://www.figma.com/design/IHjEQ1obTPsKI2XMUVkawt/354---ilaue_prospr---Software?node-id=629-7075&t=Ygqcu3Mf3r63YHhu-4)

**Source PNG(s) or SVG(s):**
Please reference the Figma design for the source assets.

**Type:** Screen

**QML File Path:** `UI/Screens/LogIn.qml`
**Child QML File Path:** `UI/Screens/login/LoginCard.qml`

**Description:**
Provides a login interface for users to authenticate and access the system. Includes fields for username and password, with a login button.

[Figma Prototype](https://www.figma.com/proto/IHjEQ1obTPsKI2XMUVkawt/354---ilaue_prospr---Software?node-id=629-7075&t=86ZZ0y0S44iDx0za-1&scaling=scale-down&content-scaling=fixed&page-id=1%3A5&starting-point-node-id=1054%3A10554) 
**Navigation:**
1.
- **Landing Page:** This is the page presented to the user when the application is first opened. 
2.
- **Accessed From:** Tapping "Log In" user Icon on `Home` screen. To Test this Log In using `admin` as the username and `admin` as the password. Then click on the login USER icon on the bottom bar. Then click the LOG OUT button.
3.
- **Navigates To:** On Cancel navigates to Home Page with the SERVICE, PRINT, SYSTEM nav button on the BottomBar greyed out and unclickeable.
4.
- **Navigates To:** On CreateUser navigates to `AddUserPage`. (For now can be a placeholder screen that include bottomBar so it can be tested navigation wise)


Do no implement backend functionality but implement the submission of data.
**Data Requirements/Interaction:**
- User enters text into input fields.
- 'Save' action likely sends new user data to the backend/C++.
- 'Cancel' action discards input.

**Global Components Used:**
- `BottomBar`: Hidden on LogIn Page
- `VirtualKeyboard`: Yes (For text input fields)

**Special Considerations:**
- Input validation (username format, password matching, minimum length).
- Password fields should mask input.
- Ensure all touch targets meet the minimum size requirements (50px).
- Handle virtual keyboard overlaying input fields.
- Logged in state TRUE then user ICON is soild in (all black). Logged in state FALSE then user ICON is outlined (black outline white inside).

### In General
- Match the Figma design for spacing, color, and typography.
- Do not implement backend logic—focus on UI, navigation, and data submission signals.

[Theme Info](context/tasks/ui_implementation/ThemeInfo.md)