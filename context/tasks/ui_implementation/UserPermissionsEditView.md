## Task: Implement Users & Permissions Edit View Screen

**Source PNG(s):**
- `System - Users & Permissions - Edit Selected.png`

**Type:** Screen (variant of Users & Permissions List)

**QML File Path:** `UI_new/Screens/System/UsersPermissions/SystemUserPermissionsPage.qml` (Likely modifies existing list page state)

**Description:**
A view of the Users & Permissions list screen specifically for selecting a user to edit. Radio buttons appear next to each user, and an 'Edit Selected' button is shown.

**Key UI Elements Visible:**
- Header/Title Bar ("Users & Permissions" and Back Button)
- List of Users
    - Each item has a Radio Button, Username, Permission Level.
- Button Bar at bottom:
    - Button: Cancel (exits edit selection mode)
    - Button: Edit Selected (navigates to edit form/popup for the selected user)

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `UserListView`: [Scrollable list view for users] - [Status: Reuse Existing | Needs modification for edit mode]
- `UserListItemDelegate`: [Delegate for list item] - [Status: Reuse Existing | Needs modification for radio button]
- `StandardButton`: [Cancel button] - [Status: Reuse Existing]
- `PrimaryButton`: ['Edit Selected' button] - [Status: Reuse Existing]
- `BottomActionBar`: [Container for bottom buttons] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `radio_unchecked`: [Radio button state] - [Asset Source Suggestion: Needs identification/creation]
- `radio_checked`: [Radio button state] - [Asset Source Suggestion: Needs identification/creation]

**Navigation:**
- **Accessed From:** Tapping an 'Edit' action/button on the main `System - Users & Permissions.png` screen.
- **Navigates To:**
    - `EditPermissionsPopup` (Task defined) when tapping 'Edit Selected', passing the selected user ID.
    - Back to the normal `SystemUserPermissionsPage` view when tapping 'Cancel'.
    - Back to the `SystemPage` using the Back button.

**Data Requirements/Interaction:**
- Displays the list of users.
- Allows user to select exactly one user via radio buttons.
- 'Edit Selected' button is likely enabled only when one item is selected.
- Tapping 'Edit Selected' navigates to the edit permissions popup for that user.
- 'Cancel' returns the list view to its normal state.

**Global Components Used:**
- `BottomBar`: No (Replaced by `BottomActionBar` in this mode)
- `VirtualKeyboard`: No

**Special Considerations:**
- This is likely a *mode* of the main `SystemUserPermissionsPage`.
- Need to manage the single selection state (radio group behavior).
- Conditional visibility of radio buttons and the bottom action bar.

**Dependencies:**
- `ScreenHeader` component
- `UserListView` component (and its delegate)
- `StandardButton` component
- `PrimaryButton` component
- `BottomActionBar` component
- `EditPermissionsPopup` (Task defined)
- `SystemUserPermissionsPage` (needs definition, this task modifies it)
- Backend integration for retrieving user list. 