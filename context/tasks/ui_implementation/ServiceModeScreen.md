## Task: Implement Service Mode Screen

**Source PNG(s):**
- `Service - Service mode.png`
- `Service - Service mode-1.png`

**Type:** Screen

**QML File Path:** `UI_new/Screens/Service/ServiceMode.qml` (Proposed)

**Description:**
Displays service mode options, likely related to printer maintenance or diagnostics. Shows status indicators and allows interaction with specific service functions.

**Key UI Elements Visible:**
- Header/Title Bar ("Service Mode" and Back Button)
- Multiple Status Indicator Sections (e.g., "Pump Speed", "Valve Status") each with a title, value, and possibly unit/icon.
- Action Buttons (e.g., "Start", "Stop", "Reset", potentially with icons)
- Possibly a log or message area at the bottom.

**Sub-Components Needed (Create or Reuse):**
- `ScreenHeader`: [Component with back button and title] - [Status: Reuse Existing]
- `StatusIndicatorCard`: [Component displaying a title, value, unit/status icon] - [Status: Create New]
- `ActionButton`: [Button styled for service actions, potentially with icons] - [Status: Create New | Reuse Existing `StandardButton` / `PrimaryButton`]

**Icons Needed (Specific List):**
- `back_arrow`: [Navigation back] - [Asset Source Suggestion: `Back/back.svg`, `vuesax/arrow-left.svg`]
- `play_icon`: [Start action] - [Asset Source Suggestion: `vuesax/play.svg`]
- `stop_icon`: [Stop action] - [Asset Source Suggestion: `vuesax/stop-circle.svg` ?]
- `reset_icon`: [Reset action] - [Asset Source Suggestion: `vuesax/refresh-2.svg` ?]
- Icons for specific status indicators (pump, valve etc.) - [Asset Source Suggestion: Needs identification from icon sets]

**Navigation:**
- **Accessed From:** Likely from the main `Service.png` screen (e.g., tapping a "Service Mode" option).
- **Navigates To:**
    - Back to `ServicePage` using the Back button.

**Data Requirements/Interaction:**
- Displays real-time or retrieved status values (Pump Speed, Valve Status, etc.).
- Buttons trigger specific service actions (Start, Stop, Reset) which interact with the backend/printer hardware.
- Status values might update dynamically.

**Global Components Used:**
- `BottomBar`: No (Likely hidden in service modes)
- `VirtualKeyboard`: No

**Special Considerations:**
- Real-time data updates for status indicators.
- Clear visual feedback for button presses and triggered actions.
- Safety implications of service mode actions.
- Ensure touch targets are sufficient.

**Dependencies:**
- `ScreenHeader` component
- `StatusIndicatorCard` component
- `ActionButton` component (or standard buttons)
- Icon assets for actions and statuses
- `ServicePage` (for navigation context)
- Backend integration for status data and actions. 