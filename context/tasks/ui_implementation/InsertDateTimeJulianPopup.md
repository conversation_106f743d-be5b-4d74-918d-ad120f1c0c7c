## Task: Implement Insert Date & Time Julian Settings Popup

**Source PNG(s):**
- `Insert - Date & Time - Julian.png`

**Type:** Popup

**QML File Path:** `UI_new/Components/Popups/InsertDateTimeJulianPopup.qml` (Proposed)

**Description:**
A popup dialog specifically for configuring Julian date settings within the Date & Time insertion feature.

**Key UI Elements Visible:**
- Popup Container
- Title: "Julian Setting"
- Close Button (X icon)
- Labeled Dropdown: "Type" (e.g., Standard, Modified)
- Labeled Input Field: "Delimiter" 
- Labeled Checkbox: "Fixed Length" (appears enabled when checked)
- Labeled Numeric Input: "Number of Digits" (enabled based on Fixed Length checkbox)
- Button: Cancel
- Button: Save

**Sub-Components Needed (Create or Reuse):**
- `PopupBase`: [Base component for modal popups] - [Status: Reuse Existing]
- `LabeledDropdown`: [Standard label + dropdown pair] - [Status: Reuse Existing]
- `LabeledTextInput`: [Standard label + text input pair] - [Status: Reuse Existing]
- `LabeledCheckbox`: [Standard label + checkbox pair] - [Status: Reuse Existing]
- `LabeledNumericInput`: [Label + input specifically for numbers] - [Status: Reuse Existing]
- `StandardButton`: [Default action button (e.g., Cancel)] - [Status: Reuse Existing]
- `PrimaryButton`: [Primary action button (e.g., Save)] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `close_icon`: [Popup close] - [Asset Source Suggestion: `vuesax/close-circle.svg`]
- `dropdown_arrow`: [Dropdown indicator] - [Asset Source Suggestion: `vuesax/arrow-down.svg`]

**Navigation:**
- **Accessed From:** Tapping a "Julian Setting" button/option on the `Insert - Date & Time` screen (presumed).
- **Navigates To:**
    - Closes the popup on 'Cancel', 'Save', or Close (X) button press, returning focus to the `InsertDateTimePage`.

**Data Requirements/Interaction:**
- Displays current Julian date settings.
- Allows user to select Type, enter Delimiter, toggle Fixed Length, and set Number of Digits.
- "Number of Digits" input is conditionally enabled based on the "Fixed Length" checkbox.
- 'Save' action confirms changes and updates the date/time configuration.
- 'Cancel' discards changes.

**Global Components Used:**
- `VirtualKeyboard`: Yes (For Delimiter and Number of Digits input)

**Special Considerations:**
- Conditional enabling/disabling of the "Number of Digits" input.
- Input validation (e.g., Number of Digits must be positive integer).
- Popup overlay behavior.

**Dependencies:**
- `PopupBase` component
- `LabeledDropdown` component
- `LabeledTextInput` component
- `LabeledCheckbox` component
- `LabeledNumericInput` component
- `StandardButton` component
- `PrimaryButton` component
- `InsertDateTimePage` (for context and triggering - needs creation task) 