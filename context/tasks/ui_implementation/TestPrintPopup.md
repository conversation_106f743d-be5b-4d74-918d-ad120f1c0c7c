## Task: Implement Test Print Mode Popup

**Source PNG(s):**
- `Test Print Mode.png`

**Type:** Popup

**QML File Path:** `UI_new/Components/Popups/TestPrintPopup.qml` (Proposed)

**Description:**
A popup initiating a test print. It seems to show a progress/status message and potentially allows canceling.

**Key UI Elements Visible:**
- Popup Container
- Icon (Printer icon?)
- Title: "Test Print Mode"
- Body Text/Status: "Printing test message..." (or similar status)
- Possibly a Cancel button or it might close automatically.

**Sub-Components Needed (Create or Reuse):**
- `PopupBase`: [Base component for modal popups] - [Status: Reuse Existing]
- `StatusIcon`: [Component to display icons like printer, info] - [Status: Reuse Existing | Create New]
- `AnimatedIndicator`: [Optional: Could show a spinner or progress bar during printing] - [Status: Create New]
- `StandardButton`: [Optional: Cancel button] - [Status: Reuse Existing]

**Icons Needed (Specific List):**
- `printer_icon`: [Indicator for test print action] - [Asset Source Suggestion: `vuesax/printer.svg`]
- `spinner_icon`: [If using an animated indicator] - [Asset Source Suggestion: Needs creation or library]

**Navigation:**
- **Accessed From:** Tapping the Test Print icon/button on the `Print.png` screen.
- **Navigates To:**
    - Closes automatically after test print completion/failure OR
    - Closes when 'Cancel' is tapped (if available), returning focus to `PrintPage`.

**Data Requirements/Interaction:**
- Displays status text indicating the test print is in progress.
- Initiates the test print command via the backend.
- Status text might update based on backend feedback (e.g., "Printing...", "Complete", "Error").
- Cancel button (if present) sends a cancel command to the backend.

**Global Components Used:**
- `VirtualKeyboard`: No

**Special Considerations:**
- Requires feedback from the backend about the test print status.
- Handling potential errors during the test print.
- Decide whether a cancel option is needed/feasible.

**Dependencies:**
- `PopupBase` component
- `StatusIcon` component
- `AnimatedIndicator` (Optional)
- `StandardButton` (Optional)
- `PrintPage` (for context and triggering)
- Backend integration for initiating and monitoring test prints. 