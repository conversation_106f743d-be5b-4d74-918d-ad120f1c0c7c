# QML & Qt Quick Best Practices

_Last updated: 2025-05-25 | Source: Qt official docs, community guides_

---

## 1. Coding Conventions
- Follow [QML Coding Conventions](https://doc.qt.io/qt-6/qml-codingconventions.html) for naming, structure, and style.
- Organize QML files and resources in a clear module structure.
- Use Qt's [resource system](https://doc.qt.io/qt-6/resources.html) to bundle images, icons, and QML files for cross-platform compatibility.

## 2. Separate UI from Business Logic
- Write UI in QML; write business logic and data processing in C++.
- Use QML for prototyping and collaborating with designers.
- Integrate C++ and QML for large or dynamic data sets ([C++ models for QML](https://doc.qt.io/qt-6/qtquick-modelviewsdata-cppmodels.html)).

## 3. Use Declarative Bindings
- Prefer declarative property bindings (e.g., `color: "red"`) over imperative JavaScript assignments (e.g., `Component.onCompleted: color = "red"`).
- Declarative bindings are faster, safer, and better supported by tooling.

## 4. Type Safety
- Use explicit types for properties (e.g., `property int count`) instead of `var` where possible.
- This improves static analysis, error reporting, and code readability.

## 5. Property Change Signals
- Prefer interaction signals (e.g., `onMoved` for `Slider`) over `valueChanged` to avoid unintended event cascades.
- Use explicit signals to react to user input, not internal value changes.

## 6. Qt Quick Layouts & Responsiveness
- Use Qt Quick Layouts (RowLayout, ColumnLayout, GridLayout, etc.) for arranging UI elements.
- Avoid explicit width/height; use anchors and layouts for scaling.
- Provide images/icons for multiple resolutions (`@2x`, `@3x`, etc.) or use SVGs/font icons for scalability.

## 7. Bundle Application Resources
- Use the Qt resource system to ensure all assets are available regardless of OS security policies.
- Keep QML files in the same directory as your module's CMakeLists.txt for correct implicit imports.

## 8. Store State in Models, Not Delegates
- Avoid storing persistent state in ListView/GridView delegates; use models for state instead.

## 9. Scalable User Interfaces
- Use layouts and anchors for adaptive UIs.
- Prefer vector graphics and font icons for scalable visuals.
- Enable high DPI scaling in Qt for best results on modern displays.

## 10. Internationalization
- Make all user-facing strings translatable using Qt's translation system.

## 11. Performance
- See [QML Performance Considerations](https://doc.qt.io/qt-6/qtquick-performance.html) for tips on optimizing QML apps.
- Minimize JavaScript usage in performance-critical paths.

## 12. Tools & Utilities
- Use Qt Design Studio for rapid prototyping and designer-developer collaboration.
- Leverage QML linting and static analysis tools.

## 13. All files/Assets should be in the Assets folder
- Be sure to always include new files and assests in the qml.qrc file or else will not be imported into projects.
- This is not caught at compile time only at runtime.

## 14. Always refernce proper Themes (Color, Typography, etc..)
- To ensure best practice and consistency always use the proper Theme files for colors, typography, etc.. 
---

_This document is intended as a quick reference for AI and developers working on QML/Qt Quick UI and backend projects. For deeper details, always consult the [official Qt documentation](https://doc.qt.io/)._
