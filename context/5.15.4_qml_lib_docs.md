# Qt 5.15.4 QML Library Documentation (In-Use)

---

## QtQuick 2.15

The [Qt Quick](https://doc.qt.io/qt-5/qtquick-index.html) module provides graphical primitive types. These types are only available in a QML document if that document imports the QtQuick namespace.

```qml
import QtQuick 2.15
```

Visit the [Qt Quick](https://doc.qt.io/qt-5/qtquick-index.html) module documentation for more information about the concepts which are central to QtQuick.

---

## QtQuick.Window 2.15

### [Window QML Type](https://doc.qt.io/qt-5/qml-qtquick-window-window.html)
Creates a new top-level window. More info: [QQuickWindow](https://doc.qt.io/qt-5/qquickwindow.html)
- [List of all members, including inherited members](https://doc.qt.io/qt-5/qml-qtquick-window-window-members.html)

---

## QtQuick.Controls 2.15

Qt Quick Controls provides QML types for creating user interfaces. These QML types work in conjunction with Qt Quick and [Qt Quick Layouts](https://doc.qt.io/qt-5/qtquicklayouts-index.html).

```qml
import QtQuick.Controls 2.15
```

---

## QtQuick.Layouts 1.15

The Qt Quick Layouts module provides QML types for arranging QML items in a user interface. These QML types work in conjunction with Qt Quick and [Qt Quick Controls](https://doc.qt.io/qt-5/qtquickcontrols-index.html).

```qml
import QtQuick.Layouts 1.15
```

### Types:
- [ColumnLayout](https://doc.qt.io/qt-5/qml-qtquick-layouts-columnlayout.html): Identical to GridLayout, but having only one column
- [GridLayout](https://doc.qt.io/qt-5/qml-qtquick-layouts-gridlayout.html): Provides a way of dynamically arranging items in a grid
- [Layout](https://doc.qt.io/qt-5/qml-qtquick-layouts-layout.html): Provides attached properties for items pushed onto a GridLayout, RowLayout or ColumnLayout
- [RowLayout](https://doc.qt.io/qt-5/qml-qtquick-layouts-rowlayout.html): Identical to GridLayout, but having only one row
- [StackLayout](https://doc.qt.io/qt-5/qml-qtquick-layouts-stacklayout.html): Stack of items where only one item is visible at a time

---

## QtQuick.VirtualKeyboard 2.4

The QML types can be imported into your application using the following import statements in your .qml file:
```qml
import QtQuick.VirtualKeyboard 2.15
import QtQuick.VirtualKeyboard.Styles 2.15
import QtQuick.VirtualKeyboard.Settings 2.15
```

In addition to importing the types, the QT_IM_MODULE environment variable must be set to `qtvirtualkeyboard`. For more information, see [Loading the Plugin](https://doc.qt.io/qt-5/qtvirtualkeyboard-deployment-guide.html).

### Key Types:
- [KeyIcon](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-styles-keyicon.html): Key icon with adjustable color
- [KeyPanel](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-styles-keypanel.html): A base type of the styled keys
- [KeyboardStyle](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-styles-keyboardstyle.html): Provides a styling interface for the Virtual Keyboard
- [SelectionListItem](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-styles-selectionlistitem.html): A base type for selection list item delegates
- [TraceCanvas](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-styles-tracecanvas.html): A specialized Canvas type for rendering Trace objects
- [TraceInputKeyPanel](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-styles-traceinputkeypanel.html): A base type of the trace input key
- [VirtualKeyboardSettings](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-settings-virtualkeyboardsettings.html): Provides settings for virtual keyboard
- [BackspaceKey](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-backspacekey.html): Backspace key for keyboard layouts
- [BaseKey](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-basekey.html): Common parent for all key types
- [ChangeLanguageKey](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-changelanguagekey.html): Change language key for keyboard layouts
- [EnterKey](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-enterkey.html): Enter key for keyboard layouts
- [EnterKeyAction](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-enterkeyaction.html): Provides attached properties for customizing the enter key
- [FillerKey](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-fillerkey.html): Filler key for keyboard layouts
- [HandwritingInputPanel](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-handwritinginputpanel.html): Provides a handwriting panel add-on for the virtual keyboard UI
- [HandwritingModeKey](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-handwritingmodekey.html): Hand writing mode key for keyboard layouts
- [HideKeyboardKey](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-hidekeyboardkey.html): Hide keyboard key for keyboard layouts
- [InputContext](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-inputcontext.html): Provides access to an input context
- [InputEngine](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-inputengine.html): Maps the user input to the input methods
- [InputMethod](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-inputmethod.html): Base type for creating input method in QML
- [InputModeKey](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-inputmodekey.html): Input mode key for keyboard layouts
- [InputPanel](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-inputpanel.html): Provides the virtual keyboard UI
- [Key](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-key.html): Regular character key for keyboard layouts
- [KeyboardColumn](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-keyboardcolumn.html): Keyboard column for keyboard layouts
- [KeyboardLayout](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-keyboardlayout.html): Keyboard layout
- [KeyboardLayoutLoader](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-keyboardlayoutloader.html): Allows dynamic loading of keyboard layout
- [KeyboardRow](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-keyboardrow.html): Keyboard row for keyboard layouts
- [ModeKey](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-modekey.html): Generic mode key for keyboard layouts
- [NumberKey](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-numberkey.html): Specialized number key for keyboard layouts
- [SelectionListModel](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-selectionlistmodel.html): Provides a data model for the selection lists
- [ShiftHandler](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-shifthandler.html): Manages the shift state
- [ShiftKey](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-shiftkey.html): Shift key for keyboard layouts

---

## QtQuick.VirtualKeyboard.Settings 2.4

The QML types can be imported into your application using the following import statement:
```qml
import QtQuick.VirtualKeyboard.Settings 2.15
```
- [VirtualKeyboardSettings](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-settings-virtualkeyboardsettings.html): Provides settings for virtual keyboard

---

## QtQuick.VirtualKeyboard.Styles 2.4

The QML types can be imported into your application using the following import statement:
```qml
import QtQuick.VirtualKeyboard.Styles 2.15
```
- [KeyIcon](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-styles-keyicon.html): Key icon with adjustable color
- [KeyPanel](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-styles-keypanel.html): A base type of the styled keys
- [KeyboardStyle](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-styles-keyboardstyle.html): Provides a styling interface for the Virtual Keyboard
- [SelectionListItem](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-styles-selectionlistitem.html): A base type for selection list item delegates
- [TraceCanvas](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-styles-tracecanvas.html): A specialized Canvas type for rendering Trace objects
- [TraceInputKeyPanel](https://doc.qt.io/qt-5/qml-qtquick-virtualkeyboard-styles-traceinputkeypanel.html): A base type of the trace input key

---

*This file was auto-generated for AI consumption and fast reference for Qt 5.15.4 QML modules currently in use in the Prospr Light project.*
