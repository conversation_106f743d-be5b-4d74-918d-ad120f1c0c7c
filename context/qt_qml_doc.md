# Qt 5.14 and QML 2.x Development Reference Guide

This document provides a comprehensive reference for developing with Qt 5.14 and QML 2.x, specifically tailored for the Light printer management application.

## Table of Contents
1. [Qt and QML Overview](#qt-and-qml-overview)
2. [Layout and Positioning Best Practices](#layout-and-positioning-best-practices)
3. [Styling and Theming](#styling-and-theming)
4. [Component Structure and Organization](#component-structure-and-organization)
5. [Qt Quick Controls](#qt-quick-controls)
6. [Performance Optimization](#performance-optimization)
7. [Common Design Patterns](#common-design-patterns)
8. [Useful Resources](#useful-resources)

## Qt and QML Overview

### Qt 5.14 Key Features
- Released in December 2019
- Improved support for high-DPI displays
- Better handling of touch and multi-touch events
- Enhanced networking capabilities with HTTP/2 support
- Optimized rendering pipeline for improved performance
- Support for multiple threading models

### QML 2.x Fundamentals
- Declarative language for designing user interfaces
- Uses a JavaScript runtime for logic and expressions
- Property binding for reactive updates
- Signal and handler mechanisms for events
- Component-based architecture for reusability
- Integration with C++ for performance-critical operations

## Layout and Positioning Best Practices

### Anchors
Anchors are the most efficient way to position items relative to their parent or siblings.

```qml
Rectangle {
    id: child
    width: 100
    height: 100
    anchors.left: parent.left
    anchors.top: parent.top
    anchors.margins: 10
}
```

**Best Practices:**
- Use `anchors` for fixed positioning relative to parent or siblings
- Anchors are more performant than bindings for positioning
- Use `anchors.fill: parent` for items that should completely fill their parent
- Use `anchors.centerIn: parent` for centered items

### Positioners
For simple sequential layouts, use the built-in positioners:

- **Row**: Arranges items in a horizontal line
- **Column**: Arranges items in a vertical line
- **Grid**: Arranges items in a grid
- **Flow**: Arranges items like a wrapping grid

```qml
Column {
    spacing: 10
    Rectangle { width: 100; height: 50; color: "red" }
    Rectangle { width: 100; height: 50; color: "blue" }
    Rectangle { width: 100; height: 50; color: "green" }
}
```

### Qt Quick Layouts
For more complex, responsive layouts, use Qt Quick Layouts:

- **RowLayout**: Responsive horizontal arrangement
- **ColumnLayout**: Responsive vertical arrangement
- **GridLayout**: Responsive grid arrangement

```qml
RowLayout {
    anchors.fill: parent
    spacing: 10
    
    Rectangle {
        Layout.fillWidth: true
        Layout.preferredWidth: 200
        Layout.minimumWidth: 100
        Layout.preferredHeight: 50
        color: "red"
    }
    
    Rectangle {
        Layout.fillWidth: true
        Layout.preferredWidth: 200
        Layout.minimumWidth: 100
        Layout.preferredHeight: 50
        color: "blue"
    }
}
```

**Best Practices:**
- Use Qt Quick Layouts when items need to resize dynamically
- Layouts are more CPU-intensive than anchors, use only when necessary
- Do not mix anchors and Layout properties on the same item
- Set proper minimum, preferred, and maximum sizes for consistent behavior

## Styling and Theming

### Gradients
Gradients create smooth color transitions and can be applied to any Rectangle item:

```qml
Rectangle {
    width: 200
    height: 100
    gradient: Gradient {
        GradientStop { position: 0.0; color: "white" }
        GradientStop { position: 1.0; color: "black" }
    }
}
```

For horizontal gradients, you might need to use rotation:
```qml
Rectangle {
    width: 200
    height: 100
    rotation: 90
    gradient: Gradient {
        GradientStop { position: 0.0; color: "white" }
        GradientStop { position: 1.0; color: "black" }
    }
    transformOrigin: Item.TopLeft
}
```

### Singleton Pattern for Theming
Use singletons to create a centralized theme that can be accessed from any QML file:

```qml
// Colors.qml
pragma Singleton
import QtQuick 2.14

QtObject {
    readonly property color primary: "#F04E23"
    readonly property color secondary: "#003140"
    // Other colors and theme properties
}
```

**Best Practices:**
- Keep all colors and visual properties in theme singletons
- Use descriptive property names rather than hard-coded values
- Allow for theme switching by changing theme properties
- Create separate theme files for different visual aspects (colors, fonts, metrics)

## Component Structure and Organization

### Reusable Components
Create modular, reusable components to improve maintainability:

```qml
// CustomButton.qml
Rectangle {
    id: root
    property string text: "Button"
    property color backgroundColor: Theme.Colors.primary
    signal clicked()
    
    width: buttonText.width + 20
    height: buttonText.height + 10
    color: backgroundColor
    radius: 5
    
    Text {
        id: buttonText
        anchors.centerIn: parent
        text: root.text
        color: "white"
    }
    
    MouseArea {
        anchors.fill: parent
        onClicked: root.clicked()
    }
}
```

### File Structure
Organize files in a logical structure:

- `UI/Screens/` - Main application screens
- `UI/Components/` - Reusable UI components
- `UI/Theme/` - Theme-related files (colors, fonts, etc.)
- `UI/Controls/` - Custom controls and input components
- `UI/Models/` - Data models and delegates

### Component Communication
Choose the appropriate method for component communication:

1. **Properties**: For parent-to-child communication
2. **Signals and Slots**: For event-based communication
3. **Context Properties**: For global state accessible to all components
4. **Loader**: For dynamic component loading and unloading

## Qt Quick Controls

### Qt Quick Controls 2
Qt Quick Controls 2 provides a set of UI controls with built-in styling:

- **Button**: Standard push button
- **CheckBox**: Toggled option
- **ComboBox**: Drop-down selection
- **TextField**: Single-line text input
- **TextArea**: Multi-line text input
- **Slider**: Adjustable value in a range
- **Switch**: On/off toggle
- **SpinBox**: Numeric input with increment/decrement

```qml
import QtQuick.Controls 2.14

Button {
    text: "Click Me"
    onClicked: console.log("Button clicked")
}
```

### Styling Controls
Controls can be styled using themes or custom styling:

```qml
Button {
    text: "Custom Button"
    
    background: Rectangle {
        implicitWidth: 100
        implicitHeight: 40
        color: parent.down ? "#d0d0d0" : "#e0e0e0"
        border.color: "#10000000"
        radius: 4
    }
}
```

## Performance Optimization

### Binding Optimization
Minimize binding expressions and especially complex JavaScript in bindings:

```qml
// Inefficient
Rectangle {
    width: parent.width * 0.5 + calculateSomething()
}

// More efficient
Rectangle {
    Component.onCompleted: {
        width = parent.width * 0.5 + calculateSomething()
    }
}
```

### Image Handling
Optimize image usage:

- Use appropriate image formats (PNG for transparency, JPEG for photos)
- Scale images at design time when possible
- Use `sourceSize` to control memory usage
- Use `asynchronous: true` for large images to prevent UI freezing

```qml
Image {
    source: "large_image.jpg"
    sourceSize.width: 300
    sourceSize.height: 200
    asynchronous: true
    cache: true
}
```

### Visibility Management
Use the most appropriate visibility technique:

- `opacity: 0` - Still in the scene graph but invisible
- `visible: false` - Removed from scene graph but still instantiated
- Use Loaders for components that aren't always needed

### Incremental Rendering
For lists with many items, use incremental rendering:

```qml
ListView {
    model: largeModel
    delegate: MyDelegate {}
    cacheBuffer: 200
    
    // Incrementally render large lists
    highlightRangeMode: ListView.ApplyRange
    preferredHighlightBegin: 0
    preferredHighlightEnd: height
}
```

## Common Design Patterns

### Model-View-Delegate
Separate data (model), presentation (view), and appearance (delegate):

```qml
ListView {
    width: 200
    height: 400
    model: myDataModel
    delegate: Rectangle {
        width: ListView.view.width
        height: 40
        color: index % 2 ? "#f0f0f0" : "#e0e0e0"
        Text {
            anchors.centerIn: parent
            text: modelData.name // or role name if using QAbstractItemModel
        }
    }
}
```

### State Management
Use states to manage different UI configurations:

```qml
Rectangle {
    id: loginForm
    
    states: [
        State {
            name: "LoggedOut"
            PropertyChanges { target: loginButton; visible: true }
            PropertyChanges { target: logoutButton; visible: false }
        },
        State {
            name: "LoggedIn"
            PropertyChanges { target: loginButton; visible: false }
            PropertyChanges { target: logoutButton; visible: true }
        }
    ]
    
    state: "LoggedOut"
}
```

### Transitions
Animate between states with transitions:

```qml
Rectangle {
    // ... states defined as above
    
    transitions: [
        Transition {
            from: "*"; to: "*"
            PropertyAnimation {
                properties: "opacity,x,y"
                duration: 200
                easing.type: Easing.InOutQuad
            }
        }
    ]
}
```

## Useful Resources

### Official Documentation
- [Qt Documentation](https://doc.qt.io/qt-5/index.html)
- [Qt QML Module](https://doc.qt.io/qt-5/qtqml-index.html)
- [Qt Quick Module](https://doc.qt.io/qt-5/qtquick-index.html)
- [Qt Quick Controls 2](https://doc.qt.io/qt-5/qtquickcontrols-index.html)

### Best Practices
- [Performance Considerations and Tips](https://doc.qt.io/qt-5/qtquick-performance.html)
- [QML Coding Conventions](https://doc.qt.io/qt-5/qml-codingconventions.html)
- [Scene Graph Rendering](https://doc.qt.io/qt-5/qtquick-visualcanvas-scenegraph.html)

### Light Application Specific
- Qt 5.14
- QML 2.x framework
- Modular component architecture
- Centralized theming with Colors.qml singleton
- Gradient backgrounds for modern UI appearance
- Streamlined navigation with simplified headers
