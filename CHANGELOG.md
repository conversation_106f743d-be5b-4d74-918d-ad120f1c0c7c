# Changelog

## [1.6.0] - 2025-04-09
### Added
- Implemented `PathResolver` singleton for dynamic resource path resolution (addresses target vs. local environment differences).
- Added `import "qrc:/UI_new/Core"` to `MainLayout.qml` and `Home.qml`.

### Changed
- Updated all QML `import` statements within `UI_new/` to use standard `qrc:/` paths.
- Updated dynamic resource loading (`Loader.source`, `Image.source`) in `main.qml`, `MainLayout.qml`, and `Home.qml` to use `PathResolver`.
- Corrected font property assignments in `KeyboardStyle.qml` to use theme properties within the `keyPanel` delegate.

### Removed
- Removed invalid `keyTextColor`, `keyContentMargins`, and `keyTextFont` property assignments from `KeyboardStyle.qml`.
- Removed direct `style` property assignment on `InputPanel` in `main.qml` due to loading issues (reverted to default style).
- Removed the initial redundant `Item` element from `main.qml`.

### Fixed
- Resolved various "module not installed", "Type unavailable", "ReferenceError: PathResolver is not defined", and incorrect property assignment errors during application startup and navigation.
- Corrected hardcoded `file:///` paths in `import` statements and `Loader` sources that broke local development builds.
- Added null check before calling `shutdownDialog.open()` in `Home.qml` to prevent runtime errors.

## [1.5.2] - 2025-04-01
### Added
- Comprehensive font management system for cross-platform deployments
- Automatic system font discovery and deployment to target devices
- Support for DejaVu, Liberation, Ubuntu, Droid, and Noto font families

### Changed
- Improved deployment scripts to handle font installation properly
- Enhanced environment setup to correctly configure Qt font directories
- Updated target environment script to better handle aarch64 deployment environments

### Fixed
- Resolved "QFontDatabase: Cannot find font directory" errors on aarch64 targets
- Fixed font path configuration to ensure correct font loading
- Improved permissions handling for font directories

## [1.5.1] - 2025-04-01
### Added
- Created reusable UI components (FileListComponent, PrintControlsComponent, PrinterStatusComponent)
- Implemented modular architecture for the Print screen

### Changed
- Refactored Print.qml to use component-based architecture
- Improved code organization and maintainability
- Enhanced UI separation of concerns with dedicated components

## [1.5.0] - 2025-04-01
### Added
- Enhanced UI for industrial environments with larger touch targets
- Implemented custom warning triangle icon for error notifications
- Added improved service tools and settings gear icons 
- Added visual feedback for button interactions (pressed states)
- Created centralized Theme directory with singleton Colors module for consistent theming

### Changed
- Updated color palette to match Figma design with dark navy backgrounds and Prospr orange accents
- Implemented dark theme UI with proper contrast and readability
- Increased status indicator sizes for better visibility
- Enlarged navigation buttons to minimum 55px height for better touch interaction
- Improved BottomBar with enlarged elements and enhanced layout
- Updated Home and Login screens with larger visual elements
- Standardized typography with larger fonts for industrial environments

### Fixed
- Fixed date and time formatting issues in BottomBar
- Added proper navigation parameters to screen transition functions
- Improved shadow effects for better visibility in varied lighting
- Enhanced button responsiveness for operators wearing gloves

## [1.4.0] - 2025-03-31
### Added
- Added comprehensive Linux support for development environments
- Implemented automatic dependency detection and installation for Ubuntu/Debian
- Added Linux-specific window scaling configuration
- Enhanced build system to handle Linux environments
- Added QML import paths specific to Linux file system structure

### Changed
- Updated build scripts to use Linux-specific commands and tools
- Improved QML module detection and import path handling on Linux
- Modified project file to specify correct lrelease path on Linux
- Updated README with Linux dependency requirements

### Fixed
- Fixed Qt module detection issues on Linux
- Resolved translation tool paths for Linux environments
- Added missing QML module dependencies for proper UI rendering
- Fixed window scaling on high-resolution Linux displays

## [1.3.0] - 2025-03-31
### Changed
- Streamlined UI by removing redundant bottom navigation bars
- Simplified screen layouts with centralized navigation through MainLayout
- Improved UI consistency across all screens
- Enhanced overall user interface appearance

### Fixed
- Fixed recursive layout errors in various screens
- Improved navigation between screens

## [1.2.0] - 2025-03-29
### Added
- Implemented comprehensive test infrastructure with QML and UI tests
- Created mock components for isolated testing of UI elements
- Added test helper utilities for UI component testing
- Created platform-aware test runner script (run_tests.sh)
- Added detailed documentation for test suite in tests/README.md

### Changed
- Updated test running workflow to handle platform-specific differences
- Improved test organization with separate QML and UI test modules
- Enhanced test coverage for UI components and scaling behavior
- Updated main README with test-related documentation

### Fixed
- Resolved issues with test component loading and initialization
- Fixed platform-specific test execution paths on macOS
- Improved test stability and reliability

## [1.1.0] - 2025-03-29
### Added
- Created comprehensive project README.md with complete documentation
- Implemented responsive UI scaling for the MacBook Pro's Retina display
- Added dynamic content scaling via transform for UI components with hardcoded dimensions
- Created new modular scripts structure in `scripts/env/` directory
- Added universal build, run and deploy scripts with consistent interfaces
- Created detailed documentation for all scripts in scripts/README.md

### Changed
- Refactored window sizing logic to use smaller fixed size (1440x810) for development
- Consolidated redundant environment scripts into a modular structure
- Moved t507_cross_compile.pri into dedicated build-config directory
- Improved application centering on screen for better visibility
- Enhanced dynamic scaling approach that keeps UI components proportional
- Updated scripts to use a more consistent argument parsing approach

### Removed
- Eliminated redundant environment script files
- Removed duplicate scaling code
- Cleaned up unnecessary variables and commented-out code

### Fixed
- Fixed UI display issues with TopBand and other components on MacBook Pro
- Resolved scaling artifacts on high-DPI displays
- Fixed window sizing to ensure all UI components remain visible
- Corrected content scaling to handle hardcoded pixel dimensions in UI components

## [1.0.0] - 2025-03-28
### Added
- Introduced `ApplicationWindow` to replace `Window` for better platform-specific support.
- Added scaling logic (`scaleFactor`) to handle different screen resolutions and device pixel ratios.
- Implemented platform-specific scaling for macOS using `Screen.devicePixelRatio`.
- Added `Component.onCompleted` logic to dynamically adjust window size and position based on screen dimensions.
- Added `scripts/build_local.sh` for local builds, including cleaning, configuring, and building the project.

### Changed
- Renamed project from `chomp_qt5` to `light_qt5`.
- Updated translation file references from `chomp_qt5` to `light_qt5`.
- Updated `main.qml` to include dynamic scaling and improved layout handling.

### Removed
- Deleted `chomp_qt5.pro` file and replaced it with updated project configuration.
- Removed unused or outdated QML files and resources from the project.

### Fixed
- Ensured the application window fits within the screen dimensions, avoiding off-screen rendering issues.