# Light Application UI Guidelines

This document provides guidelines and best practices for UI development in the Light application. The application uses a comprehensive theming system with a dark mode theme based on Figma designs, consistent typography, standardized spacing, and elevation effects.

## Theme System Overview

The Light application uses a complete theming system consisting of the following components:

1. **Theme** - Controls global theme settings like dark mode
2. **Colors** - Provides a centralized color palette with light/dark variants
3. **Typography** - Standardizes text styles and sizes
4. **Spacing** - Defines consistent spacing and sizing
5. **Shadows** - Creates standardized elevation effects

All theme components are implemented as QML singletons in the `UI_new/Theme` directory.

## Dark Mode Implementation

The Light application now implements a dark theme UI based on the Figma design specifications. The dark theme features:

- Dark navy blue (`#003140`) backgrounds for primary surfaces
- Slightly darker navy (`#00242F`) for secondary container backgrounds
- Prospr orange (`#F04E23`) as the primary accent color
- White text on dark backgrounds for optimal readability
- Semantic colors for status indicators (success, warning, error)

All UI components should use the centralized color system to maintain visual consistency throughout the application.

## Color System

The Light application uses a centralized color system via the `Theme.Colors` singleton. All color definitions are maintained in a single location to ensure consistency throughout the application.

### Importing the Theme

To use the theme colors in your QML files, add this import statement at the top of your file:

```qml
import "qrc:/UI_new/Theme" as Theme
```

Make sure to adjust the path based on your file's location relative to the `UI_new/Theme` directory.

### Using Theme Colors

Replace any hardcoded color values with references to the Theme.Colors properties:

```qml
// INCORRECT: Hardcoded colors
Rectangle {
    color: "#003140"
    
    Text {
        color: "#FFFFFF"
    }
}

// CORRECT: Theme colors
Rectangle {
    color: Theme.Colors.backgroundPrimary
    
    Text {
        color: Theme.Colors.textPrimary
    }
}
```

### Color Palette Reference

The following color properties are available through `Theme.Colors`:

#### Main Brand Colors
- `primary`: #F04E23 (Prospr orange) - Primary brand color, used for accents and emphasis
- `secondary`: #003140 (Dark navy blue) - Secondary brand color, used for backgrounds
- `inputBorder`: #d0d0d0 (Light grey) - Used for input field borders
- `borderColor`: #d0d0d0 (Light grey) - Used for borders
- `shadow`: #22000000 (Semi-transparent black) - Used for shadows
- `transparent`: #00000000 (Transparent)
- `darkBlue`: #003140 (Dark navy blue) - Used for backgrounds

#### Semantic Colors
- `success`: #00A651 (Green) - Used for success states
- `warning`: #F7B500 (Yellow) - Used for warning states
- `error`: #D9001D (Red) - Used for error states

#### Neutrals
- `black`: #000000 (Black)
- `white`: #FFFFFF (White)
- `lightGrey`: #F5F5F7 (Very light grey) - Used for light backgrounds
- `midGrey`: #9E9E9E (Mid grey) - Used for disabled states
- `darkGrey`: #555555 (Dark grey) - Used for secondary text

#### UI Colors
- `backgroundPrimary`: secondary (Dark navy blue) - Main background color
- `backgroundSecondary`: #00242F (Darker navy) - Container backgrounds
- `textPrimary`: white - Primary text color
- `textSecondary`: darkGrey - Secondary text color

#### Button States
- `primaryButtonPressed`: Darker version of primary
- `secondaryButtonPressed`: Darker version of secondary
- `tertiaryButtonColor`: white - Used for tertiary buttons

#### Status Indicators
- `statusActive`: success
- `statusInactive`: midGrey
- `statusWarning`: warning
- `statusError`: error
        color: Theme.Colors.white
    }
}
```

### Available Color Properties

The following color properties are available in the Theme.Colors singleton:

#### Brand Colors
- `primary`: #F04E23 (Prospr orange)
- `secondary`: Theme.Colors.secondary (Dark blue-teal)

#### Semantic Colors
- `success`: #00A651 (GREEN)
- `warning`: #F7B500 (YELLOW)
- `error`: #D9001D (RED)

#### Neutrals
- `black`: #000000 (BLACK)
- `white`: #FFFFFF (WHITE)
- `lightGrey`: #F5F5F7 (LIGHT GREY)
- `midGrey`: #9E9E9E (MID GREY)
- `darkGrey`: #555555 (DARK GREY)

#### Derived/Utility Colors
- `backgroundPrimary`: white
- `backgroundSecondary`: lightGrey
- `textPrimary`: secondary
- `textSecondary`: darkGrey
- `borderColor`: #E0E0E0 (Light border color)

#### Button States
- `primaryButtonPressed`: Darker version of primary
- `secondaryButtonPressed`: Darker version of secondary

#### Status Indicators
- `statusActive`: success
- `statusInactive`: midGrey
- `statusWarning`: warning
- `statusError`: error

### Extending the Color System

If you need to add new colors to the system:

1. Add the new color property to `UI_new/Theme/Colors.qml`
2. Document the new color in this file
3. Update existing usage if needed

### Best Practices

1. **Never** use hardcoded color values in QML files
2. Use semantic color names when possible (e.g., `success` instead of `green`)
3. For derived colors, create a new property in Colors.qml rather than using Qt color manipulation in individual QML files
4. Use opacity carefully, as it can make colors inconsistent
5. Always check if colors remain accessible in both light and dark modes

## Typography System

The typography system ensures consistent text styles and readability across the application.

### Importing Typography

```qml
import "qrc:/UI_new/Theme" as Theme
```

### Using Typography

```qml
Text {
    text: "Heading"
    font.pixelSize: Theme.Typography.h1
    font.weight: Theme.Typography.weightBold
    font.family: Theme.Typography.primaryFontFamily
}
```

For pre-configured text styles:

```qml
Text {
    text: "Button Label"
    font: Theme.Typography.button
}
```

### Available Typography Properties

#### Font Families
- `primaryFontFamily`: The main application font
- `secondaryFontFamily`: Alternative font for specific elements
- `monospaceFontFamily`: For code or fixed-width text

#### Font Weights
- `weightLight`: Light (300)
- `weightNormal`: Normal (400) 
- `weightMedium`: Medium (500)
- `weightBold`: Bold (700)

#### Font Sizes
- Headers: `h1` through `h6` (32px to 16px)
- Body text: `bodyLarge`, `bodyMedium`, `bodySmall`
- UI elements: `buttonText`, `inputText`, `labelText`, `helperText`

#### Pre-configured Styles
- `title`: Large bold headings
- `subtitle`: Secondary headings
- `body`: Standard body text
- `button`: Button labels (uppercase)
- `caption`: Small helper text
- `industrial`: Larger text optimized for industrial touch screens

## Spacing System

The spacing system ensures consistent layouts and component sizing across the application.

### Using Spacing

```qml
Rectangle {
    width: Theme.Spacing.large
    height: Theme.Spacing.large
    anchors.margins: Theme.Spacing.medium
}
```

### Available Spacing Properties

#### Base Spacing
- `unit`: 8px (base unit for all spacing measurements)

#### Named Spacings
- Range from `none` (0px) to `xxxlarge` (96px)
- Examples: `small` (16px), `medium` (24px), `large` (32px)

#### Industrial UI
- `touchableMinHeight` and `touchableMinWidth`: 50px (minimum size for touch targets)
- `touchableSpacing`: 16px (minimum spacing between touchable elements)

#### Component Specific
- `buttonPadding`, `cardPadding`, `containerMargin`, etc.
- `bottomBarHeight`: Standard height for the bottom navigation bar

## Shadow System

The shadow system provides standardized elevation effects for components.

### Using Shadows

Import the theme and call the shadow application function:

```qml
import "qrc:/UI_new/Theme" as Theme

Rectangle {
    id: card
    // ... other properties
    
    Component.onCompleted: {
        Theme.Shadows.applyElevation(card, 2) // Apply elevation level 2
    }
}
```

### Available Elevation Levels

- `elevation1`: Subtle shadow (buttons, cards)
- `elevation2`: Light shadow (hovering elements)
- `elevation4`: Medium shadow (dialogs, popovers)
- `elevation8`: Pronounced shadow (side panels)
- `elevation16`: Heavy shadow (modal dialogs)

## Example Components

### Themed Button

```qml
import QtQuick 2.14
import "qrc:/UI_new/Theme" as Theme

Rectangle {
    id: button
    width: 120
    height: 40
    radius: 6
    
    property bool isPressed: false
    property string text: "Button"
    
    // Use theme colors for states
    color: isPressed ? Theme.Colors.primaryButtonPressed : Theme.Colors.primary
    
    // Apply shadow for elevation
    Component.onCompleted: {
        Theme.Shadows.applyElevation(this, 1)
    }
    
    Text {
        anchors.centerIn: parent
        text: button.text
        color: Theme.Colors.white
        font: Theme.Typography.button
    }
    
    MouseArea {
        anchors.fill: parent
        anchors.margins: -Theme.Spacing.xxsmall // Increase touch area
        onPressed: button.isPressed = true
        onReleased: button.isPressed = false
    }
}
```

### Themed Card

```qml
import QtQuick 2.14
import "qrc:/UI_new/Theme" as Theme

Rectangle {
    id: card
    width: 300
    height: 200
    radius: 8
    
    // Apply shadow
    Component.onCompleted: {
        Theme.Shadows.applyElevation(this, 2)
    }
    
    // Content container with proper spacing
    Item {
        anchors.fill: parent
        anchors.margins: Theme.Spacing.cardPadding
        
        // Card title
        Text {
            id: cardTitle
            anchors.top: parent.top
            anchors.left: parent.left
            anchors.right: parent.right
            text: "Card Title"
            font: Theme.Typography.subtitle
            color: Theme.Colors.textPrimary
        }
        
        // Card content
        Text {
            anchors.top: cardTitle.bottom
            anchors.topMargin: Theme.Spacing.small
            anchors.left: parent.left
            anchors.right: parent.right
            text: "Card content with proper typography and colors."
            font: Theme.Typography.body
            color: Theme.Colors.textSecondary
            wrapMode: Text.Wrap
        }
    }
}
```

## Adding Dark Mode Support to New Components

When creating new components, consider dark mode support by:

1. Using theme colors that automatically adjust (e.g., `textPrimary` instead of hardcoded colors)
2. Testing components in both light and dark modes
3. Connecting to the `Theme.Theme.themeChanged` signal if the component needs to handle theme changes dynamically

```qml
Component.onCompleted: {
    Theme.Theme.themeChanged.connect(function() {
        // Update component state based on new theme
    })
}
```

## Streamlined UI Navigation

The Light application uses a streamlined navigation approach with:

1. A centralized BottomBar component for primary navigation
2. Elimination of redundant navigation elements and headers
3. Consistent back button behavior for nested screens

This approach creates a cleaner, more modern UI that works well on both desktop and touch environments.
