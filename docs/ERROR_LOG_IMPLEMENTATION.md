# Error Log Management Implementation

## Overview

This document describes the implementation of the error log management feature in the Prospr Light Qt/QML application, including the connection between frontend button actions and backend functionality.

## Architecture

### Backend Components

#### ErrorManager (C++ Singleton)
- **Location**: `src/ErrorManager.h` and `src/ErrorManager.cpp`
- **QML Registration**: `Backend.Error 1.0` as singleton
- **Key Methods**:
  - `Q_INVOKABLE void reloadErrorsFromFile()` - Reloads errors from storage/errorlog.json
  - `Q_INVOKABLE void clearErrors()` - Clears all error entries from memory and storage
  - `Q_INVOKABLE void saveErrorsToFile()` - Saves current errors to storage
- **Signals**:
  - `errorsChanged()` - Emitted when error list changes

#### PrinterManager (C++ Singleton)
- **Location**: `src/printer/PrinterManager.h` and `src/printer/PrinterManager.cpp`
- **QML Registration**: `Backend.PrinterManager 1.0` as singleton
- **Key Methods**:
  - `Q_INVOKABLE void setStatusIndicators(bool system, bool warning, bool error)` - Sets all three status indicators
  - `Q_INVOKABLE void setStatusIndicator(int index, bool value)` - Sets individual status indicator
- **Signals**:
  - `statusIndicatorsChanged()` - Emitted when status indicators change

#### ErrorModel (C++ Model)
- **Location**: `src/models/ErrorModel.h` and `src/models/ErrorModel.cpp`
- **QML Registration**: `Backend.Error 1.0` as type
- **Purpose**: Provides QML ListView model for displaying error entries
- **Roles**: ErrorRole, CodeRole, TimestampRole

### Frontend Components

#### ErrorLog.qml (Main Screen)
- **Location**: `UI/Screens/ErrorLog.qml`
- **Features**:
  - Three action buttons: REFRESH, CLEAR, RESET
  - State management for operations (isRefreshing, confirmation dialogs)
  - Visual feedback (loading indicators, confirmation dialogs)
  - Direct connections to backend singletons

#### ErrorLogPanel.qml (Display Component)
- **Location**: `UI/Components/errorlog/ErrorLogPanel.qml`
- **Features**:
  - ListView with ErrorModel for displaying error entries
  - Integrated navigation controls (ErrorLogControls)
  - Automatic refresh on errorsChanged signal
  - Column headers with sorting indicators

#### ErrorLogControls.qml (Navigation Component)
- **Location**: `UI/Components/errorlog/ErrorLogControls.qml`
- **Features**:
  - Up/Down navigation buttons
  - Refresh button
  - Next/Previous page navigation
  - Signal-based communication with parent components

## Implementation Details

### Button Functionality

#### REFRESH Button
1. **Frontend**: Sets `isRefreshing = true` and shows loading indicator
2. **Backend Call**: `ErrorManager.reloadErrorsFromFile()`
3. **Response**: `errorsChanged()` signal resets `isRefreshing = false`
4. **Visual Feedback**: Rotating loading indicator while refreshing

#### CLEAR Button
1. **Frontend**: Shows confirmation dialog (`showClearConfirmation = true`)
2. **User Confirmation**: Calls `performClearLog()` if confirmed
3. **Backend Call**: `ErrorManager.clearErrors()`
4. **Response**: Automatically saves to file and emits `errorsChanged()`

#### RESET Button
1. **Frontend**: Shows confirmation dialog (`showResetConfirmation = true`)
2. **User Confirmation**: Calls `performResetLog()` if confirmed
3. **Backend Call**: `PrinterManager.setStatusIndicators(true, false, false)`
4. **Response**: Sets system status to normal (green), others to grey

### State Management

The ErrorLog.qml screen manages several states:
- `isRefreshing`: Boolean indicating refresh operation in progress
- `showClearConfirmation`: Boolean controlling clear confirmation dialog visibility
- `showResetConfirmation`: Boolean controlling reset confirmation dialog visibility

### Signal/Slot Connections

#### Automatic Connections
- `ErrorManager.errorsChanged` → ErrorLogPanel updates ListView
- `ErrorManager.errorsChanged` → ErrorLog resets refresh state
- `PrinterManager.statusIndicatorsChanged` → UI components can react to status changes

#### Manual Connections
- Button clicks → JavaScript functions in ErrorLog.qml
- Navigation controls → ListView positioning methods
- Confirmation dialogs → Backend method calls

## Testing

### QML Tests
- **Location**: `tests/qmltests/tst_errorlog.qml`
- **Coverage**:
  - Backend singleton availability
  - Method existence verification
  - Signal emission testing
  - Status indicator functionality

### Manual Testing
1. **Refresh**: Click refresh button, verify loading indicator and data update
2. **Clear**: Click clear button, confirm dialog, verify log is cleared
3. **Reset**: Click reset button, confirm dialog, verify status indicators reset
4. **Navigation**: Use navigation controls to scroll through error list

## Error Handling

### Backend Error Handling
- File I/O errors are logged to console with qWarning()
- Invalid JSON parsing is handled gracefully
- Missing backend objects are checked before method calls

### Frontend Error Handling
- Backend availability checks: `typeof ErrorManager !== 'undefined'`
- Method existence checks before calling
- Graceful degradation if backend is unavailable

## Configuration

### Storage Location
- Error log data: `storage/errorlog.json`
- Format: JSON array of error objects with error, code, and timestamp fields

### Status Indicators
- Index 0: System status (green = normal)
- Index 1: Warning status (yellow = warning)
- Index 2: Error status (red = error)

## Future Enhancements

1. **Filtering**: Add date range and error type filters
2. **Export**: Add functionality to export error logs
3. **Real-time Updates**: Add automatic refresh on new errors
4. **Pagination**: Add proper pagination for large error logs
5. **Search**: Add search functionality within error logs
