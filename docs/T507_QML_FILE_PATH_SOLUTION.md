# T507 QML File Path Solution

## Overview

This document provides a comprehensive solution for QML rendering issues on the Allwinner T507 embedded target. The core problem was that the Prospr Light application was attempting to load QML files from embedded Qt resources (qrc://), while the T507 environment is optimized for direct file access.

## Successful Testing

We've verified that QML rendering works correctly on the T507 when:
1. QML files are loaded directly from the filesystem using `file://` paths
2. Proper environment variables are set to redirect resource loading
3. Software rendering is used instead of hardware acceleration

The successful test displayed the proper UI with:
- Text rendering at the correct size and position
- Animations functioning properly
- Proper theme colors and layout
- Correct file path resolution

## Solution Components

### 1. Environment Variables

The following environment variables must be set before launching the application:

```bash
# Force direct file loading
export QT_FORCE_DIRECT_ASSET_LOADING=1
export QT_NO_USE_RESOURCE_FILES=1

# Map qrc:/ to file:/ paths
export QT_QML_PREFIX=/root

# Fallback paths for resource resolution
export QT_RESOURCE_FALLBACK_PATHS=/root:/root/UI_new:/root/UI_new/Theme

# QML import paths
export QML_IMPORT_PATH=/usr/local/Qt-5.15.4/qml:/root:/root/UI_new
export QML2_IMPORT_PATH=/usr/local/Qt-5.15.4/qml:/root:/root/UI_new

# Use software rendering
export QT_QUICK_BACKEND=software
export QSG_RENDER_LOOP=basic
export QT_QPA_NO_OPENGL=1
export QML_FORCE_SOFTWARE_RENDERER=1

# Platform configuration
export QT_QPA_PLATFORM=linuxfb:fb=/dev/fb0
export QT_QPA_FB_HIDECURSOR=1
export QT_QPA_GENERIC_PLUGINS=evdevtouch:/dev/input/event1
```

### 2. QML Path Correction

All QML files must use `file://` paths instead of `qrc:/` paths. This can be done by:

1. **Manual Path Replacement**: Update all source files to use `file://root/...` instead of `qrc:/...`
2. **Runtime Path Mapping**: Use the `QT_QML_PREFIX` and `QT_RESOURCE_FALLBACK_PATHS` environment variables
3. **QML Redirector**: Implement a QML singleton that intercepts and redirects path resolution

### 3. Deployment Process

The deployment process includes:
1. Copying all QML files to the target in the correct directory structure
2. Setting the necessary environment variables in the launcher script
3. Ensuring all dependencies (fonts, modules) are available on the target

### 4. Font Configuration

Ensure fonts are properly configured:
1. Install necessary font packages on the target
2. Set `QT_QPA_FONTDIR` to point to the font directory
3. Verify font loading through test applications

## Implementation Details

### Launcher Script

A launcher script (`/root/prospr_light_file_launcher.sh`) has been created that:
1. Sets up all necessary environment variables
2. Prepares the display and input devices
3. Verifies required QML modules are installed
4. Launches the application with the correct parameters

### QML Modifications

The main QML file has been updated to use file paths for all component loading:
- Changed `source: "qrc:/UI_new/..."` to `source: "file://root/UI_new/..."`
- Updated import statements to use local paths when necessary

### Testing Strategy

1. Create a simple standalone QML test application
2. Deploy and run on the T507 to verify basic rendering
3. Incrementally add more complex components to verify compatibility
4. Finally deploy and test the full application

## Troubleshooting

If QML rendering issues persist:

1. **Check Environment Variables**: Verify all variables are set correctly
2. **Examine QML Error Messages**: Look for specific path resolution errors
3. **Test with Minimal QML**: Use a simple test file to verify basic rendering works
4. **Check Module Availability**: Ensure all required QML modules are installed
5. **Inspect Font Loading**: Font errors can cause cascading rendering issues

## Additional Considerations

For future development:
1. Consider using a QML pre-processor to handle path differences between development and deployment
2. Implement a robust fallback mechanism for resource loading
3. Document platform-specific rendering differences
4. Create a CI/CD pipeline that automatically tests QML rendering on target hardware

## Lessons Learned

1. Embedded targets often require direct file access rather than embedded resources
2. Environment variables provide a powerful way to control Qt's resource loading behavior
3. Simple test applications are essential for isolating and resolving complex rendering issues
4. Software rendering is more reliable than hardware acceleration on embedded targets like the T507
