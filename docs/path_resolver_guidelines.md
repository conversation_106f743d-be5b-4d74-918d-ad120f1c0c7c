# PathResolver Guidelines

## Overview

The `PathResolver` is a singleton QML component that provides a unified approach to resource path handling across different platforms (desktop development and T507 embedded target). This guide explains how to use the PathResolver and the best practices for path handling in the Light project.

## Why PathResolver?

One of the most challenging aspects of developing for multiple platforms is handling resource paths consistently. The Qt resource system uses the `qrc:/` scheme in desktop environments, but on the T507 embedded target, resources are accessed from the filesystem using `file:///` URLs.

The PathResolver addresses these challenges by:

1. Automatically detecting the platform (desktop vs. embedded)
2. Resolving relative paths to the correct absolute paths based on the platform
3. Providing a caching mechanism for better performance
4. Handling error cases and providing fallbacks

## Importing PathResolver

To use PathResolver in your QML files, add the following import:

```qml
import UI_new.Core 1.0
```

## Basic Usage

### Resolving Paths

The core functionality of PathResolver is to convert relative paths to the appropriate absolute paths for the current platform:

```qml
// Basic path resolution
Image {
    source: PathResolver.resolvePath("UI_new/Assets/icons/home-icon.svg")
}

// Component loading with PathResolver
Loader {
    source: PathResolver.resolvePath("UI_new/Components/ProsprButton.qml")
}
```

### Specialized Resource Resolution

For common resource types, PathResolver provides specialized methods:

```qml
// For images and other assets
Image {
    source: PathResolver.resolveAsset("icons/home-icon.svg")
    // Equivalent to: PathResolver.resolvePath("UI_new/Assets/icons/home-icon.svg")
}

// For UI components
Loader {
    source: PathResolver.resolveComponent("ProsprButton.qml")
    // Equivalent to: PathResolver.resolvePath("UI_new/Components/ProsprButton.qml")
}

// For screens
Loader {
    source: PathResolver.resolveScreen("Home.qml")
    // Equivalent to: PathResolver.resolvePath("UI_new/Screens/Home.qml")
}
```

### Platform Detection

You can check whether the application is running on the embedded target:

```qml
// Check if running on embedded platform
Text {
    text: PathResolver.isEmbedded ? "Running on T507" : "Running on Desktop"
}
```

## Best Practices

1. **Always use PathResolver for resource paths**
   - Replace all hard-coded `qrc:/` or `file:///` paths with PathResolver calls

2. **Use specialized methods for common resource types**
   - `resolveAsset()` for images, icons, and other assets
   - `resolveComponent()` for UI components
   - `resolveScreen()` for application screens

3. **Import resources consistently**
   - When importing QML files (e.g., with `import` statements), use the same path structure that PathResolver expects

4. **Test on both platforms**
   - Test your changes on both the desktop development environment and the T507 embedded target

5. **Debug PathResolver issues**
   - Enable debug mode to see detailed logging: `PathResolver.debugMode = true`
   - Check console logs for path resolution attempts and failures

## Integration with Existing Code

The ContentLoader component has been updated to use PathResolver, but also maintains fallback methods for backward compatibility. When modifying existing components:

1. Update primary resource loading to use PathResolver
2. Keep fallback methods for robustness
3. Add debug logging to monitor path resolution

## Example: Complete Component Using PathResolver

```qml
import QtQuick 2.15
import QtQuick.Controls 2.15
import UI_new.Core 1.0
import "../Theme"

Rectangle {
    id: root
    width: 300
    height: 200
    color: Theme.Colors.background

    Column {
        anchors.centerIn: parent
        spacing: 10

        // Using PathResolver for an image
        Image {
            id: icon
            source: PathResolver.resolveAsset("icons/home-icon.svg")
            width: 48
            height: 48
            anchors.horizontalCenter: parent.horizontalCenter
        }

        // Text element
        Text {
            text: "Platform: " + (PathResolver.isEmbedded ? "Embedded" : "Desktop")
            color: Theme.Colors.text
            anchors.horizontalCenter: parent.horizontalCenter
        }

        // Button with dynamic loading of component
        Button {
            text: "Load Details"
            anchors.horizontalCenter: parent.horizontalCenter
            onClicked: detailLoader.active = true
        }

        // Loader using PathResolver
        Loader {
            id: detailLoader
            active: false
            source: PathResolver.resolveComponent("DetailView.qml")
            anchors.horizontalCenter: parent.horizontalCenter
        }
    }
}
```

## Troubleshooting

### Common Issues

1. **Component not found**
   - Check that the path is correct relative to the project root
   - Verify that the file is included in qml.qrc
   - Try using the full path format manually to check file accessibility

2. **Resource loading failures**
   - Check console logs for PathResolver debug output
   - Verify file permissions on the target device
   - Confirm that all resources are correctly deployed

3. **Path inconsistencies**
   - Use PathResolver consistently throughout your component
   - Avoid mixing different path resolution methods

### Debugging Techniques

1. Enable PathResolver debug mode:
   ```qml
   Component.onCompleted: {
       PathResolver.debugMode = true;
   }
   ```

2. Test path resolution in console:
   ```qml
   Component.onCompleted: {
       console.debug("Asset path: " + PathResolver.resolveAsset("icons/home-icon.svg"));
       console.debug("Component path: " + PathResolver.resolveComponent("ProsprButton.qml"));
       console.debug("Screen path: " + PathResolver.resolveScreen("Home.qml"));
   }
   ```

3. Clear the path cache if needed:
   ```qml
   Button {
       text: "Clear Cache"
       onClicked: PathResolver.clearCache()
   }
   ```

## Extending PathResolver

If additional path resolution functionality is needed:

1. Add new specialized resolution methods to PathResolver.qml
2. Add robust error handling and fallbacks
3. Update this documentation to reflect new capabilities
4. Test thoroughly on both platforms

## Conclusion

By consistently using PathResolver throughout the codebase, we can eliminate path-related issues between the desktop development environment and T507 embedded target, resulting in a more maintainable and robust application.
