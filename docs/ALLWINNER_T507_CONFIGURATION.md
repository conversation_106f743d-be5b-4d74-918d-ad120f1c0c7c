# Allwinner T507 Configuration Guide

## Overview
This document details the configuration requirements for running Prospr Light on the Allwinner T507 platform with optimal touchscreen rendering.

## Hardware Specifications

| Component | Specification |
|-----------|---------------|
| SoC       | Allwinner T507 (sun50iw9p1) |
| CPU       | Quad-Core Cortex-A53 ARM CPU |
| GPU       | Mali-G31 MP2 GPU |
| Display   | 1920x1080 touchscreen |
| OS        | Embedded Linux (Kernel 4.9) |

## Display & Rendering Configuration

### Platform Plugin Selection
The Allwinner T507 with Mali-G31 MP2 GPU does **not** fully support the EGLFS platform plugin. Instead, use the Linux Framebuffer (`linuxfb`) platform for optimal rendering:

```bash
export QT_QPA_PLATFORM=linuxfb:tty=/dev/fb0
```

### Framebuffer Configuration
Before launching the application, ensure the framebuffer is properly configured:

1. Set appropriate permissions:
   ```bash
   chmod 666 /dev/fb0
   ```

2. Verify/configure resolution (requires `fbset` utility):
   ```bash
   # Check current mode
   fbset
   
   # Set 1920x1080 mode if needed
   fbset -xres 1920 -yres 1080
   ```

3. Configure double buffering for smoother rendering:
   ```bash
   export QT_QPA_FB_DISABLE_DOUBLE_BUFFER=0
   export FB_MULTI_BUFFER=2
   ```

4. Force RGB888 color mode for better visual quality:
   ```bash
   export QT_QPA_FB_FORCE888=1
   ```

## Touchscreen Configuration

### Input Device Setup
The touchscreen input device needs proper configuration:

1. Set permissions on input device nodes:
   ```bash
   chmod 666 /dev/input/event*
   ```

2. Configure the Qt evdev touch plugin:
   ```bash
   export QT_QPA_GENERIC_PLUGINS=evdevtouch:/dev/input/event1
   export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1:rotate=0
   ```

   > **Note**: You may need to identify the correct event device for your touchscreen. Use `evtest` to identify the right device.

3. Hide the mouse cursor:
   ```bash
   export QT_QPA_FB_HIDECURSOR=1
   ```

## Font Rendering

### Typography System (April 2025 Update)
Prospr Light uses the following brand fonts:

- **Primary Font**: Young Serif - Used for headings and emphasis
  - Weights: Regular

- **Secondary Font**: Clash Grotesk - Used for body text and UI elements
  - Weights: Regular, Medium, Semibold, Bold

### Font Rendering Configuration

1. Set font directories:
   ```bash
   export QT_QPA_FONTDIR="$APP_DIR/fonts"
   ```

2. Configure DPI for appropriate font sizing on the 1920x1080 display:
   ```bash
   export QT_FONT_DPI=96
   ```

## Performance Optimization

### QML Rendering
For optimal performance on the Allwinner T507:

1. Use basic render loop:
   ```bash
   export QSG_RENDER_LOOP=basic
   ```

2. Disable QML cache to prevent stale cache issues:
   ```bash
   export QML_DISABLE_DISK_CACHE=1
   ```

3. Launch with minimal GPU/OpenGL dependence:
   ```bash
   ./light_qt5 --embedded --aarch64 --fullscreen --no-opengl
   ```

## Complete Environment Setup

For a complete deployment:

1. Copy the application binary to the target
2. Copy all required fonts to the fonts directory
3. Run the setup script with proper environment variables
4. Configure input devices
5. Launch the application with the correct arguments

## Troubleshooting

### Common Issues

1. **Display not showing or incorrect colors**
   - Verify framebuffer device exists: `ls -l /dev/fb0`
   - Check framebuffer mode: `fbset -i`
   - Verify permission: `ls -la /dev/fb0`

2. **Touch input not working**
   - Find correct input device: `ls -la /dev/input/event*`
   - Test input device: `evtest /dev/input/event1`
   - Verify permission: `ls -la /dev/input/event1`

3. **Application crashes on startup**
   - Check log for missing plugins: Look for "qt.qpa.plugin" messages
   - Verify environment variables are set correctly
   - Check for missing libraries: Use `ldd` on the binary

## Reference Environment Variables

Here's a complete list of environment variables used for the Allwinner T507:

```bash
# Platform configuration
export QT_QPA_PLATFORM=linuxfb:tty=/dev/fb0
export QT_EMBEDDED=1
export QT_AARCH64_TARGET=1
export TARGET_ARCH=aarch64

# Display configuration
export QT_SCREEN_SCALE_FACTORS=1
export QT_FONT_DPI=96
export DISPLAY_WIDTH=1920
export DISPLAY_HEIGHT=1080

# Input configuration
export QT_QPA_FB_HIDECURSOR=1
export QT_QPA_GENERIC_PLUGINS=evdevtouch:/dev/input/event1
export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1:rotate=0

# Virtual keyboard configuration
export QT_IM_MODULE=qtvirtualkeyboard

# Font configuration
export QT_QPA_FONTDIR="/path/to/fonts"

# Performance configuration
export QSG_RENDER_LOOP=basic
export QT_QPA_FB_FORCE888=1
export QT_QPA_FB_DISABLE_DOUBLE_BUFFER=0
export FB_MULTI_BUFFER=2
export QML_DISABLE_DISK_CACHE=1
```

## Testing

Before full deployment, you can test the display and touch configuration using the included test script. This will help verify that all input and display settings are correctly configured.
