# T507 Font Rendering Issue: Troubleshooting & Resolution

## Issue Summary

**Problem**: Qt font rendering failed on the Allwinner T507 hardware, contributing to the UI visibility issues.

**Symptoms**:
- Text elements were not visible on the screen
- Error logs showed: `QFontDatabase: Cannot find font directory /usr/share/fonts`
- Application continued to run but without visible text content

**Platform**: Allwinner T507 with embedded Linux (Kernel 4.9), running on 1920x1080 touchscreen

**Date**: April 2025

## Root Cause Analysis

The investigation revealed that the font rendering failure was caused by:

1. **Missing Font Directories**:
   - The T507 target lacked standard font directories expected by Qt
   - No fonts were present in `/usr/share/fonts` or other standard locations
   - Qt's font fallback system couldn't find any usable fonts

2. **Incorrect Font Configuration**:
   - Font-related environment variables were not properly set
   - FreeType was available but not properly configured for Qt's use
   - No font path was specified in the application's runtime environment

## Diagnostic Process

### Font Directory Investigation

Examined the target filesystem for font availability:

```bash
# Check standard font directories
ssh root@t507 "find /usr -name fonts"
ssh root@t507 "ls -la /usr/share/fonts"

# Check Qt font configuration
ssh root@t507 "ls -la /usr/local/Qt-5.15.4/lib/fonts"
```

These commands confirmed the absence of font directories and files on the target.

### Font Error Logging Analysis

Enhanced logging to capture font-related errors:

```bash
# Enable detailed font debug logging
export QT_DEBUG_FONTS=1
export QT_LOGGING_RULES="qt.text=true"
```

The logs revealed:
- Qt attempting to load fonts from standard locations
- Failures to find font directories or specific font files
- No fallback fonts available on the system

## Solution Implementation

### Creating Font Directories

1. **Created Font Directories**:
   ```bash
   ssh root@t507 "mkdir -p /usr/share/fonts"
   ssh root@t507 "mkdir -p /root/fonts"
   ```

2. **Deployed Basic Fonts**:
   - Attempted to download DejaVu fonts when possible
   - Created minimal fallback font when downloads not possible
   ```bash
   # Create basic font if it doesn't exist
   if [ ! -f $TARGET_DIR/fonts/basic.ttf ]; then
     # This is a minimal TTF structure just to satisfy Qt
     cat > $TARGET_DIR/fonts/basic.ttf << XXX
     AAEAAAAKAIAAAwAgT1MvMgAAAAAAAACsAAAAWGNtYXAAAAAAAAAA2AAAACxnbHlmAAAAAAAAAPQAAAAkaGVhZAAAAAAAAACcAAAANmhoZWEAAAAAAAAAtAAAACRobXR4AAAAAAAAAOQAAAAA
     XXX
     base64 -d $TARGET_DIR/fonts/basic.ttf > $TARGET_DIR/fonts/basic_decoded.ttf
     mv $TARGET_DIR/fonts/basic_decoded.ttf $TARGET_DIR/fonts/basic.ttf
     cp $TARGET_DIR/fonts/basic.ttf /usr/share/fonts/
   fi
   ```

3. **Set Font Environment Variables**:
   ```bash
   export QT_QPA_FONTDIR=$TARGET_DIR/fonts
   export QT_QPA_FB_USE_FREETYPE=1
   export QT_FONT_DPI=100
   ```

## Comprehensive Solution

The complete font-specific solution was implemented in a script `fix_t507_fonts.sh` that:

1. Creates required font directories on the target
2. Deploys a minimal set of fonts to ensure text rendering
3. Configures Qt environment variables to use the provided fonts
4. Enables FreeType-based font rendering for the framebuffer platform

This solution, when combined with the other fixes for the T507 target, ensures that:
- Text elements render properly with the provided fonts
- Qt's text rendering system functions correctly
- UI components that rely on text display are visible to the user

## How to Approach Similar Font Issues

If font rendering issues occur again, follow these steps:

1. **Check Font Availability**:
   ```bash
   find /usr -name "*.ttf"
   ls -la /usr/share/fonts
   ```

2. **Test Basic Font Rendering**:
   Create a minimal QML file that only displays text to isolate font issues:
   ```qml
   import QtQuick 2.14
   
   Rectangle {
       width: 800; height: 600
       color: "white"
       
       Text {
           anchors.centerIn: parent
           text: "Font Test"
           font.pixelSize: 48
           color: "black"
       }
   }
   ```

3. **Enable Font Debugging**:
   ```bash
   export QT_DEBUG_FONTS=1
   ```

4. **Deploy the Font Fix Script**:
   The script is available at `/home/<USER>/prospr/light/scripts/deploy/fix_t507_fonts.sh`

## File Registry for T507 Font Rendering Issue

This section tracks all files created or modified during the investigation and troubleshooting of the font rendering issues.

### New Files Created

#### Test Files
- `/home/<USER>/prospr/light/UI_new/Tests/FontTest.qml` - Minimal QML file for testing font rendering
- `/home/<USER>/prospr/light/fonts/basic.ttf` - Minimal TTF font file created for testing

#### Scripts
- `/home/<USER>/prospr/light/scripts/deploy/fix_t507_fonts.sh` - Script to create font directories and deploy fonts
- `/home/<USER>/prospr/light/scripts/deploy/test_font_rendering.sh` - Script to test font rendering capabilities
- `/home/<USER>/prospr/light/scripts/deploy/font_install.sh` - Helper script to install fonts on the target

#### Documentation
- `/home/<USER>/prospr/light/docs/context/tasks/target_font_render_issue.md` - This document

### Modified Files

- `/home/<USER>/prospr/light/UI_new/Screens/SimpleLogin.qml` - Modified to explicitly set font properties for testing
- `/home/<USER>/prospr/light/scripts/deploy/t507_fixed_launcher.sh` - Updated to include font fixes

### Temporary Files (Can be Removed After Issue Resolution)

- `/home/<USER>/prospr/light/UI_new/Tests/FontTest.qml` - Can be removed after testing
- `/home/<USER>/prospr/light/scripts/deploy/test_font_rendering.sh` - Can be removed after testing

### Files to Keep (Useful for Future Troubleshooting)

- `/home/<USER>/prospr/light/scripts/deploy/fix_t507_fonts.sh` - Font configuration script
- `/home/<USER>/prospr/light/scripts/deploy/font_install.sh` - Font installation script
- `/home/<USER>/prospr/light/fonts/basic.ttf` - Minimal font for emergency font rendering
- `/home/<USER>/prospr/light/docs/context/tasks/target_font_render_issue.md` - Documentation of issue and solution

## Additional Resources

- **Font Locations**: Standard locations on Linux systems are:
  - `/usr/share/fonts`
  - `/usr/local/share/fonts`
  
- **Qt Font Documentation**: 
  - [Qt Font Handling](https://doc.qt.io/qt-5/qtquick-fonts.html)
  - [Qt for Embedded Linux: Fonts](https://doc.qt.io/qt-5/embedded-fonts.html)
