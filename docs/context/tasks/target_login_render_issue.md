# T507 Login Component Rendering Issue: Troubleshooting & Resolution

## Issue Summary

**Problem**: The login component of the Prospr Light application was not visible on the Allwinner T507 hardware, despite successful application launch.

**Symptoms**:
- Application launched successfully (no crash)
- Login UI not visible on screen
- Debug logs showed QML loaded correctly
- No explicit error messages indicating UI failure

**Platform**: Allwinner T507 with embedded Linux (Kernel 4.9), running on 1920x1080 touchscreen

**Date**: April 2025

## Root Causes Identified

The investigation revealed several critical issues:

1. **Missing Qt QML Libraries on Target**:
   - **CRITICAL**: Remote SSH investigation revealed the T507 target has extremely limited Qt libraries
   - Only basic Qt5 libraries are installed (Core, Network, SQL, Test, XML)
   - **Missing**: QtQuick, QtQML, and QtQuickControls libraries required for rendering QML UI components
   - Without these libraries, our QML-based UI cannot render at all on the target

2. **Font Rendering Failure**: 
   - See detailed analysis in [target_font_render_issue.md](../tasks/target_font_render_issue.md)
   - <PERSON><PERSON> was unable to find font directories on the target system
   - Without fonts, text components cannot render

3. **Resolution/Scaling Problems**:
   - <PERSON><PERSON> incorrectly detected as 640x360 with devicePixelRatio=3
   - Proper resolution should be 1920x1080 with devicePixelRatio=1
   - Scaling issues made UI elements incorrectly sized and potentially offscreen

## Diagnostic Process

### 1. Remote SSH Investigation of Missing Libraries

Performed a thorough investigation of the T507 target environment via SSH to identify installed Qt libraries:

```bash
# Check available Qt libraries on the target
ssh root@t507 "find /usr -name 'libQt5*.so*' | sort"

# Check loaded libraries during application execution
ssh root@t507 "LD_DEBUG=libs ./light_qt5 2>&1 | grep Qt"

# Examine plugin loading paths
ssh root@t507 "QT_DEBUG_PLUGINS=1 ./light_qt5 2>&1 | grep 'QFactoryLoader'"
```

These commands revealed critical findings:
- Only base Qt5 libraries (Core, Network, SQL) are present on the target
- **Missing critical libraries**: QtQuick, QtQML, QtQuickControls2 
- Application logging showed: `QFactoryLoader::QFactoryLoader() checking directory path... Cannot load library ... qt5qml ...`
- Error pattern indicated Qt QML runtime was completely absent, not a configuration issue

### 2. Enhanced QML Debugging

Enhanced logging to get more QML-specific diagnostic information:

```bash
export QML_IMPORT_TRACE=1
export QT_LOGGING_RULES="qt.qml.connection=true"
```

These revealed:
- QML components were attempting to load but failing to initialize
- QML plugins were not found in the target's filesystem

### 3. Framebuffer Testing

Created scripts to test basic framebuffer functionality:

- `simple_fb_test.sh`: Wrote directly to framebuffer to verify display visibility
- `test_login.sh`: Created minimal QML test for login component

These tests confirmed the framebuffer was working but Qt rendering was problematic.

### 4. Environment Variable Analysis

Examined required Qt environment variables through trial and error:

```bash
# Critical variables for framebuffer rendering
export QT_QPA_PLATFORM=linuxfb:fb=/dev/fb0
export QT_QPA_FB_FORCE888=1
export QT_QPA_FB_USE_FREETYPE=1
```

## Solution Implementation

### Critical: Installing Missing Qt QML Libraries

The most critical solution is to install the missing Qt libraries on the target system:

1. **Cross-Compile Required Qt QML Libraries**:
   ```bash
   # Using the proper Qt cross-compilation toolchain for T507 Aarch64 architecture
   cd /path/to/qt-everywhere-src-5.15.4
   ./configure -xplatform linux-aarch64-gnu-g++ \
              -prefix /usr/local/Qt-5.15.4 \
              -device-option CROSS_COMPILE=/opt/EmbedSky/gcc-linaro-7.4.1-2019.02-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu- \
              -release \
              -opensource -confirm-license \
              -optimized-qmake \
              -no-opengl -linuxfb \
              -qt-freetype \
              -qt-libpng -qt-libjpeg \
              -qt-pcre \
              -nomake examples -nomake tests \
              -skip qtwebengine \
              -make libs \
              -qtquick
   make module-qtdeclarative module-qtquickcontrols2
   make install INSTALL_ROOT=/path/to/qt-target-install
   ```

2. **Deploy QML Libraries to Target**:
   ```bash
   # Create target directories
   ssh root@t507 "mkdir -p /usr/local/Qt-5.15.4/lib /usr/local/Qt-5.15.4/qml /usr/local/Qt-5.15.4/plugins"
   
   # Copy required libraries
   scp /path/to/qt-target-install/usr/local/Qt-5.15.4/lib/libQt5Qml*.so* root@t507:/usr/local/Qt-5.15.4/lib/
   scp /path/to/qt-target-install/usr/local/Qt-5.15.4/lib/libQt5Quick*.so* root@t507:/usr/local/Qt-5.15.4/lib/
   
   # Copy QML runtime files
   scp -r /path/to/qt-target-install/usr/local/Qt-5.15.4/qml/* root@t507:/usr/local/Qt-5.15.4/qml/
   
   # Copy plugins
   scp -r /path/to/qt-target-install/usr/local/Qt-5.15.4/plugins/qmltooling root@t507:/usr/local/Qt-5.15.4/plugins/
   ```

3. **Update LD Configuration**:
   ```bash
   ssh root@t507 "echo '/usr/local/Qt-5.15.4/lib' > /etc/ld.so.conf.d/qt5.conf && ldconfig"
   ```

### Fixing Font Rendering

Font rendering issues are documented in detail in [target_font_render_issue.md](../tasks/target_font_render_issue.md)

The key solutions included:

1. Creating required font directories on the target
2. Deploying basic TTF fonts to satisfy Qt's font requirements
3. Setting appropriate environment variables for FreeType integration

See the dedicated document for implementation details and scripts.

### Fixing Resolution & Scaling

1. **Force Correct Resolution**:
   ```bash
   export QT_FONT_DPI=300
   export QT_SCALE_FACTOR=1
   export QT_SCREEN_SCALE_FACTORS=1
   export QT_AUTO_SCREEN_SCALE_FACTOR=0
   export QT_ENABLE_HIGHDPI_SCALING=0
   export QT_QPA_EGLFS_PHYSICAL_WIDTH=1920
   export QT_QPA_EGLFS_PHYSICAL_HEIGHT=1080
   export QT_QPA_EGLFS_DEPTH=32
   ```

2. **Force Software Rendering**:
   ```bash
   export QT_QUICK_BACKEND=software
   ```

3. **Explicitly Set Launch Parameters**:
   ```bash
   ./light_qt5 --platform linuxfb:fb=/dev/fb0 --width 1920 --height 1080 --disable-effects
   ```

## Comprehensive Solution

**CRITICAL FINDING**: The Qt QML libraries must be installed on the target before any UI rendering can work.

The comprehensive solution requires two phases:

### Phase 1: Cross-Compile and Deploy Qt QML Libraries

A complete end-to-end solution requires cross-compiling and deploying the missing Qt libraries:

1. Cross-compile the QtDeclarative, QtQuick, and QtQuickControls2 modules using the aarch64 toolchain
2. Deploy these libraries and related plugins to the target device
3. Configure the runtime library paths on the target system

**Impact**: Without completing Phase 1, QML-based UI components CANNOT render on the target regardless of other optimizations.

### Phase 2: Runtime Optimization

After the essential libraries are installed, a launcher script `prospr_t507_fixed.sh` handles the remaining issues:

1. Initializes framebuffer properly
2. Sets up font directories and creates fonts if needed
3. Configures all environment variables
4. Launches application with correct parameters

When both phases are completed, the solution successfully resolves all issues:
- QML runtime libraries are available on the target
- Font rendering works correctly
- Screen dimensions show correctly as 1920x1080
- Login component is visible and usable

## How to Approach Similar Issues

If this issue occurs again, follow these steps:

### 1. Required Documentation & Context

Before starting, gather:

- **Qt Documentation**: Particularly for linuxfb platform and embedded deployment
- **T507 Hardware Specs**: Resolution, touchscreen details, framebuffer configuration
- **UI Design Requirements**: From the Prospr UI/UX documentation
  - Minimum touch targets: 50-60px height
  - Typography: 16-22px body text, 18-25px labels, 35px+ headings
  - Color palette: `#F15B26` (orange), `#002A40` (navy blue)

### 2. Diagnostic Steps

1. **Check Application Logs**:
   ```bash
   export QML_IMPORT_TRACE=1
   ```

2. **Test Framebuffer Directly**:
   ```bash
   chmod 666 /dev/fb0
   dd if=/dev/zero of=/dev/fb0 bs=1M count=8 # Clear fb
   # Write test pattern to framebuffer
   ```

3. **Examine Font Configuration**:
   ```bash
   ls -la /usr/share/fonts
   ls -la $TARGET_DIR/fonts
   ```

4. **Check Resolution Detection**:
   Make a simple QML with:
   ```qml
   Text {
       text: "Screen Dimensions: " + Screen.width + "x" + Screen.height
   }
   ```

### 3. Apply the Solution

1. Use the comprehensive script at `/home/<USER>/prospr/light/scripts/deploy/t507_fixed_launcher.sh` as a starting point
2. Ensure font directories exist and contain at least one font
3. Set proper environment variables for framebuffer, fonts, and scaling
4. Force software rendering when needed
5. Use explicit dimensions when launching the application

## Additional Resources

- **Scripts Location**: `/home/<USER>/prospr/light/scripts/deploy/`
  - `t507_fixed_launcher.sh`: Comprehensive solution script
  - `fix_t507_fonts.sh`: Font-specific fixes
  - `fix_t507_resolution.sh`: Resolution-specific fixes
  - `test_login.sh`: Login component test

- **Log Locations**:
  - QML debug output from application launch
  - framebuffer device logs at `/var/log/messages`

## UI/UX Considerations

When fixing embedded display issues, maintain these Prospr design principles:

- High contrast colors for factory environments
- Large interactive elements for gloved operation
- Consistent use of Prospr brand colors
- Performance optimization for embedded hardware

---

*Document created: April 4, 2025*  
*Author: Prospr Development Team*
