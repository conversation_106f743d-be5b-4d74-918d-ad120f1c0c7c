# T507 QML Rendering Solution

## Overview
This document describes the comprehensive solution implemented to fix QML rendering issues on the Allwinner T507 hardware for the Prospr Light application. The solution addresses multiple issues affecting the rendering of interactive UI components, particularly focusing on the login screen and navigation components.

**✅ IMPLEMENTED: April 4, 2025 - File System Approach Successfully Deployed**

## Root Causes
The investigation identified several key issues:

1. **Missing QML Modules**: Critical QtQuick modules were missing on the T507 device
2. **Rendering Configuration**: Incorrect backend settings for the Allwinner hardware
3. **Resource Path Resolution**: The `qrc:/` resource system not functioning correctly on the target
4. **QtGraphicalEffects**: Missing module required for UI shadows and effects
5. **Version Inconsistencies**: Mismatched import versions between QML files
6. **Path Format Inconsistencies**: Inconsistent use of file paths across QML files

## Solution Components

### 1. QtQuick Controls and Critical Modules
Ensured the following modules are properly installed and linked:
- QtQuick.Controls and QtQuick.Controls.2
- QtQuick.Templates and QtQuick.Templates.2
- QtQuick.Layouts
- **QtGraphicalEffects** (critical for shadows and visual effects)

### 2. Platform Configuration
```bash
# Framebuffer configuration
export QT_QPA_PLATFORM=linuxfb:fb=/dev/fb0
export QT_QPA_FB_HIDECURSOR=1
export QT_QPA_GENERIC_PLUGINS=evdevtouch:/dev/input/event1
export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1:rotate=0
```

### 3. Rendering Configuration
```bash
# Software rendering for stability
export QT_QUICK_BACKEND=software
export QSG_RENDER_LOOP=basic
export QT_QPA_NO_OPENGL=1
export QML_FORCE_SOFTWARE_RENDERER=1
```

### 4. Resource Path Resolution
```bash
# CRITICAL: Direct file path configuration
export QT_FORCE_DIRECT_ASSET_LOADING=1
export QT_NO_USE_RESOURCE_FILES=1  # Force application to ignore QRC resources
export QT_QML_PREFIX=/root
export QT_RESOURCE_FALLBACK_PATHS=/root:/root/UI_new:/root/UI_new/Theme

# Extended import paths
export QML_IMPORT_PATH=/usr/local/Qt-5.15.4/qml:/root:/root/UI_new
export QML2_IMPORT_PATH=/usr/local/Qt-5.15.4/qml:/root:/root/UI_new
```

### 5. Controls and Font Configuration
```bash
# Controls styling
export QT_QUICK_CONTROLS_STYLE=Fusion
export QT_QUICK_CONTROLS_MOBILE=false

# Font configuration
export QT_QPA_FONTDIR=/usr/share/fonts
export QT_FONT_DPI=96
```

### 6. Performance Optimization
```bash
# Disable JIT for better stability
export QV4_FORCE_INTERPRETER=1
export QT_ENABLE_REGEXP_JIT=0
```

## Implementation Scripts

### Primary Solution Scripts

1. **t507_comprehensive_file_solution.sh**
   - Final production solution that fully implements the file system approach
   - Builds the application for aarch64 target using the universal build script
   - Fixes all QML files to use file:/// paths instead of qrc:/ paths
   - Deploys all necessary files to the T507 target
   - Sets up autostart configuration using the appropriate init system
   - Includes comprehensive logging and error handling

2. **prospr_launcher.sh** (generated by the solution script)
   - Production-ready launcher with all necessary environment variables
   - Properly initializes the framebuffer device
   - Sets font configuration
   - Includes extensive logging for troubleshooting

### Supporting Scripts

1. **t507_fixup_qml.sh**
   - Utility script to fix QML file paths from qrc:/ to file:///
   - Handles special cases for imports and resources
   - Processes all QML files in the UI_new directory

2. **deploy_mainlayout_fix.sh**
   - Targeted fix for MainLayout.qml import issues
   - Specifically addresses component path resolution

3. **t507_integrated_deploy.sh**
   - Combined build and deploy script
   - Includes font installation and environment setup

## Testing and Verification

Comprehensive testing was performed using:

1. **Path Resolution Testing**:
   - Direct comparison of `file:/` vs `qrc:/` path loading
   - Confirmed that direct file path loading works correctly while QRC paths fail

2. **Component Testing**:
   - Verified rendering of text, buttons, rectangles, and input fields
   - Tested with Prospr's color palette and styling

3. **Full Application Testing**:
   - Tested login screen rendering with proper styling
   - Verified all interactive components display correctly

## User Interface Implementation

The implementation follows Prospr's UI guidelines for industrial environments:

- **Touch Targets**: Large buttons (60px height) optimized for factory operators
- **Color Palette**: Using the official Prospr colors:
  - Primary: #F15B26 (orange)
  - Secondary: #002A40 (navy blue)
  - Supporting semantic colors for status indicators
- **Typography**: Increased font sizes (18-36px) for better visibility
- **Contrast**: High contrast between text and backgrounds for industrial environments

## Implemented Solution: File System Approach

✅ **COMPLETED APRIL 2025**

1. **QRC Resource Replacement**:
   - All QML files modified to use `file:///root/` paths instead of `qrc:/` paths
   - All loader sources updated to use absolute file paths
   - Dynamic path resolver added to main.qml to handle any remaining qrc:/ references

2. **Path Standardization**:
   - Standardized approach to use absolute paths with file:/// prefix
   - Updated Components and Theme imports to use full paths
   - Created utility scripts to automate the conversion process

3. **Environment Configuration**:
   - Comprehensive environment variable setup in launcher script
   - Proper font handling and fallback configuration
   - Startup scripts integrated with the target's init system

## Maintenance Notes

When updating the Qt libraries or the Prospr Light application:

1. Always verify the QtQuick Controls and QtGraphicalEffects modules are present
2. Ensure the launcher script is using the correct paths
3. Check that the resource path configuration is maintained
4. Test the application with all screens and interactive components
5. Use the file system approach for all QML files - do not revert to QRC resources
6. When adding new QML files, ensure they use `file:///root/` paths for all resources
7. Run the `t507_fixup_qml.sh` script after making significant QML changes

## File Path Conversion Guidelines

When developing new QML components or modifying existing ones:

1. Replace any `qrc:/` references with `file:///root/`
2. For relative imports like `"Components"`, use `"file:///root/UI_new/Components"`
3. For image sources, use `source: "file:///root/UI_new/Assets/image.png"`
4. For Loader components, always use absolute paths: `source: "file:///root/UI_new/Screens/Home.qml"`

## Performance Notes

The file system approach has shown the following characteristics:

1. Faster application startup time
2. Lower memory usage during runtime
3. More reliable rendering on the T507 hardware
4. Easier debugging and modification of UI components on the target
