# Prospr Light - Industrial Printer Management System

Prospr Light is a Qt5/QML-based industrial printer management application designed for CIJ (Continuous Ink Jet) printers. Built for reliability in factory environments, it provides a modern touch-optimized interface for printer control, label creation, and system management.

## 🎯 Target Platforms

- **Development**: Ubuntu 22.04.x LTS, macOS (Homebrew)
- **Production**: Allwinner T507 aarch64 embedded Linux
- **Testing**: Cross-platform compatibility via Qt5

## ✨ Key Features

### Printer Management
- Real-time printer status monitoring
- Service function control (start/stop operations)
- Print job management
- Hardware integration and diagnostics

### User Interface
- Touch-optimized industrial design
- Centralized navigation with bottom bar
- Responsive scaling for different screen sizes
- Dark theme optimized for factory environments

### Content Management
- Label creation and editing tools
- Print file management system
- USB import/export capabilities
- Template and message libraries

### System Administration
- Multi-user authentication system
- Role-based permission management
- System configuration and settings
- Comprehensive error logging and diagnostics

### Industrial Features
- Robust error handling and recovery
- Persistent configuration management
- Real-time status indicators
- Factory-optimized font rendering

## 📁 Project Structure

```
light/
├── main.qml                  # Application entry point
├── main.cpp                  # C++ application bootstrap
├── src/                      # C++ backend components
│   ├── models/              # Data models (Permissions, Printfile, Service, Error)
│   ├── printer/             # Printer management (PrinterManager, ServiceFunction)
│   ├── printfile/           # Print file management system
│   ├── ConfigManager.*      # Application configuration
│   ├── UserManager.*        # User authentication and permissions
│   ├── ErrorManager.*       # Error logging and management
│   └── TimeHandler.*        # Date/time utilities
├── UI/                      # QML user interface
│   ├── Core/               # Application logic (AppController, ContentLoader, PathResolver)
│   ├── Components/         # Reusable UI components
│   ├── Screens/            # Application screens and views
│   ├── Theme/              # Design system (Colors, Typography, Spacing)
│   └── Assets/             # Images, icons, and visual resources
├── tests/                   # Comprehensive test suite
│   ├── qmltests/           # QML/JavaScript unit tests
│   ├── uitests/            # UI component integration tests
│   └── pathresolver/       # Cross-platform path resolution tests
├── scripts/                 # Build, deployment, and utility scripts
├── fonts/                   # Font files for embedded deployment
├── 3rdparty/               # External dependencies
│   └── cutekeyboard/       # Virtual keyboard component (submodule)
├── qt_aarch64_builder/     # Docker-based cross-compilation environment
├── docs/                   # Technical documentation
├── config/                 # Configuration files and templates
├── storage/                # Runtime data storage
└── build/                  # Build output directory
```

## 🚀 Quick Start

### Prerequisites

- **Linux**: Ubuntu 22.04.x LTS (recommended)
- **macOS**: macOS 10.15+ with Homebrew
- **Qt**: Qt 5.15.x (automatically installed by setup script)
- **Git**: For repository and submodule management

### Initial Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/paaxware/light.git
   cd light
   ```

2. **Initialize submodules**
   ```bash
   git submodule update --init --recursive
   ```

3. **Set up development environment**
   ```bash
   ./scripts/setup_dev_env.sh
   ```

### Building

#### Development Build (Recommended)
```bash
./scripts/build.sh -t x86_64 -b debug -c
```

#### Production Build
```bash
./scripts/build.sh -t x86_64 -b release -c
```

#### Cross-compile for Embedded Target
```bash
./scripts/build.sh -t aarch64 -b release -c
```

### Running

```bash
./scripts/run.sh
```

### Testing

```bash
# Run all tests
./tests/run_tests.sh

# Run specific test suites
cd tests/qmltests && qmake && make && ./tst_qml
cd tests/uitests && qmake && make && ./tst_ui
```

## 🔧 Development Tools

### Code Quality
```bash
# Format QML code
./scripts/format_qml.sh

# Lint QML code
./scripts/lint_qml.sh
```

### Configuration Management
```bash
# Set application configuration
./scripts/set_config.sh
```

### Font Management (Embedded Targets)
```bash
# Prepare fonts for deployment
./scripts/prepare_fonts.sh
```

## 📋 System Requirements

### Development Dependencies

**Ubuntu 22.04.x LTS:**
- Qt 5.15.x (qtdeclarative5-dev, qtquickcontrols2-5-dev)
- Build tools (build-essential, cmake, pkg-config)
- YAML support (libyaml-cpp-dev)
- Virtual keyboard support (qtvirtualkeyboard-plugin)

**macOS:**
- Qt 5.15.x (via Homebrew: `brew install qt@5`)
- Xcode Command Line Tools
- YAML support (via Homebrew: `brew install yaml-cpp`)

### Runtime Requirements

**Desktop:**
- Qt 5.15.x runtime libraries
- OpenGL support
- Audio system (for notifications)

**Embedded (T507):**
- Linux kernel 4.9+
- Framebuffer support
- Touch input support
- 512MB+ RAM, 1GB+ storage

### Qt Modules Used
- **QtQuick 2.15**: Core QML engine
- **QtQuick.Controls 2.15**: UI controls and components
- **QtQuick.Layouts**: Layout management
- **QtGraphicalEffects**: Visual effects and shadows
- **QtTest**: Unit testing framework

## 🧩 Third-Party Components

### CuteKeyboard
Virtual keyboard component for touch interfaces.

- **Source**: Modified Qt Examples component
- **Location**: `3rdparty/cutekeyboard/` (Git submodule)
- **Purpose**: Provides on-screen keyboard for embedded deployment
- **Build**: Automatically built by `setup_dev_env.sh`

### Font Libraries
Comprehensive font support for embedded deployment.

- **DejaVu**: Sans, Serif, and Mono families
- **Liberation**: Sans, Serif, and Mono families
- **Ubuntu**: Complete Ubuntu font family
- **Noto**: Emoji and international character support
- **Droid**: Fallback font support

## 🏗️ Build System

### Build Targets
- **x86_64**: Local development (Linux/macOS)
- **aarch64**: Cross-compilation for T507 embedded target

### Build Types
- **Debug**: Development builds with debugging symbols
- **Release**: Optimized production builds

### Cross-Compilation
Uses Docker-based toolchain in `qt_aarch64_builder/` for consistent cross-compilation to aarch64 targets.

## 📚 Documentation

- **[DEVELOPER_GUIDE.md](DEVELOPER_GUIDE.md)**: Comprehensive development guide and workflow
- **[ARCHITECTURE.md](ARCHITECTURE.md)**: System architecture and design overview
- **[docs/](docs/)**: Technical documentation and implementation guides
- **[context/](context/)**: Development context and best practices

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Follow the development workflow in [DEVELOPER_GUIDE.md](DEVELOPER_GUIDE.md)
4. Run tests: `./tests/run_tests.sh`
5. Submit a pull request

## 📄 License

This project is proprietary software developed by Paaxware for industrial printer management applications.