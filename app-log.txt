Running for local development environment...
Running Light application with Qt ...
Qt base directory: 
Failed to load font: "NotoColorEmoji.ttf"
qml: PathResolver: Platform detection - isEmbedded: false
qml: PathResolver: Using prefix: qrc:/
ConfigManager: Loading INI from: "/Users/<USER>/.config/Prospr/Light.ini"
ConfigManager: Loaded windowWidth: 1920 , windowHeight: 1080
qml: Using screen device pixel ratio: 2
qml: Using ConfigManager-based scale factor: 1
qt.qpa.fonts: Populating font family aliases took 183 ms. Replace uses of missing font family "Sans" with one that exists to avoid this cost. 
qml: [MainLayout] currentScreen changed to: LogIn
qml: [MainLayout] navButtons recompute for screen: LogIn
qml: [MainLayout] navButtons for LogIn: []
qml: [BottomBar] navButtons changed: [] currentScreen: LogIn
qml: [BottomBar] currentScreen changed: LogIn navButtons: []
qml: PathResolver: Resolving 'UI/Screens/LogIn.qml' to 'qrc:/UI/Screens/LogIn.qml'
qml: PathResolver: Resolving 'UI/Assets/backgrounds/system-background.png' to 'qrc:/UI/Assets/backgrounds/system-background.png'
qml: PathResolver: Resolving 'UI/Assets/icons/home-small.svg' to 'qrc:/UI/Assets/icons/home-small.svg'
qml: PathResolver: Resolving 'UI/Assets/icons/logout.svg' to 'qrc:/UI/Assets/icons/logout.svg'
qml: PathResolver: Resolving 'UI/Assets/icons/user-circle.svg' to 'qrc:/UI/Assets/icons/user-circle.svg'
qml: PathResolver: Resolving 'UI/Assets/icons/user.svg' to 'qrc:/UI/Assets/icons/user.svg'
qml: PathResolver: Resolving 'UI/Assets/icons/create.svg' to 'qrc:/UI/Assets/icons/create.svg'
qml: [MainLayout] Loader loaded new screen: LogIn
qml: [BottomBar] onCompleted - navButtons: [] currentScreen: LogIn
qml: [BottomBar] onCompleted - navButtons: []
qml: MainLayout.navigateToRequested signal not found for connection.
qml: MainLayout configured successfully
qml: Initializing PathResolver singleton...
qml: PathResolver detected platform: Desktop
qml: PathResolver using prefix: qrc:/
qml: Application starting
qml: === Platform Information ===
qml: OS: osx
qml: Embedded mode: false
qml: Aarch64 target: false
qml: Screen dimensions: 1920x1080
qml: Screen DPI: 81.50000122400722
qml: Device pixel ratio: 2
qml: === Display Configuration ===
qml: Target resolution: 1920x1080
qml: Actual screen: 1920x1080
qml: Device pixel ratio: 2
qml: Scale factor: 1
qml: Visibility mode: 2
qml: Initializing application...
qml: Application initialized
qml: Main layout size: 1920x1052
qml: Main layout scale: 1
qml: === Deployment Debug Info ===
qml: QML_IMPORT_PATH: not set
qml: Current directory: /Users/<USER>/prospr/ltempp/build/x86_64-debug/light_qt5.app/Contents/MacOS/light_qt5
qml: Qt.resolvedUrl(./): qrc:/
Qt Quick Layouts: Polish loop detected. Aborting after two iterations.
qml: Login attempt with username: sdasd
qml: [NavigationManager] go() called. Navigating from: LogIn to: Home Current before: LogIn
qml: [NavigationManager] go() called. Navigating from: LogIn to: Home Current before: LogIn
qml: [MainLayout] currentScreen changed to: Home
qml: [BottomBar] currentScreen changed: Home navButtons: []
qml: [MainLayout] navButtons recompute for screen: Home
qml: [MainLayout] navButtons for Home: [{"label":"SERVICE","icon":"service.svg","screen":"Service"},{"label":"PRINT","icon":"print.svg","screen":"Print"},{"label":"SYSTEM","icon":"system.svg","screen":"Settings"}]
qml: [BottomBar] navButtons changed: [{"label":"SERVICE","icon":"service.svg","screen":"Service"},{"label":"PRINT","icon":"print.svg","screen":"Print"},{"label":"SYSTEM","icon":"system.svg","screen":"Settings"}] currentScreen: Home
qml: PathResolver: Resolving 'UI/Screens/Home.qml' to 'qrc:/UI/Screens/Home.qml'
qml: PathResolver: Resolving 'UI/Components/ShutdownDialog.qml' to 'qrc:/UI/Components/ShutdownDialog.qml'
qml: PathResolver: Resolving 'UI/Assets/icons/forbidden.svg' to 'qrc:/UI/Assets/icons/forbidden.svg'
qml: [MainLayout] Loader loaded new screen: Home
qml: [NavigationManager] go() after update. Current: Home History: ["LogIn"]
qml: [NavigationManager] go() after update. Current: Home History: ["LogIn"]
qml: [NavigationManager] Emitting navigateToRequested signal with to: Home from: LogIn
qml: [MainLayout] NavigationManager.navigateToRequested fired. Navigating to: Home from: LogIn
qml: [NavigationManager] showUserProfile() called. Navigating to UserProfile.
qml: [NavigationManager] go() called. Navigating from:  to: settings/UserPermissions Current before: Home
qml: [NavigationManager] go() called. Navigating from:  to: settings/UserPermissions Current before: Home
qml: [NavigationManager] go() after update. Current: settings/UserPermissions History: ["LogIn","Home"]
qml: [NavigationManager] go() after update. Current: settings/UserPermissions History: ["LogIn","Home"]
qml: [NavigationManager] Emitting navigateToRequested signal with to: settings/UserPermissions from: 
qml: [MainLayout] NavigationManager.navigateToRequested fired. Navigating to: settings/UserPermissions from: 
qml: [MainLayout] currentScreen changed to: settings/UserPermissions
qml: [BottomBar] currentScreen changed: settings/UserPermissions navButtons: [{"label":"SERVICE","icon":"service.svg","screen":"Service"},{"label":"PRINT","icon":"print.svg","screen":"Print"},{"label":"SYSTEM","icon":"system.svg","screen":"Settings"}]
qml: [MainLayout] navButtons recompute for screen: settings/UserPermissions
qml: [MainLayout] navButtons for settings/DateTime|UserPermissions|Language: [{"label":"BACK","icon":"back.svg","screen":"Settings"},{"label":"SAVE CHANGES","icon":"save-changes.svg","action":"save","screen":""}]
qml: [BottomBar] navButtons changed: [{"label":"BACK","icon":"back.svg","screen":"Settings"},{"label":"SAVE CHANGES","icon":"save-changes.svg","action":"save","screen":""}] currentScreen: settings/UserPermissions
qml: PathResolver: Resolving 'UI/Screens/settings/UserPermissions.qml' to 'qrc:/UI/Screens/settings/UserPermissions.qml'
qml: PathResolver: Resolving 'UI/Assets/icons/left-arrow-black.svg' to 'qrc:/UI/Assets/icons/left-arrow-black.svg'
qml: PathResolver: Resolving 'UI/Assets/icons/right-arrow-white.svg' to 'qrc:/UI/Assets/icons/right-arrow-white.svg'
qml: PathResolver: Resolving 'UI/Assets/Images/dropdown-arrow.png' to 'qrc:/UI/Assets/Images/dropdown-arrow.png'
qml: [MainLayout] Loader loaded new screen: settings/UserPermissions
qml: [BottomBar] navButton BACK clicked: calling NavigationManager.goBack()
qml: [NavigationManager] goBack() called. Current: settings/UserPermissions History: ["LogIn","Home"]
qml: [NavigationManager] goBack() called. Current: settings/UserPermissions History: ["LogIn","Home"]
qml: [NavigationManager] goBack() navigating back to: Home from: settings/UserPermissions History after pop: ["LogIn"]
qml: [NavigationManager] Emitting navigateToRequested signal with to: Home from: settings/UserPermissions
qml: [MainLayout] NavigationManager.navigateToRequested fired. Navigating to: Home from: settings/UserPermissions
qml: [MainLayout] currentScreen changed to: Home
qml: [BottomBar] currentScreen changed: Home navButtons: [{"label":"BACK","icon":"back.svg","screen":"Settings"},{"label":"SAVE CHANGES","icon":"save-changes.svg","action":"save","screen":""}]
qml: [MainLayout] navButtons recompute for screen: Home
qml: [MainLayout] navButtons for Home: [{"label":"SERVICE","icon":"service.svg","screen":"Service"},{"label":"PRINT","icon":"print.svg","screen":"Print"},{"label":"SYSTEM","icon":"system.svg","screen":"Settings"}]
qml: [BottomBar] navButtons changed: [{"label":"SERVICE","icon":"service.svg","screen":"Service"},{"label":"PRINT","icon":"print.svg","screen":"Print"},{"label":"SYSTEM","icon":"system.svg","screen":"Settings"}] currentScreen: Home
qml: [MainLayout] Loader loaded new screen: Home
qml: [BottomBar] Home icon clicked: navigating to Home (reset history)
qml: [NavigationManager] goHome() called. Resetting history and navigating to Home.
qml: [MainLayout] NavigationManager.navigateToRequested fired. Navigating to: Home from: 
qml: [NavigationManager] showErrorLog() called. Navigating to ErrorLog.
qml: [NavigationManager] go() called. Navigating from:  to: ErrorLog Current before: Home
qml: [NavigationManager] go() called. Navigating from:  to: ErrorLog Current before: Home
qml: [NavigationManager] go() after update. Current: ErrorLog History: ["Home"]
qml: [NavigationManager] go() after update. Current: ErrorLog History: ["Home"]
qml: [NavigationManager] Emitting navigateToRequested signal with to: ErrorLog from: 
qml: [MainLayout] NavigationManager.navigateToRequested fired. Navigating to: ErrorLog from: 
qml: [MainLayout] currentScreen changed to: ErrorLog
qml: [BottomBar] currentScreen changed: ErrorLog navButtons: [{"label":"SERVICE","icon":"service.svg","screen":"Service"},{"label":"PRINT","icon":"print.svg","screen":"Print"},{"label":"SYSTEM","icon":"system.svg","screen":"Settings"}]
qml: [MainLayout] navButtons recompute for screen: ErrorLog
qml: [MainLayout] navButtons for ErrorLog: [{"label":"SERVICE","icon":"service.svg","screen":"Service"},{"label":"PRINT","icon":"print.svg","screen":"Print"},{"label":"SYSTEM","icon":"system.svg","screen":"Settings"}]
qml: [BottomBar] navButtons changed: [{"label":"SERVICE","icon":"service.svg","screen":"Service"},{"label":"PRINT","icon":"print.svg","screen":"Print"},{"label":"SYSTEM","icon":"system.svg","screen":"Settings"}] currentScreen: ErrorLog
qml: PathResolver: Resolving 'UI/Screens/ErrorLog.qml' to 'qrc:/UI/Screens/ErrorLog.qml'
qml: PathResolver: Resolving 'UI/Assets/icons/reload.svg' to 'qrc:/UI/Assets/icons/reload.svg'
qml: PathResolver: Resolving 'UI/Assets/icons/chevron-down.svg' to 'qrc:/UI/Assets/icons/chevron-down.svg'
qml: [MainLayout] Loader loaded new screen: ErrorLog
