---
type: "agent_requested"
description: "General Development Considerations"
---
# Prospr Light Project Configuration Rules & Guidelines

## Project Structure & Architecture

- This is a **Qt5/QML industrial printer management application** targeting:
  - Desktop: Ubuntu 22.04
  - Embedded: Allwinner T507
- Follow the established modular architecture:
  - **C++ backend** for business logic
  - **QML frontend** for UI
- Use the existing directory structure:
  - `UI/Core`, `UI/Components`, `UI/Screens`, `UI/Theme`, `UI/Assets`
- All new QML files **must be added** to `qml.qrc` or they won't be available at runtime

---

## Theme System (**CRITICAL**)

- ❌ NEVER use hardcoded colors – always use `Theme.Colors` singleton
- ❌ NEVER use hardcoded typography – always use `Theme.Typography` singleton
- ❌ NEVER use hardcoded spacing – always use `Theme.Spacing` singleton
- ✅ Import theme as:
  ```qml
  import "qrc:/UI/Theme" as Theme
  ```
- ✅ Use **semantic** color names:
  - e.g. `Theme.Colors.success` instead of `Theme.Colors.green`
- ✅ Apply shadows using:
  ```qml
  Theme.Shadows.applyElevation(target, level)
  ```

---

## Path Resolution (**CRITICAL**)

- ✅ ALWAYS use `PathResolver` for resource paths to ensure desktop/embedded compatibility
- ✅ Import:
  ```qml
  import UI.Core 1.0
  ```
- ✅ Use specialized methods:
  ```qml
  PathResolver.resolveAsset("icons/file.svg")
  PathResolver.resolveComponent("Button.qml")
  PathResolver.resolveScreen("Home.qml")
  ```
- ❌ NEVER use hardcoded `qrc:/` or `file:///` paths

---

## Code Style & Standards

- Follow **Qt/QML naming conventions and best practices**
- Use `camelCase` for variables/functions in QML and C++
- Use **4-space indentation** in QML
- QML property order:
  1. `id`
  2. Dimensions (e.g. `width`, `height`)
  3. Anchors
  4. Colors
  5. Custom properties
- Prefer **declarative bindings** over imperative JavaScript
- Use **explicit types** for properties instead of `var` when possible

---

## Component Development

- Leverage existing core components:
  - `AppController`, `ContentLoader`, `PathResolver`
- Reuse existing components and follow their patterns
- Use **Qt Quick Layouts** for responsive design
- Ensure **touch-friendly sizing** (minimum 50px targets)
- Test components on **both desktop and embedded** platforms

---

## Development Workflow

- Use provided scripts:
  ```bash
  ./scripts/build.sh
  ./scripts/run.sh
  ./scripts/setup_dev_env.sh
  ```
- Run QML linting and formatting:
  ```bash
  ./scripts/lint_qml.sh
  ./scripts/format_qml.sh
  ```
- Branch naming convention:  
  `<ticket-number>-<meaningful-name>`
- PR Process:
  1. Create branch
  2. Make changes
  3. Commit
  4. Push
  5. Create PR
  6. Squash merge

---

## Platform Considerations

- Target **Qt 5.15.4** for embedded compatibility
- Use `PathResolver` to handle:
  - Desktop (`qrc:/`)
  - Embedded (`file:///`) resource differences
- Consider **industrial requirements**:
  - Durability
  - Touch interface
- Test **display scaling** on T507 embedded target

---

## Testing & Quality

- Write **unit tests** for new functionality
- Test on both desktop and embedded platforms
- Use `qmllint` configuration from `qmllint.ini`
- Ensure:
  - All QML files pass linting and formatting checks
  - All resources are properly included in `qml.qrc`

---

## Dependencies & Package Management

- Use appropriate package managers:
  - `apt` for Ubuntu
  - `brew` for macOS
- Avoid manual file editing
- Follow setup procedures in:
  ```bash
  scripts/setup_dev_env.sh
  ```
- Maintain compatibility with:
  - **Ubuntu 22.04 LTS**
  - **macOS** for development

---

## Documentation

- Update relevant documentation when adding components or features
- Follow patterns used in `DEVELOPER_GUIDE.md`
- Document:
  - New theme properties
  - New core components

---

## Performance & Best Practices

- Minimize JavaScript in **performance-critical paths**
- Store state in **models**, not `delegates` for `ListView`/`GridView`
- Use **Qt resource system** for cross-platform asset bundling
- Prefer **vector graphics** and **font icons** for scalability
- Enable **high DPI scaling** support
