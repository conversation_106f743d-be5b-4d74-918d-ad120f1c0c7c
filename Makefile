#############################################################################
# Makefile for building: light_qt5.app/Contents/MacOS/light_qt5
# Generated by qmake (3.1) (Qt 5.15.16)
# Project:  light_qt5.pro
# Template: app
# Command: /usr/local/opt/qt@5/bin/qmake -o Makefile light_qt5.pro
#############################################################################

MAKEFILE      = Makefile

EQ            = =

####### Compiler, tools and options

CC            = /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang
CXX           = /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++
DEFINES       = -DQT_VERSION_DIFFERENCE -DCOMPATIBILITY_5_15_16 -DQT_NO_DEBUG -DQT_QUICKCONTROLS2_LIB -DQT_QUICK_LIB -DQT_GUI_LIB -DQT_QMLMODELS_LIB -DQT_QML_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB
CFLAGS        = -pipe -O2 $(EXPORT_ARCH_ARGS) -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.13 -Wall -Wextra -fPIC $(DEFINES)
CXXFLAGS      = -pipe -stdlib=libc++ -O2 -std=gnu++1z $(EXPORT_ARCH_ARGS) -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.13 -Wall -Wextra -fPIC $(DEFINES)
INCPATH       = -I. -Isrc -Isrc -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I. -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AGL.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -F/usr/local/Cellar/qt@5/5.15.16_2/lib
QMAKE         = /usr/local/opt/qt@5/bin/qmake
DEL_FILE      = rm -f
CHK_DIR_EXISTS= test -d
MKDIR         = mkdir -p
COPY          = cp -f
COPY_FILE     = cp -f
COPY_DIR      = cp -f -R
INSTALL_FILE  = install -m 644 -p
INSTALL_PROGRAM = install -m 755 -p
INSTALL_DIR   = cp -f -R
QINSTALL      = /usr/local/opt/qt@5/bin/qmake -install qinstall
QINSTALL_PROGRAM = /usr/local/opt/qt@5/bin/qmake -install qinstall -exe
DEL_FILE      = rm -f
SYMLINK       = ln -f -s
DEL_DIR       = rmdir
MOVE          = mv -f
TAR           = tar -cf
COMPRESS      = gzip -9f
DISTNAME      = light_qt51.0.0
DISTDIR = /Users/<USER>/prospr/ltempp/light/.tmp/light_qt51.0.0
LINK          = /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++
LFLAGS        = -stdlib=libc++ -headerpad_max_install_names $(EXPORT_ARCH_ARGS) -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.13 -Wl,-rpath,@executable_path/../Frameworks
LIBS          = $(SUBLIBS) -F/usr/local/Cellar/qt@5/5.15.16_2/lib -L/usr/local/opt/yaml-cpp/lib -lyaml-cpp -framework QtQuickControls2 -framework QtQuick -framework QtGui -framework AppKit -framework Metal -framework QtQmlModels -framework QtQml -framework QtNetwork -framework QtCore -framework DiskArbitration -framework IOKit -framework OpenGL -framework AGL   
AR            = /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar cq
RANLIB        = /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib -s
SED           = sed
STRIP         = strip

####### Output directory

OBJECTS_DIR   = ./

####### Files

SOURCES       = main.cpp \
		src/models/PermissionsModel.cpp \
		src/models/PrintfileItemModel.cpp \
		src/models/PrintfileModel.cpp \
		src/models/ServiceFunctionModel.cpp \
		src/models/ServiceStatusModel.cpp \
		src/printer/PrinterManager.cpp \
		src/printer/ServiceFunction.cpp \
		src/printer/ServiceStatus.cpp \
		src/Permission.cpp \
		src/TimeHandler.cpp \
		src/ConfigManager.cpp \
		src/YamlConfigLoader.cpp \
		src/BackendSingletons.cpp \
		src/QmlRegistration.cpp \
		src/UserInfo.cpp \
		src/UserManager.cpp \
		src/printfile/Printfile.cpp \
		src/printfile/PrintfileItem.cpp \
		src/printfile/PrintfileManager.cpp \
		src/models/ErrorModel.cpp \
		src/ErrorManager.cpp \
		tests/printer/PrinterManagerTest.cpp qrc_qml.cpp \
		moc_PermissionsModel.cpp \
		moc_PrintfileItemModel.cpp \
		moc_PrintfileModel.cpp \
		moc_ServiceFunctionModel.cpp \
		moc_ServiceStatusModel.cpp \
		moc_PrinterManager.cpp \
		moc_ServiceFunction.cpp \
		moc_ServiceStatus.cpp \
		moc_Permission.cpp \
		moc_TimeHandler.cpp \
		moc_ConfigManager.cpp \
		moc_YamlConfigLoader.cpp \
		moc_UserInfo.cpp \
		moc_UserManager.cpp \
		moc_Printfile.cpp \
		moc_PrintfileItem.cpp \
		moc_PrintfileManager.cpp \
		moc_ErrorModel.cpp \
		moc_ErrorManager.cpp \
		moc_PrinterManagerTest.cpp
OBJECTS       = main.o \
		PermissionsModel.o \
		PrintfileItemModel.o \
		PrintfileModel.o \
		ServiceFunctionModel.o \
		ServiceStatusModel.o \
		PrinterManager.o \
		ServiceFunction.o \
		ServiceStatus.o \
		Permission.o \
		TimeHandler.o \
		ConfigManager.o \
		YamlConfigLoader.o \
		BackendSingletons.o \
		QmlRegistration.o \
		UserInfo.o \
		UserManager.o \
		Printfile.o \
		PrintfileItem.o \
		PrintfileManager.o \
		ErrorModel.o \
		ErrorManager.o \
		PrinterManagerTest.o \
		qrc_qml.o \
		moc_PermissionsModel.o \
		moc_PrintfileItemModel.o \
		moc_PrintfileModel.o \
		moc_ServiceFunctionModel.o \
		moc_ServiceStatusModel.o \
		moc_PrinterManager.o \
		moc_ServiceFunction.o \
		moc_ServiceStatus.o \
		moc_Permission.o \
		moc_TimeHandler.o \
		moc_ConfigManager.o \
		moc_YamlConfigLoader.o \
		moc_UserInfo.o \
		moc_UserManager.o \
		moc_Printfile.o \
		moc_PrintfileItem.o \
		moc_PrintfileManager.o \
		moc_ErrorModel.o \
		moc_ErrorManager.o \
		moc_PrinterManagerTest.o
DIST          = /usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/spec_pre.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/qdevice.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/device_config.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/unix.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/mac.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/macx.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/sanitize.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/gcc-base.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/gcc-base-mac.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/clang.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/clang-mac.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/qconfig.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3danimation.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3danimation_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dcore.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dcore_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dextras.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dextras_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dinput.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dinput_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dlogic.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dlogic_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquick.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquick_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickanimation.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickextras.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickinput.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickrender.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3drender.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3drender_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bluetooth.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bluetooth_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bodymovin_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_charts.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_charts_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_clipboard_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_concurrent.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_concurrent_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_core.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_core_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_datavisualization.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_datavisualization_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_dbus.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_dbus_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_designer.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_designer_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_designercomponents_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_edid_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_fb_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gamepad.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gamepad_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_graphics_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gui.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gui_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_help.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_help_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_location.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_location_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_macextras.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_macextras_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimedia.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimedia_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimediawidgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_network.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_network_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_networkauth.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_networkauth_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_nfc.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_nfc_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_opengl.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_opengl_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_openglextensions.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_openglextensions_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdf.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdf_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdfwidgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdfwidgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioning.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioning_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioningquick.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioningquick_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_printsupport.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_printsupport_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_purchasing.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_purchasing_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qml.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qml_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmldebug_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlmodels.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlmodels_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmltest.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmltest_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlworkerscript.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlworkerscript_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3d.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3d_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dassetimport.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dassetimport_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3drender.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3drender_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3druntimerender.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3druntimerender_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dutils.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dutils_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickcontrols2.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickparticles_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickshapes_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quicktemplates2.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickwidgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_remoteobjects.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_repparser.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_repparser_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_script.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_script_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scripttools.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scripttools_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scxml.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scxml_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sensors.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sensors_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialbus.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialbus_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialport.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialport_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_service_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sql.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sql_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_svg.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_svg_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_testlib.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_testlib_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_texttospeech.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_texttospeech_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_theme_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_uiplugin.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_uitools.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_uitools_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_virtualkeyboard.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_virtualkeyboard_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webchannel.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webchannel_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webengine.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webengine_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginecore.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginecore_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginecoreheaders_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginewidgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginewidgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_websockets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_websockets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webview.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webview_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_widgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_widgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xml.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xml_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xmlpatterns.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qt_functions.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qt_config.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang/qmake.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/spec_post.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/exclusive_builds.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/sdk.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/toolchain.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/toolchain.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/default_pre.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/default_pre.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/resolve_config.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/default_post.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/default_post.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/objective_c.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/mac.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/warn_on.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qt.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/resources_functions.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/resources.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/moc.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/unix/opengl.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/unix/thread.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qmake_use.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/file_copies.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/rez.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/asset_catalogs.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/testcase_targets.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/exceptions.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/yacc.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/lex.prf \
		light_qt5.pro src/Constants.h \
		src/models/PermissionsModel.h \
		src/models/PrintfileItemModel.h \
		src/models/PrintfileModel.h \
		src/models/ServiceFunctionModel.h \
		src/models/ServiceStatusModel.h \
		src/printer/PrinterManager.h \
		src/printer/ServiceFunction.h \
		src/printer/ServiceStatus.h \
		src/Permission.h \
		src/TimeHandler.h \
		src/ConfigManager.h \
		src/YamlConfigLoader.h \
		src/BackendSingletons.h \
		src/QmlRegistration.h \
		src/UserInfo.h \
		src/UserManager.h \
		src/printfile/Printfile.h \
		src/printfile/PrintfileItem.h \
		src/printfile/PrintfileManager.h \
		src/models/ErrorModel.h \
		src/ErrorManager.h \
		tests/printer/PrinterManagerTest.h main.cpp \
		src/models/PermissionsModel.cpp \
		src/models/PrintfileItemModel.cpp \
		src/models/PrintfileModel.cpp \
		src/models/ServiceFunctionModel.cpp \
		src/models/ServiceStatusModel.cpp \
		src/printer/PrinterManager.cpp \
		src/printer/ServiceFunction.cpp \
		src/printer/ServiceStatus.cpp \
		src/Permission.cpp \
		src/TimeHandler.cpp \
		src/ConfigManager.cpp \
		src/YamlConfigLoader.cpp \
		src/BackendSingletons.cpp \
		src/QmlRegistration.cpp \
		src/UserInfo.cpp \
		src/UserManager.cpp \
		src/printfile/Printfile.cpp \
		src/printfile/PrintfileItem.cpp \
		src/printfile/PrintfileManager.cpp \
		src/models/ErrorModel.cpp \
		src/ErrorManager.cpp \
		tests/printer/PrinterManagerTest.cpp
QMAKE_TARGET  = light_qt5
DESTDIR       = 
TARGET        = light_qt5.app/Contents/MacOS/light_qt5

####### Custom Variables
EXPORT_QMAKE_MAC_SDK = macosx
EXPORT_QMAKE_MAC_SDK_VERSION = 15.5
EXPORT_QMAKE_XCODE_DEVELOPER_PATH = /Applications/Xcode.app/Contents/Developer
EXPORT__QMAKE_STASH_ = 
EXPORT_VALID_ARCHS = x86_64
EXPORT_DEFAULT_ARCHS = x86_64
EXPORT_ARCHS = $(filter $(EXPORT_VALID_ARCHS), $(if $(ARCHS), $(ARCHS), $(if $(EXPORT_DEFAULT_ARCHS), $(EXPORT_DEFAULT_ARCHS), $(EXPORT_VALID_ARCHS))))
EXPORT_ARCH_ARGS = $(foreach arch, $(if $(EXPORT_ARCHS), $(EXPORT_ARCHS), $(EXPORT_VALID_ARCHS)), -arch $(arch))
EXPORT__PRO_FILE_ = /Users/<USER>/prospr/ltempp/light/light_qt5.pro


include /usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/sdk.mk
first: all
####### Build rules

light_qt5.app/Contents/MacOS/light_qt5:  $(OBJECTS)  
	@test -d light_qt5.app/Contents/MacOS/ || mkdir -p light_qt5.app/Contents/MacOS/
	$(LINK) $(LFLAGS) -o $(TARGET) $(OBJECTS) $(OBJCOMP) $(LIBS)
	mkdir -p /Users/<USER>/prospr/ltempp/light/../Resources/config; cp /Users/<USER>/prospr/ltempp/light/config/ui.ini /Users/<USER>/prospr/ltempp/light/../Resources/config/ui.ini;

Makefile: light_qt5.pro /usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang/qmake.conf /usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/spec_pre.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/qdevice.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/device_config.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/unix.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/mac.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/macx.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/sanitize.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/gcc-base.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/gcc-base-mac.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/clang.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/clang-mac.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/qconfig.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3danimation.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3danimation_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dcore.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dcore_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dextras.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dextras_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dinput.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dinput_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dlogic.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dlogic_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquick.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquick_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickanimation.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickextras.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickinput.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickrender.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3drender.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3drender_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bluetooth.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bluetooth_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bodymovin_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_charts.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_charts_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_clipboard_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_concurrent.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_concurrent_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_core.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_core_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_datavisualization.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_datavisualization_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_dbus.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_dbus_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_designer.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_designer_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_designercomponents_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_edid_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_fb_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gamepad.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gamepad_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_graphics_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gui.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gui_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_help.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_help_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_location.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_location_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_macextras.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_macextras_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimedia.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimedia_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimediawidgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_network.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_network_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_networkauth.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_networkauth_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_nfc.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_nfc_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_opengl.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_opengl_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_openglextensions.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_openglextensions_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdf.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdf_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdfwidgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdfwidgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioning.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioning_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioningquick.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioningquick_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_printsupport.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_printsupport_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_purchasing.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_purchasing_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qml.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qml_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmldebug_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlmodels.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlmodels_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmltest.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmltest_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlworkerscript.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlworkerscript_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3d.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3d_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dassetimport.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dassetimport_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3drender.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3drender_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3druntimerender.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3druntimerender_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dutils.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dutils_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickcontrols2.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickparticles_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickshapes_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quicktemplates2.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickwidgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_remoteobjects.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_repparser.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_repparser_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_script.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_script_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scripttools.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scripttools_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scxml.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scxml_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sensors.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sensors_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialbus.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialbus_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialport.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialport_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_service_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sql.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sql_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_svg.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_svg_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_testlib.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_testlib_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_texttospeech.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_texttospeech_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_theme_support_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_uiplugin.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_uitools.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_uitools_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_virtualkeyboard.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_virtualkeyboard_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webchannel.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webchannel_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webengine.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webengine_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginecore.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginecore_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginecoreheaders_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginewidgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginewidgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_websockets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_websockets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webview.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webview_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_widgets.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_widgets_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xml.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xml_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xmlpatterns.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qt_functions.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qt_config.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang/qmake.conf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/spec_post.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/exclusive_builds.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/sdk.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/toolchain.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/toolchain.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/default_pre.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/default_pre.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/resolve_config.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/default_post.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/default_post.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/objective_c.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/mac.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/warn_on.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qt.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/resources_functions.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/resources.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/moc.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/unix/opengl.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/unix/thread.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qmake_use.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/file_copies.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/rez.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/asset_catalogs.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/testcase_targets.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/exceptions.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/yacc.prf \
		/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/lex.prf \
		light_qt5.pro \
		qml.qrc \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Resources/QtQuickControls2.prl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Resources/QtQuick.prl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Resources/QtGui.prl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Resources/QtQmlModels.prl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Resources/QtQml.prl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Resources/QtNetwork.prl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Resources/QtCore.prl
	$(QMAKE) -o Makefile light_qt5.pro
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/spec_pre.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/qdevice.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/device_config.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/unix.conf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/mac.conf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/macx.conf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/sanitize.conf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/gcc-base.conf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/gcc-base-mac.conf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/clang.conf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/common/clang-mac.conf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/qconfig.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3danimation.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3danimation_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dcore.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dcore_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dextras.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dextras_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dinput.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dinput_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dlogic.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dlogic_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquick.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquick_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickanimation.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickanimation_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickextras.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickextras_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickinput.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickinput_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickrender.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickrender_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickscene2d.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3dquickscene2d_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3drender.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_3drender_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_accessibility_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bluetooth.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bluetooth_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bodymovin_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_bootstrap_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_charts.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_charts_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_clipboard_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_concurrent.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_concurrent_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_core.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_core_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_datavisualization.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_datavisualization_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_dbus.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_dbus_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_designer.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_designer_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_designercomponents_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_edid_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_fb_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_fontdatabase_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gamepad.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gamepad_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_graphics_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gui.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_gui_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_help.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_help_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_location.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_location_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_macextras.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_macextras_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimedia.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimedia_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimediawidgets.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_multimediawidgets_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_network.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_network_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_networkauth.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_networkauth_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_nfc.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_nfc_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_opengl.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_opengl_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_openglextensions.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_openglextensions_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_packetprotocol_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdf.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdf_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdfwidgets.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_pdfwidgets_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_platformcompositor_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioning.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioning_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioningquick.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_positioningquick_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_printsupport.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_printsupport_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_purchasing.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_purchasing_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qml.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qml_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmldebug_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmldevtools_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlmodels.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlmodels_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmltest.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmltest_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlworkerscript.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qmlworkerscript_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3d.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3d_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dassetimport.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dassetimport_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3drender.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3drender_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3druntimerender.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3druntimerender_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dutils.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick3dutils_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quick_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickcontrols2.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickcontrols2_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickparticles_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickshapes_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quicktemplates2.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quicktemplates2_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickwidgets.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_quickwidgets_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_remoteobjects.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_remoteobjects_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_repparser.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_repparser_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_script.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_script_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scripttools.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scripttools_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scxml.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_scxml_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sensors.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sensors_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialbus.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialbus_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialport.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_serialport_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_service_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sql.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_sql_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_svg.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_svg_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_testlib.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_testlib_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_texttospeech.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_texttospeech_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_theme_support_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_uiplugin.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_uitools.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_uitools_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_virtualkeyboard.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_virtualkeyboard_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webchannel.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webchannel_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webengine.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webengine_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginecore.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginecore_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginecoreheaders_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginewidgets.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webenginewidgets_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_websockets.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_websockets_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webview.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_webview_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_widgets.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_widgets_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xml.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xml_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xmlpatterns.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/modules/qt_lib_xmlpatterns_private.pri:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qt_functions.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qt_config.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang/qmake.conf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/spec_post.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/exclusive_builds.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/sdk.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/toolchain.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/toolchain.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/default_pre.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/default_pre.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/resolve_config.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/default_post.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/default_post.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/objective_c.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/mac.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/warn_on.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qt.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/resources_functions.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/resources.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/moc.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/unix/opengl.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/unix/thread.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/qmake_use.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/file_copies.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/rez.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/mac/asset_catalogs.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/testcase_targets.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/exceptions.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/yacc.prf:
/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/lex.prf:
light_qt5.pro:
qml.qrc:
/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Resources/QtQuickControls2.prl:
/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Resources/QtQuick.prl:
/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Resources/QtGui.prl:
/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Resources/QtQmlModels.prl:
/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Resources/QtQml.prl:
/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Resources/QtNetwork.prl:
/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Resources/QtCore.prl:
qmake: FORCE
	@$(QMAKE) -o Makefile light_qt5.pro

qmake_all: FORCE

light_qt5.app/Contents/PkgInfo: 
	@test -d light_qt5.app/Contents || mkdir -p light_qt5.app/Contents
	@$(DEL_FILE) light_qt5.app/Contents/PkgInfo
	@echo "APPL????" > light_qt5.app/Contents/PkgInfo
light_qt5.app/Contents/Resources/empty.lproj: 
	@test -d light_qt5.app/Contents/Resources || mkdir -p light_qt5.app/Contents/Resources
	@touch light_qt5.app/Contents/Resources/empty.lproj
	
light_qt5.app/Contents/Info.plist: 
	@test -d light_qt5.app/Contents || mkdir -p light_qt5.app/Contents
	@$(DEL_FILE) light_qt5.app/Contents/Info.plist
	@sed -e "s,@SHORT_VERSION@,1.0,g" -e "s,\$${QMAKE_SHORT_VERSION},1.0,g" -e "s,@FULL_VERSION@,1.0.0,g" -e "s,\$${QMAKE_FULL_VERSION},1.0.0,g" -e "s,@TYPEINFO@,????,g" -e "s,\$${QMAKE_PKGINFO_TYPEINFO},????,g" -e "s,@BUNDLEIDENTIFIER@,com.yourcompany.light-qt5,g" -e "s,\$${PRODUCT_BUNDLE_IDENTIFIER},com.yourcompany.light-qt5,g" -e "s,\$${MACOSX_DEPLOYMENT_TARGET},10.13,g" -e "s,\$${IPHONEOS_DEPLOYMENT_TARGET},,g" -e "s,\$${TVOS_DEPLOYMENT_TARGET},,g" -e "s,\$${WATCHOS_DEPLOYMENT_TARGET},,g" -e "s,@ICON@,,g" -e "s,\$${ASSETCATALOG_COMPILER_APPICON_NAME},,g" -e "s,@EXECUTABLE@,light_qt5,g" -e "s,@LIBRARY@,light_qt5,g" -e "s,\$${EXECUTABLE_NAME},light_qt5,g" -e "s,@TYPEINFO@,????,g" -e "s,\$${QMAKE_PKGINFO_TYPEINFO},????,g" /usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang/Info.plist.app >light_qt5.app/Contents/Info.plist
light_qt5.app/Contents/MacOS/storage/permissions.json: storage/permissions.json
	@test -d light_qt5.app/Contents/MacOS/storage || mkdir -p light_qt5.app/Contents/MacOS/storage
	@$(DEL_FILE) light_qt5.app/Contents/MacOS/storage/permissions.json
	@$(COPY_FILE) storage/permissions.json light_qt5.app/Contents/MacOS/storage/permissions.json
light_qt5.app/Contents/MacOS/storage/users.json: storage/users.json
	@test -d light_qt5.app/Contents/MacOS/storage || mkdir -p light_qt5.app/Contents/MacOS/storage
	@$(DEL_FILE) light_qt5.app/Contents/MacOS/storage/users.json
	@$(COPY_FILE) storage/users.json light_qt5.app/Contents/MacOS/storage/users.json
light_qt5.app/Contents/MacOS/storage/errorlog.json: storage/errorlog.json
	@test -d light_qt5.app/Contents/MacOS/storage || mkdir -p light_qt5.app/Contents/MacOS/storage
	@$(DEL_FILE) light_qt5.app/Contents/MacOS/storage/errorlog.json
	@$(COPY_FILE) storage/errorlog.json light_qt5.app/Contents/MacOS/storage/errorlog.json
light_qt5.app/Contents/MacOS/storage/permissions.json: storage/permissions.json
	@test -d light_qt5.app/Contents/MacOS/storage || mkdir -p light_qt5.app/Contents/MacOS/storage
	@$(DEL_FILE) light_qt5.app/Contents/MacOS/storage/permissions.json
	@$(COPY_FILE) storage/permissions.json light_qt5.app/Contents/MacOS/storage/permissions.json
light_qt5.app/Contents/MacOS/storage/users.json: storage/users.json
	@test -d light_qt5.app/Contents/MacOS/storage || mkdir -p light_qt5.app/Contents/MacOS/storage
	@$(DEL_FILE) light_qt5.app/Contents/MacOS/storage/users.json
	@$(COPY_FILE) storage/users.json light_qt5.app/Contents/MacOS/storage/users.json

all: Makefile \
		light_qt5.app/Contents/PkgInfo \
		light_qt5.app/Contents/Resources/empty.lproj \
		light_qt5.app/Contents/Info.plist \
		light_qt5.app/Contents/MacOS/storage/permissions.json \
		light_qt5.app/Contents/MacOS/storage/users.json \
		light_qt5.app/Contents/MacOS/storage/errorlog.json \
		light_qt5.app/Contents/MacOS/storage/permissions.json \
		light_qt5.app/Contents/MacOS/storage/users.json light_qt5.app/Contents/MacOS/light_qt5

dist: distdir FORCE
	(cd `dirname $(DISTDIR)` && $(TAR) $(DISTNAME).tar $(DISTNAME) && $(COMPRESS) $(DISTNAME).tar) && $(MOVE) `dirname $(DISTDIR)`/$(DISTNAME).tar.gz . && $(DEL_FILE) -r $(DISTDIR)

distdir: FORCE
	@test -d $(DISTDIR) || mkdir -p $(DISTDIR)
	$(COPY_FILE) --parents $(DIST) $(DISTDIR)/
	$(COPY_FILE) --parents qml.qrc $(DISTDIR)/
	$(COPY_FILE) --parents /usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/data/dummy.cpp $(DISTDIR)/
	$(COPY_FILE) --parents src/Constants.h src/models/PermissionsModel.h src/models/PrintfileItemModel.h src/models/PrintfileModel.h src/models/ServiceFunctionModel.h src/models/ServiceStatusModel.h src/printer/PrinterManager.h src/printer/ServiceFunction.h src/printer/ServiceStatus.h src/Permission.h src/TimeHandler.h src/ConfigManager.h src/YamlConfigLoader.h src/BackendSingletons.h src/QmlRegistration.h src/UserInfo.h src/UserManager.h src/printfile/Printfile.h src/printfile/PrintfileItem.h src/printfile/PrintfileManager.h src/models/ErrorModel.h src/ErrorManager.h tests/printer/PrinterManagerTest.h $(DISTDIR)/
	$(COPY_FILE) --parents main.cpp src/models/PermissionsModel.cpp src/models/PrintfileItemModel.cpp src/models/PrintfileModel.cpp src/models/ServiceFunctionModel.cpp src/models/ServiceStatusModel.cpp src/printer/PrinterManager.cpp src/printer/ServiceFunction.cpp src/printer/ServiceStatus.cpp src/Permission.cpp src/TimeHandler.cpp src/ConfigManager.cpp src/YamlConfigLoader.cpp src/BackendSingletons.cpp src/QmlRegistration.cpp src/UserInfo.cpp src/UserManager.cpp src/printfile/Printfile.cpp src/printfile/PrintfileItem.cpp src/printfile/PrintfileManager.cpp src/models/ErrorModel.cpp src/ErrorManager.cpp tests/printer/PrinterManagerTest.cpp $(DISTDIR)/
	$(COPY_FILE) --parents light_qt5_en_US.ts $(DISTDIR)/


clean: compiler_clean 
	-$(DEL_FILE) $(OBJECTS)
	-$(DEL_FILE) *~ core *.core


distclean: clean 
	-$(DEL_FILE) -r light_qt5.app
	-$(DEL_FILE) Makefile


####### Sub-libraries

xcodeproj:
	@$(QMAKE) -spec macx-xcode "$(EXPORT__PRO_FILE_)"

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_rcc_make_all: qrc_qml.cpp
compiler_rcc_clean:
	-$(DEL_FILE) qrc_qml.cpp
qrc_qml.cpp: qml.qrc \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/rcc \
		main.qml \
		+windows/main.qml \
		UI/MainLayout.qml \
		UI/Assets/prospr_logo.qml \
		UI/Assets/SettingsGearIcon.qml \
		UI/Assets/placeholder_barcode.qml \
		UI/Assets/warning_icon.qml \
		UI/Assets/WarningTriangleIcon.qml \
		UI/Assets/home_icon.qml \
		UI/Assets/print_icon.png \
		UI/Assets/user_icon.qml \
		UI/Assets/ServiceToolsIcon.qml \
		UI/Assets/settings_icon.png \
		UI/Assets/warning_triangle.png \
		UI/Assets/SvgIcon.qml \
		UI/Assets/service_icon.png \
		UI/Assets/power_icon.qml \
		UI/Assets/backgrounds/login-background.png \
		UI/Assets/backgrounds/login-background.svg \
		UI/Assets/backgrounds/home-background.png \
		UI/Assets/backgrounds/system-background.png \
		UI/Assets/backgrounds/system-background.svg \
		UI/Assets/Images/barcode.png \
		UI/Assets/Images/NumberCircleOne.png \
		UI/Assets/Images/calendar.png \
		UI/Assets/Images/edit-rectangle-black.png \
		UI/Assets/Images/half.png \
		UI/Assets/Images/add-circle.png \
		UI/Assets/Images/phase.png \
		UI/Assets/Images/bold.png \
		UI/Assets/Images/flag-es.png \
		UI/Assets/Images/hand-touch.png \
		UI/Assets/Images/batch-code.png \
		UI/Assets/Images/dropdown-arrow.png \
		UI/Assets/Images/shift.png \
		UI/Assets/Images/messages.png \
		UI/Assets/Images/qr-code-02.png \
		UI/Assets/Images/reload.png \
		UI/Assets/Images/trash.png \
		UI/Assets/Images/user-tick.png \
		UI/Assets/Images/exchange.png \
		UI/Assets/Images/metering.png \
		UI/Assets/Images/function.png \
		UI/Assets/Images/check.png \
		UI/Assets/Images/information.png \
		UI/Assets/Images/text.png \
		UI/Assets/Images/menu.png \
		UI/Assets/Images/service-mode.png \
		UI/Assets/Images/trash-black.png \
		UI/Assets/Images/calculator.png \
		UI/Assets/Images/insert.png \
		UI/Assets/Images/usb.png \
		UI/Assets/Images/flag-us.png \
		UI/Assets/Images/lab_background.jpg \
		UI/Assets/Images/status.png \
		UI/Assets/Images/arrow.png \
		UI/Assets/Images/edit-rectangle.png \
		UI/Assets/Images/language-circle.png \
		UI/Assets/logos/prospr-logo.png \
		UI/Assets/logos/prospr-logo.svg \
		UI/Assets/icons/brightness.svg \
		UI/Assets/icons/user-circle.svg \
		UI/Assets/icons/select.svg \
		UI/Assets/icons/chevron-down.svg \
		UI/Assets/icons/reload.svg \
		UI/Assets/icons/error_reset.svg \
		UI/Assets/icons/create.svg \
		UI/Assets/icons/system.svg \
		UI/Assets/icons/card-status-circles.svg \
		UI/Assets/icons/checkmark.svg \
		UI/Assets/icons/power-button.svg \
		UI/Assets/icons/user.svg \
		UI/Assets/icons/save-changes.svg \
		UI/Assets/icons/service.svg \
		UI/Assets/icons/plus.svg \
		UI/Assets/icons/forbidden.svg \
		UI/Assets/icons/edit.svg \
		UI/Assets/icons/minus.svg \
		UI/Assets/icons/error_clear.svg \
		UI/Assets/icons/add-user.svg \
		UI/Assets/icons/back.svg \
		UI/Assets/icons/left-arrow.svg \
		UI/Assets/icons/Trash.svg \
		UI/Assets/icons/home.svg \
		UI/Assets/icons/stop.svg \
		UI/Assets/icons/power-button-hue.svg \
		UI/Assets/icons/left-arrow-black.svg \
		UI/Assets/icons/checkmark-language.svg \
		UI/Assets/icons/warning-yellow.svg \
		UI/Assets/icons/home-small.svg \
		UI/Assets/icons/night-mode.svg \
		UI/Assets/icons/right-arrow-white.svg \
		UI/Assets/icons/power-button-background.svg \
		UI/Assets/icons/copy.svg \
		UI/Assets/icons/logout.svg \
		UI/Assets/icons/print.svg \
		UI/Assets/icons/warning-grey.svg \
		UI/Screens/PrintCreateUSB.qml \
		UI/Screens/ErrorLog.qml \
		UI/Screens/InsertText.qml \
		UI/Screens/PrinterSettings.qml \
		UI/Screens/CreateEditMessage.qml \
		UI/Screens/InsertData.qml \
		UI/Screens/LogIn.qml \
		UI/Screens/FileManager.qml \
		UI/Screens/PrintSelectSettings.qml \
		UI/Screens/Settings.qml \
		UI/Screens/PrintCreate.qml \
		UI/Screens/Home.qml \
		UI/Screens/PrintUsb.qml \
		UI/Screens/Service.qml \
		UI/Screens/Print.qml \
		UI/Screens/PrintSettings.qml \
		UI/Screens/printSettings/RightPanelSettings.qml \
		UI/Screens/printSettings/LeftPanelSettings.qml \
		UI/Screens/printSettings/TopPanelSettings.qml \
		UI/Screens/printSettings/PrintLayoutSettings.qml \
		UI/Screens/print/PrintLayout.qml \
		UI/Screens/printSelectSettings/TopPanelSelectSettings.qml \
		UI/Screens/printSelectSettings/PrintLayoutSelectSettings.qml \
		UI/Screens/printCreateUSB/PrintLayoutUSB.qml \
		UI/Screens/printCreateUSB/LeftPanelUSB.qml \
		UI/Screens/printCreateUSB/TopPanelUSB.qml \
		UI/Screens/insertDataCounter/LeftPanelCounter.qml \
		UI/Screens/insertDataCounter/TopPanelCounter.qml \
		UI/Screens/insertDataCounter/RightPanelCounter.qml \
		UI/Screens/insertDataCounter/CounterLayout.qml \
		UI/Screens/insertData/MeteringInsert.qml \
		UI/Screens/insertData/BarcodeInsert.qml \
		UI/Screens/insertData/TextInsert.qml \
		UI/Screens/insertData/BatchCodeInsert.qml \
		UI/Screens/insertData/ShiftInsert.qml \
		UI/Screens/insertData/textInsert/PrintLayoutInsert.qml \
		UI/Screens/insertData/textInsert/RightPanelInsert.qml \
		UI/Screens/insertData/textInsert/TopPanelInsert.qml \
		UI/Screens/insertData/textInsert/LeftPanelInsert.qml \
		UI/Screens/insertData/baseInsert/LeftPanelMetering.qml \
		UI/Screens/insertData/baseInsert/PrintLayoutBase.qml \
		UI/Screens/insertData/baseInsert/LeftPanelBatch.qml \
		UI/Screens/insertData/baseInsert/LeftPanelShift.qml \
		UI/Screens/insertData/baseInsert/LeftPanelBarcode.qml \
		UI/Screens/insertData/baseInsert/TopPanelBarcode.qml \
		UI/Screens/printCreate/LeftPanelCreate.qml \
		UI/Screens/printCreate/PrintLayoutCreate.qml \
		UI/Screens/printCreate/RightPanelCreate.qml \
		UI/Screens/settings/QRData.qml \
		UI/Screens/settings/Upgrade.qml \
		UI/Screens/settings/SystemInfo.qml \
		UI/Screens/settings/AddUser.qml \
		UI/Screens/settings/Users.qml \
		UI/Screens/settings/Other.qml \
		UI/Screens/settings/DateTime.qml \
		UI/Screens/settings/Language.qml \
		UI/Screens/settings/UserPermissions.qml \
		UI/Screens/insertDataDateTime/TopPanelDateTime.qml \
		UI/Screens/insertDataDateTime/BottomPanelOverride.qml \
		UI/Screens/insertDataDateTime/DateTimeLayout.qml \
		UI/Screens/insertDataDateTime/BottomPanelJulian.qml \
		UI/Screens/insertDataDateTime/MidPanelDateTime.qml \
		UI/Screens/login/LoginCard.qml \
		UI/Screens/service/Phase.qml \
		UI/Screens/service/ResetMaintenance.qml \
		UI/Screens/service/Status.qml \
		UI/Screens/service/Function.qml \
		UI/Screens/service/Service.qml \
		UI/Components/PrintControlsComponent.qml \
		UI/Components/DefaultButton.qml \
		UI/Components/SwitchIndicator.qml \
		UI/Components/ScrollableContainer.qml \
		UI/Components/CheckBoxControl.qml \
		UI/Components/LightInput.qml \
		UI/Components/ProsprButton.qml \
		UI/Components/ShutdownDialog.qml \
		UI/Components/TestPrintMode.qml \
		UI/Components/TimeHandler.qml \
		UI/Components/ToastPopup.qml \
		UI/Components/LeftPanelButton.qml \
		UI/Components/PrinterStatusComponent.qml \
		UI/Components/ProgressDialog.qml \
		UI/Components/SwitchControl.qml \
		UI/Components/SquareButton.qml \
		UI/Components/QPopup.qml \
		UI/Components/qmldir \
		UI/Components/DropShadow.qml \
		UI/Components/CardSwitch.qml \
		UI/Components/TitledFrame.qml \
		UI/Components/DropShadowEffect.qml \
		UI/Components/BlurOverlay.qml \
		UI/Components/BottomBar.qml \
		UI/Components/CardSpinBox.qml \
		UI/Components/PrintPreviewPanel.qml \
		UI/Components/ComboBoxControl.qml \
		UI/Components/ProsprTextField.qml \
		UI/Components/CustomSwitchButton.qml \
		UI/Components/CardButton.qml \
		UI/Components/CardStatus.qml \
		UI/Components/bottombar/BottomBarConfigs.js \
		UI/Components/errorlog/ErrorLogControls.qml \
		UI/Components/errorlog/ErrorLogPanel.qml \
		UI/Components/errorlog/qmldir \
		UI/Theme/Colors.qml \
		UI/Theme/Theme.qml \
		UI/Theme/Typography.qml \
		UI/Theme/Radius.qml \
		UI/Theme/qmldir \
		UI/Theme/Spacing.qml \
		UI/Theme/Shadows.qml \
		UI/Theme/Fonts.qml \
		UI/Core/DisplayConfig.qml \
		UI/Core/AppController.qml \
		UI/Core/ContentLoader.qml \
		UI/Core/PathResolver.qml \
		UI/Core/constants.js \
		UI/Core/PlatformHelpers.qml \
		UI/Core/FallbackScreen.qml \
		UI/Core/qmldir \
		UI/Core/NavigationManager.qml
	/usr/local/Cellar/qt@5/5.15.16_2/bin/rcc -name qml qml.qrc -o qrc_qml.cpp

compiler_moc_predefs_make_all: moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) moc_predefs.h
moc_predefs.h: /usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/data/dummy.cpp
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++ -pipe -stdlib=libc++ -O2 -std=gnu++1z $(EXPORT_ARCH_ARGS) -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.13 -Wall -Wextra -dM -E -o moc_predefs.h /usr/local/Cellar/qt@5/5.15.16_2/mkspecs/features/data/dummy.cpp

compiler_moc_header_make_all: moc_PermissionsModel.cpp moc_PrintfileItemModel.cpp moc_PrintfileModel.cpp moc_ServiceFunctionModel.cpp moc_ServiceStatusModel.cpp moc_PrinterManager.cpp moc_ServiceFunction.cpp moc_ServiceStatus.cpp moc_Permission.cpp moc_TimeHandler.cpp moc_ConfigManager.cpp moc_YamlConfigLoader.cpp moc_UserInfo.cpp moc_UserManager.cpp moc_Printfile.cpp moc_PrintfileItem.cpp moc_PrintfileManager.cpp moc_ErrorModel.cpp moc_ErrorManager.cpp moc_PrinterManagerTest.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) moc_PermissionsModel.cpp moc_PrintfileItemModel.cpp moc_PrintfileModel.cpp moc_ServiceFunctionModel.cpp moc_ServiceStatusModel.cpp moc_PrinterManager.cpp moc_ServiceFunction.cpp moc_ServiceStatus.cpp moc_Permission.cpp moc_TimeHandler.cpp moc_ConfigManager.cpp moc_YamlConfigLoader.cpp moc_UserInfo.cpp moc_UserManager.cpp moc_Printfile.cpp moc_PrintfileItem.cpp moc_PrintfileManager.cpp moc_ErrorModel.cpp moc_ErrorManager.cpp moc_PrinterManagerTest.cpp
moc_PermissionsModel.cpp: src/models/PermissionsModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSortFilterProxyModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsortfilterproxymodel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QList \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qlist.h \
		src/Permission.h \
		src/UserManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qmap.h \
		src/UserInfo.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSet \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qset.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/models/PermissionsModel.h -o moc_PermissionsModel.cpp

moc_PrintfileItemModel.cpp: src/models/PrintfileItemModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		src/printfile/PrintfileItem.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariantMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QUrl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qurl.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/models/PrintfileItemModel.h -o moc_PrintfileItemModel.cpp

moc_PrintfileModel.cpp: src/models/PrintfileModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		src/printfile/Printfile.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariantMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QList \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qlist.h \
		src/printfile/PrintfileItem.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QUrl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qurl.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/models/PrintfileModel.h -o moc_PrintfileModel.cpp

moc_ServiceFunctionModel.cpp: src/models/ServiceFunctionModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		src/printer/ServiceFunction.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSet \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qset.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/models/ServiceFunctionModel.h -o moc_ServiceFunctionModel.cpp

moc_ServiceStatusModel.cpp: src/models/ServiceStatusModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		src/printer/ServiceStatus.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/models/ServiceStatusModel.h -o moc_ServiceStatusModel.cpp

moc_PrinterManager.cpp: src/printer/PrinterManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qmap.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QList \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qlist.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		src/ConfigManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSettings \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsettings.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		src/models/ServiceFunctionModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		src/printer/ServiceFunction.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSet \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qset.h \
		src/models/ServiceStatusModel.h \
		src/printer/ServiceStatus.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/printer/PrinterManager.h -o moc_PrinterManager.cpp

moc_ServiceFunction.cpp: src/printer/ServiceFunction.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSet \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qset.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/printer/ServiceFunction.h -o moc_ServiceFunction.cpp

moc_ServiceStatus.cpp: src/printer/ServiceStatus.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/printer/ServiceStatus.h -o moc_ServiceStatus.cpp

moc_Permission.cpp: src/Permission.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/Permission.h -o moc_Permission.cpp

moc_TimeHandler.cpp: src/TimeHandler.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDateTime \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdatetime.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QtDebug \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdebug.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/TimeHandler.h -o moc_TimeHandler.cpp

moc_ConfigManager.cpp: src/ConfigManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSettings \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsettings.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/ConfigManager.h -o moc_ConfigManager.cpp

moc_YamlConfigLoader.cpp: src/YamlConfigLoader.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/YamlConfigLoader.h -o moc_YamlConfigLoader.cpp

moc_UserInfo.cpp: src/UserInfo.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSet \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qset.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/UserInfo.h -o moc_UserInfo.cpp

moc_UserManager.cpp: src/UserManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qmap.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		src/Permission.h \
		src/UserInfo.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSet \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qset.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/UserManager.h -o moc_UserManager.cpp

moc_Printfile.cpp: src/printfile/Printfile.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariantMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QList \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qlist.h \
		src/printfile/PrintfileItem.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QUrl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qurl.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/printfile/Printfile.h -o moc_Printfile.cpp

moc_PrintfileItem.cpp: src/printfile/PrintfileItem.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariantMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QUrl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qurl.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/printfile/PrintfileItem.h -o moc_PrintfileItem.cpp

moc_PrintfileManager.cpp: src/printfile/PrintfileManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qmap.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QList \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qlist.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariantMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSharedPointer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsharedpointer.h \
		src/models/PrintfileModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		src/printfile/Printfile.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		src/printfile/PrintfileItem.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QUrl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qurl.h \
		src/models/PrintfileItemModel.h \
		src/ConfigManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSettings \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsettings.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/printfile/PrintfileManager.h -o moc_PrintfileManager.cpp

moc_ErrorModel.cpp: src/models/ErrorModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		src/ErrorManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVector \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvector.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDateTime \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdatetime.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/models/ErrorModel.h -o moc_ErrorModel.cpp

moc_ErrorManager.cpp: src/ErrorManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVector \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvector.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDateTime \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdatetime.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib src/ErrorManager.h -o moc_ErrorManager.cpp

moc_PrinterManagerTest.cpp: tests/printer/PrinterManagerTest.h \
		src/printer/PrinterManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qmap.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QList \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qlist.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		src/ConfigManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSettings \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsettings.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		src/models/ServiceFunctionModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		src/printer/ServiceFunction.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSet \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qset.h \
		src/models/ServiceStatusModel.h \
		src/printer/ServiceStatus.h \
		moc_predefs.h \
		/usr/local/Cellar/qt@5/5.15.16_2/bin/moc
	/usr/local/Cellar/qt@5/5.15.16_2/bin/moc $(DEFINES) --include /Users/<USER>/prospr/ltempp/light/moc_predefs.h -I/usr/local/Cellar/qt@5/5.15.16_2/mkspecs/macx-clang -I/Users/<USER>/prospr/ltempp/light -I/Users/<USER>/prospr/ltempp/light/src -I/Users/<USER>/prospr/ltempp/light/src -I/usr/local/opt/yaml-cpp/include -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuickControls2.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQuick.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQmlModels.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtNetwork.framework/Headers -I/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/usr/local/Cellar/qt@5/5.15.16_2/lib tests/printer/PrinterManagerTest.h -o moc_PrinterManagerTest.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_rez_source_make_all:
compiler_rez_source_clean:
compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean 

####### Compile

main.o: main.cpp /usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers/QGuiApplication \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers/qguiapplication.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers/QQmlApplicationEngine \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers/qqmlapplicationengine.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers/QFont \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers/qfont.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers/QFontDatabase \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtGui.framework/Headers/qfontdatabase.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDir \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdir.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QLocale \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qlocale.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTranslator \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtranslator.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDebug \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdebug.h \
		src/QmlRegistration.h \
		src/BackendSingletons.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o main.o main.cpp

PermissionsModel.o: src/models/PermissionsModel.cpp src/models/PermissionsModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSortFilterProxyModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsortfilterproxymodel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QList \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qlist.h \
		src/Permission.h \
		src/UserManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qmap.h \
		src/UserInfo.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSet \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qset.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDebug \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdebug.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o PermissionsModel.o src/models/PermissionsModel.cpp

PrintfileItemModel.o: src/models/PrintfileItemModel.cpp /usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDebug \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdebug.h \
		src/models/PrintfileItemModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		src/printfile/PrintfileItem.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariantMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QUrl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qurl.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o PrintfileItemModel.o src/models/PrintfileItemModel.cpp

PrintfileModel.o: src/models/PrintfileModel.cpp src/models/PrintfileModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		src/printfile/Printfile.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariantMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QList \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qlist.h \
		src/printfile/PrintfileItem.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QUrl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qurl.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDebug \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdebug.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o PrintfileModel.o src/models/PrintfileModel.cpp

ServiceFunctionModel.o: src/models/ServiceFunctionModel.cpp src/models/ServiceFunctionModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		src/printer/ServiceFunction.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSet \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qset.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o ServiceFunctionModel.o src/models/ServiceFunctionModel.cpp

ServiceStatusModel.o: src/models/ServiceStatusModel.cpp src/models/ServiceStatusModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		src/printer/ServiceStatus.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o ServiceStatusModel.o src/models/ServiceStatusModel.cpp

PrinterManager.o: src/printer/PrinterManager.cpp /usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDebug \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdebug.h \
		src/printer/PrinterManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qmap.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QList \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qlist.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		src/ConfigManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSettings \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsettings.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		src/models/ServiceFunctionModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		src/printer/ServiceFunction.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSet \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qset.h \
		src/models/ServiceStatusModel.h \
		src/printer/ServiceStatus.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o PrinterManager.o src/printer/PrinterManager.cpp

ServiceFunction.o: src/printer/ServiceFunction.cpp src/printer/ServiceFunction.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSet \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qset.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDateTime \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdatetime.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDebug \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdebug.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o ServiceFunction.o src/printer/ServiceFunction.cpp

ServiceStatus.o: src/printer/ServiceStatus.cpp src/printer/ServiceStatus.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDebug \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdebug.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o ServiceStatus.o src/printer/ServiceStatus.cpp

Permission.o: src/Permission.cpp src/Permission.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o Permission.o src/Permission.cpp

TimeHandler.o: src/TimeHandler.cpp src/TimeHandler.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDateTime \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdatetime.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QtDebug \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdebug.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o TimeHandler.o src/TimeHandler.cpp

ConfigManager.o: src/ConfigManager.cpp src/ConfigManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSettings \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsettings.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDebug \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdebug.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QCoreApplication \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qcoreapplication.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDir \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdir.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o ConfigManager.o src/ConfigManager.cpp

YamlConfigLoader.o: src/YamlConfigLoader.cpp src/YamlConfigLoader.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/yaml.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/parser.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/dll.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/emitter.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/binary.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/emitterdef.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/emittermanip.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/null.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/ostream_wrapper.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/emitterstyle.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/stlemitter.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/exceptions.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/mark.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/noexcept.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/traits.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/node/node.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/node/detail/iterator_fwd.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/node/ptr.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/node/type.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/node/impl.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/node/detail/memory.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/node/detail/node.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/node/detail/node_ref.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/node/detail/node_data.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/node/detail/node_iterator.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/node/iterator.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/node/detail/iterator.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/node/convert.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/node/detail/impl.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/node/parse.h \
		/usr/local/opt/yaml-cpp/include/yaml-cpp/node/emit.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QFile \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qfile.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDebug \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdebug.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o YamlConfigLoader.o src/YamlConfigLoader.cpp

BackendSingletons.o: src/BackendSingletons.cpp src/BackendSingletons.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers/QQmlEngine \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers/qqmlengine.h \
		src/UserManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qmap.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		src/Permission.h \
		src/UserInfo.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSet \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qset.h \
		src/ConfigManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSettings \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsettings.h \
		src/printfile/PrintfileManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QList \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qlist.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariantMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSharedPointer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsharedpointer.h \
		src/models/PrintfileModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		src/printfile/Printfile.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		src/printfile/PrintfileItem.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QUrl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qurl.h \
		src/models/PrintfileItemModel.h \
		tests/printer/PrinterManagerTest.h \
		src/printer/PrinterManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		src/models/ServiceFunctionModel.h \
		src/printer/ServiceFunction.h \
		src/models/ServiceStatusModel.h \
		src/printer/ServiceStatus.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o BackendSingletons.o src/BackendSingletons.cpp

QmlRegistration.o: src/QmlRegistration.cpp /usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers/QQmlEngine \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers/qqmlengine.h \
		src/TimeHandler.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDateTime \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdatetime.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QtDebug \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdebug.h \
		src/models/PermissionsModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSortFilterProxyModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsortfilterproxymodel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QList \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qlist.h \
		src/Permission.h \
		src/UserManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qmap.h \
		src/UserInfo.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSet \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qset.h \
		src/models/ErrorModel.h \
		src/ErrorManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVector \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvector.h \
		src/printer/ServiceFunction.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		src/printer/ServiceStatus.h \
		src/printfile/PrintfileItem.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariantMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QUrl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qurl.h \
		src/printfile/Printfile.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o QmlRegistration.o src/QmlRegistration.cpp

UserInfo.o: src/UserInfo.cpp src/UserInfo.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSet \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qset.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o UserInfo.o src/UserInfo.cpp

UserManager.o: src/UserManager.cpp /usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QFile \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qfile.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QJsonDocument \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qjsondocument.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QJsonObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qjsonobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QJsonArray \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qjsonarray.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDebug \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdebug.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDir \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdir.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QCoreApplication \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qcoreapplication.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers/QQmlEngine \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers/qqmlengine.h \
		src/Constants.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		src/UserManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qmap.h \
		src/Permission.h \
		src/UserInfo.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSet \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qset.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o UserManager.o src/UserManager.cpp

Printfile.o: src/printfile/Printfile.cpp /usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDebug \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdebug.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers/QQmlEngine \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers/qqmlengine.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSharedPointer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsharedpointer.h \
		src/printfile/Printfile.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariantMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QList \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qlist.h \
		src/printfile/PrintfileItem.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QUrl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qurl.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o Printfile.o src/printfile/Printfile.cpp

PrintfileItem.o: src/printfile/PrintfileItem.cpp src/printfile/PrintfileItem.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariantMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QUrl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qurl.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDebug \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdebug.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o PrintfileItem.o src/printfile/PrintfileItem.cpp

PrintfileManager.o: src/printfile/PrintfileManager.cpp /usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDebug \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdebug.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QJsonDocument \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qjsondocument.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QJsonObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qjsonobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QFile \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qfile.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDir \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdir.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QCoreApplication \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qcoreapplication.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QUuid \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/quuid.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers/QQmlEngine \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtQml.framework/Headers/qqmlengine.h \
		src/Constants.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		src/printfile/PrintfileManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qmap.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QList \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qlist.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariantMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSharedPointer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsharedpointer.h \
		src/models/PrintfileModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		src/printfile/Printfile.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		src/printfile/PrintfileItem.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QUrl \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qurl.h \
		src/models/PrintfileItemModel.h \
		src/ConfigManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSettings \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsettings.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o PrintfileManager.o src/printfile/PrintfileManager.cpp

ErrorModel.o: src/models/ErrorModel.cpp src/models/ErrorModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		src/ErrorManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVector \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvector.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDateTime \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdatetime.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o ErrorModel.o src/models/ErrorModel.cpp

ErrorManager.o: src/ErrorManager.cpp src/Constants.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		src/ErrorManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVector \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvector.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDateTime \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdatetime.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QFile \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qfile.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QJsonDocument \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qjsondocument.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QJsonArray \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qjsonarray.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QJsonObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qjsonobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QCoreApplication \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qcoreapplication.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDir \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdir.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QRandomGenerator \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qrandom.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o ErrorManager.o src/ErrorManager.cpp

PrinterManagerTest.o: tests/printer/PrinterManagerTest.cpp /usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QTimer \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qtimer.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QDebug \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qdebug.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QHash \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qhash.h \
		tests/printer/PrinterManagerTest.h \
		src/printer/PrinterManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QObject \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qobject.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QMap \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qmap.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QList \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qlist.h \
		src/ConfigManager.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSettings \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qsettings.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QString \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qstring.h \
		src/models/ServiceFunctionModel.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QAbstractListModel \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qabstractitemmodel.h \
		src/printer/ServiceFunction.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QVariant \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qvariant.h \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/QSet \
		/usr/local/Cellar/qt@5/5.15.16_2/lib/QtCore.framework/Headers/qset.h \
		src/models/ServiceStatusModel.h \
		src/printer/ServiceStatus.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o PrinterManagerTest.o tests/printer/PrinterManagerTest.cpp

qrc_qml.o: qrc_qml.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qrc_qml.o qrc_qml.cpp

moc_PermissionsModel.o: moc_PermissionsModel.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_PermissionsModel.o moc_PermissionsModel.cpp

moc_PrintfileItemModel.o: moc_PrintfileItemModel.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_PrintfileItemModel.o moc_PrintfileItemModel.cpp

moc_PrintfileModel.o: moc_PrintfileModel.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_PrintfileModel.o moc_PrintfileModel.cpp

moc_ServiceFunctionModel.o: moc_ServiceFunctionModel.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_ServiceFunctionModel.o moc_ServiceFunctionModel.cpp

moc_ServiceStatusModel.o: moc_ServiceStatusModel.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_ServiceStatusModel.o moc_ServiceStatusModel.cpp

moc_PrinterManager.o: moc_PrinterManager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_PrinterManager.o moc_PrinterManager.cpp

moc_ServiceFunction.o: moc_ServiceFunction.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_ServiceFunction.o moc_ServiceFunction.cpp

moc_ServiceStatus.o: moc_ServiceStatus.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_ServiceStatus.o moc_ServiceStatus.cpp

moc_Permission.o: moc_Permission.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_Permission.o moc_Permission.cpp

moc_TimeHandler.o: moc_TimeHandler.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_TimeHandler.o moc_TimeHandler.cpp

moc_ConfigManager.o: moc_ConfigManager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_ConfigManager.o moc_ConfigManager.cpp

moc_YamlConfigLoader.o: moc_YamlConfigLoader.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_YamlConfigLoader.o moc_YamlConfigLoader.cpp

moc_UserInfo.o: moc_UserInfo.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_UserInfo.o moc_UserInfo.cpp

moc_UserManager.o: moc_UserManager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_UserManager.o moc_UserManager.cpp

moc_Printfile.o: moc_Printfile.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_Printfile.o moc_Printfile.cpp

moc_PrintfileItem.o: moc_PrintfileItem.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_PrintfileItem.o moc_PrintfileItem.cpp

moc_PrintfileManager.o: moc_PrintfileManager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_PrintfileManager.o moc_PrintfileManager.cpp

moc_ErrorModel.o: moc_ErrorModel.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_ErrorModel.o moc_ErrorModel.cpp

moc_ErrorManager.o: moc_ErrorManager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_ErrorManager.o moc_ErrorManager.cpp

moc_PrinterManagerTest.o: moc_PrinterManagerTest.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_PrinterManagerTest.o moc_PrinterManagerTest.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

