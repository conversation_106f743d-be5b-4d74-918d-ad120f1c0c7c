{"templateId": "simple_text_template", "name": "Simple Text Template", "description": "A basic template with a text item for demonstration", "backgroundColor": "#FFFFFF", "items": [{"itemId": "text_item_1", "name": "Main Text", "qmlPath": "qrc:/ui/components/TextItem.qml", "x": 50, "y": 50, "width": 300, "height": 40, "rotation": 0, "scale": 1.0, "properties": {"text": "Hello, <PERSON>!", "fontSize": 18, "color": "#000000", "fontFamily": "<PERSON><PERSON>", "fontWeight": "normal", "textAlign": "left"}, "visible": true, "zOrder": 0}, {"itemId": "text_item_2", "name": "Subtitle", "qmlPath": "qrc:/ui/components/TextItem.qml", "x": 50, "y": 110, "width": 300, "height": 30, "rotation": 0, "scale": 1.0, "properties": {"text": "This is a subtitle", "fontSize": 14, "color": "#666666", "fontFamily": "<PERSON><PERSON>", "fontWeight": "normal", "textAlign": "left"}, "visible": true, "zOrder": 1}, {"itemId": "text_item_3", "name": "Footer", "qmlPath": "qrc:/ui/components/TextItem.qml", "x": 50, "y": 250, "width": 300, "height": 25, "rotation": 0, "scale": 1.0, "properties": {"text": "Footer text here", "fontSize": 12, "color": "#999999", "fontFamily": "<PERSON><PERSON>", "fontWeight": "normal", "textAlign": "center"}, "visible": true, "zOrder": 2}]}