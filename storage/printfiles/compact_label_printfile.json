{"templateId": "compact_label_template", "name": "Compact Label Template", "description": "Compact template for small product labels", "backgroundColor": "#FFFFFF", "items": [{"itemId": "product_info", "name": "Product Info", "qmlPath": "qrc:/ui/components/TextItem.qml", "x": 10, "y": 10, "width": 280, "height": 25, "rotation": 0, "scale": 1.0, "properties": {"text": "Test File Name", "fontSize": 18, "color": "#000000", "fontFamily": "<PERSON><PERSON>", "fontWeight": "normal", "textAlign": "left"}, "visible": true, "zOrder": 0}, {"itemId": "lot_info", "name": "Lot Information", "qmlPath": "qrc:/ui/components/TextItem.qml", "x": 10, "y": 45, "width": 280, "height": 35, "rotation": 0, "scale": 1.0, "properties": {"text": "LOT #12345", "fontSize": 28, "color": "#000000", "fontFamily": "<PERSON><PERSON>", "fontWeight": "bold", "textAlign": "left"}, "visible": true, "zOrder": 1}, {"itemId": "best_by_info", "name": "Best By Information", "qmlPath": "qrc:/ui/components/TextItem.qml", "x": 10, "y": 90, "width": 280, "height": 35, "rotation": 0, "scale": 1.0, "properties": {"text": "BEST BY 02/17/26", "fontSize": 28, "color": "#000000", "fontFamily": "<PERSON><PERSON>", "fontWeight": "bold", "textAlign": "left"}, "visible": true, "zOrder": 2}]}