{"templateId": "test_template", "name": "Test Template", "description": "A minimal template for testing the TemplateManager", "items": [{"itemId": "test_text", "name": "Test Text", "qmlPath": "qrc:/ui/components/TextItem.qml", "x": 10, "y": 10, "width": 280, "height": 30, "rotation": 0, "scale": 1.0, "properties": {"text": "TemplateManager is working!", "fontSize": 16, "color": "#2E8B57", "fontFamily": "<PERSON><PERSON>", "fontWeight": "bold", "textAlign": "center"}, "visible": true, "zOrder": 0}, {"itemId": "test_rect", "name": "Test Rectangle", "qmlPath": "qrc:/ui/components/RectangleItem.qml", "x": 50, "y": 60, "width": 200, "height": 100, "rotation": 15, "scale": 1.0, "properties": {"color": "#FFE4B5", "borderColor": "#DDA0DD", "borderWidth": 2, "radius": 10}, "visible": true, "zOrder": 1}]}