#!/bin/bash

SRC_DIR="$(dirname "$0")/../config"
SRC_FILE="$SRC_DIR/ui.ini"
DEST_DIR="$HOME/.config/Prospr"
DEST_FILE="$DEST_DIR/Light.ini"

if [[ "$(uname)" == "Darwin" ]]; then
    if [ -f "$SRC_FILE" ]; then
        mkdir -p "$DEST_DIR"
        cp -v "$SRC_FILE" "$DEST_FILE"
        echo "[set_config.sh] Copied $SRC_FILE to $DEST_FILE"
    else
        echo "[set_config.sh] Source file not found: $SRC_FILE"
        exit 1
    fi
else
    echo "[set_config.sh] Not running on Darwin, skipping config copy."
fi
