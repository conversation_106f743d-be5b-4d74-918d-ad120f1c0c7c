#!/bin/bash

# Prospr Light: Developer Environment Setup
# -----------------------------------------
# This script sets up the development environment for the Prospr Light application.
# Supports Ubuntu 22.04.x LTS and macOS with Homebrew.
# Installs Qt5, QML dependencies, build tools, and configures the cutekeyboard submodule.
#
# Usage: ./scripts/setup_dev_env.sh
#
# The script will:
# 1. Detect the platform (Linux/macOS)
# 2. Install required Qt5 and development dependencies
# 3. Configure environment paths
# 4. Build and install the cutekeyboard virtual keyboard plugin

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Prospr Light Development Environment Setup${NC}"
echo -e "${BLUE}==========================================${NC}"

# Get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

if [[ "$(uname -s)" == "Linux" ]]; then
    echo -e "${YELLOW}Detected Linux platform${NC}"

    # Install basic tools for non-interactive environments (Docker)
    if [[ -n "$DEBIAN_FRONTEND" ]] || [[ ! -t 0 ]]; then
        echo -e "${BLUE}Setting up non-interactive environment...${NC}"
        export DEBIAN_FRONTEND=noninteractive
        apt-get update
        apt-get install -y lsb-release sudo tzdata git curl
        ln -fs /usr/share/zoneinfo/Etc/UTC /etc/localtime
        dpkg-reconfigure --frontend noninteractive tzdata
    fi

    # Check platform version
    if command -v lsb_release >/dev/null 2>&1; then
        if ! lsb_release -d | grep -q "Ubuntu 22.04"; then
            echo -e "${YELLOW}Warning: This script is optimized for Ubuntu 22.04.x LTS${NC}"
            echo -e "${YELLOW}Your system: $(lsb_release -d | cut -f2)${NC}"
            echo -e "${YELLOW}Continuing anyway, but you may need to adjust package versions...${NC}"
        else
            echo -e "${GREEN}✅ Ubuntu 22.04.x LTS detected${NC}"
        fi
    fi

    echo -e "${BLUE}Installing Qt5 and development dependencies...${NC}"

    # Core Qt5 packages - using more flexible versioning
    REQUIRED_PACKAGES=(
        "qtdeclarative5-dev"
        "qtquickcontrols2-5-dev"
        "qttools5-dev-tools"
        "qtdeclarative5-dev-tools"
        "qml-module-qtquick2"
        "qml-module-qtquick-controls2"
        "qml-module-qtquick-window2"
        "qml-module-qtquick-layouts"
        "qml-module-qtgraphicaleffects"
        "qt5-qmltooling-plugins"
        "build-essential"
        "qtvirtualkeyboard-plugin"
        "qml-module-qt-labs-folderlistmodel"
        "qtbase5-private-dev"           # Required for cutekeyboard
        "qtdeclarative5-private-dev"    # Required for cutekeyboard
        "libyaml-cpp-dev"              # Required for YAML configuration
        "pkg-config"                   # Build system dependency
        "cmake"                        # Alternative build system
    )

    # Install packages with error handling
    for package in "${REQUIRED_PACKAGES[@]}"; do
        echo -e "${BLUE}Installing $package...${NC}"
        if ! sudo apt-get install -y "$package"; then
            echo -e "${YELLOW}Warning: Failed to install $package, continuing...${NC}"
        fi
    done

    # Set library and pkg-config paths for Linux Qt modules
    echo -e "${BLUE}Configuring environment paths...${NC}"
    export LD_LIBRARY_PATH="/usr/lib/x86_64-linux-gnu:$LD_LIBRARY_PATH"
    export PKG_CONFIG_PATH="/usr/lib/x86_64-linux-gnu/pkgconfig:$PKG_CONFIG_PATH"

    echo -e "${GREEN}✅ Linux dependencies installed${NC}"
fi

if [[ "$(uname -s)" == "Darwin" ]]; then
    echo -e "${YELLOW}Detected macOS platform${NC}"

    # Check for Homebrew
    if ! command -v brew >/dev/null 2>&1; then
        echo -e "${BLUE}Homebrew not found. Installing Homebrew...${NC}"
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

        # Set up Homebrew environment
        if [[ -f "/opt/homebrew/bin/brew" ]]; then
            eval "$(/opt/homebrew/bin/brew shellenv)"
        elif [[ -f "/usr/local/bin/brew" ]]; then
            eval "$(/usr/local/bin/brew shellenv)"
        fi
    else
        echo -e "${GREEN}✅ Homebrew found${NC}"
    fi

    echo -e "${BLUE}Installing Qt5 and development dependencies...${NC}"

    # Update Homebrew and install dependencies
    brew update

    # Install required packages
    MACOS_PACKAGES=(
        "qt@5"
        "cmake"
        "git"
        "yaml-cpp"
        "pkg-config"
    )

    for package in "${MACOS_PACKAGES[@]}"; do
        echo -e "${BLUE}Installing $package...${NC}"
        if ! brew install "$package"; then
            echo -e "${YELLOW}Warning: Failed to install $package, continuing...${NC}"
        fi
    done

    echo -e "${GREEN}✅ macOS dependencies installed${NC}"

    # Set up Qt5 environment paths
    echo -e "${BLUE}Configuring Qt5 environment...${NC}"

    # Detect Qt5 installation path
    if [[ -d "/opt/homebrew/opt/qt@5" ]]; then
        QT5_PATH="/opt/homebrew/opt/qt@5"
    elif [[ -d "/usr/local/opt/qt@5" ]]; then
        QT5_PATH="/usr/local/opt/qt@5"
    else
        echo -e "${RED}❌ Qt5 installation not found${NC}"
        exit 1
    fi

    echo -e "${GREEN}✅ Qt5 found at: $QT5_PATH${NC}"

    # Display environment setup instructions
    echo -e "${YELLOW}📝 To use Qt5 tools, add the following to your shell profile:${NC}"
    echo -e "${BLUE}export PATH=\"$QT5_PATH/bin:\$PATH\"${NC}"
    echo -e "${BLUE}export LDFLAGS=\"-L$QT5_PATH/lib\"${NC}"
    echo -e "${BLUE}export CPPFLAGS=\"-I$QT5_PATH/include\"${NC}"
    echo -e "${BLUE}export PKG_CONFIG_PATH=\"$QT5_PATH/lib/pkgconfig\"${NC}"

    # Temporarily set paths for this session
    export PATH="$QT5_PATH/bin:$PATH"
    export LDFLAGS="-L$QT5_PATH/lib"
    export CPPFLAGS="-I$QT5_PATH/include"
    export PKG_CONFIG_PATH="$QT5_PATH/lib/pkgconfig"
fi

# Build cutekeyboard submodule
echo -e "${BLUE}Building cutekeyboard virtual keyboard plugin...${NC}"
CUTEKBD_DIR="$PROJECT_DIR/3rdparty/cutekeyboard"

if [[ ! -d "$CUTEKBD_DIR" ]]; then
    echo -e "${RED}❌ cutekeyboard submodule not found at $CUTEKBD_DIR${NC}"
    echo -e "${YELLOW}Run: git submodule update --init --recursive${NC}"
    exit 1
fi

# Build cutekeyboard
cd "$CUTEKBD_DIR/src"

# Find qmake
if [[ "$(uname -s)" == "Darwin" ]]; then
    if command -v qmake >/dev/null 2>&1; then
        QMAKE_CMD="qmake"
    elif [[ -x "$QT5_PATH/bin/qmake" ]]; then
        QMAKE_CMD="$QT5_PATH/bin/qmake"
    else
        echo -e "${RED}❌ qmake not found${NC}"
        exit 1
    fi
    MAKE_JOBS=$(sysctl -n hw.logicalcpu)
else
    QMAKE_CMD=$(command -v qmake || command -v qmake-qt5)
    if [[ -z "$QMAKE_CMD" ]]; then
        echo -e "${RED}❌ qmake not found${NC}"
        exit 1
    fi
    MAKE_JOBS=$(nproc)
fi

echo -e "${BLUE}Using qmake: $QMAKE_CMD${NC}"
echo -e "${BLUE}Building with $MAKE_JOBS parallel jobs...${NC}"

# Clean and build
make clean 2>/dev/null || true
$QMAKE_CMD
make -j$MAKE_JOBS

if [[ $? -eq 0 ]]; then
    echo -e "${GREEN}✅ cutekeyboard built successfully${NC}"

    # Install the plugin
    if make install; then
        echo -e "${GREEN}✅ cutekeyboard installed successfully${NC}"
    else
        echo -e "${YELLOW}⚠️ cutekeyboard install failed, but build succeeded${NC}"
    fi
else
    echo -e "${RED}❌ cutekeyboard build failed${NC}"
    exit 1
fi

# Return to project directory
cd "$PROJECT_DIR"

echo -e "${GREEN}🎉 Development environment setup complete!${NC}"
echo -e "${BLUE}Next steps:${NC}"
echo -e "${BLUE}1. Build the application: ./scripts/build.sh${NC}"
echo -e "${BLUE}2. Run the application: ./scripts/run.sh${NC}"
echo -e "${BLUE}3. Run tests: ./tests/run_tests.sh${NC}"

if [[ "$(uname -s)" == "Darwin" ]]; then
    echo -e "${YELLOW}📝 Remember to add Qt5 paths to your shell profile for future sessions${NC}"
fi
