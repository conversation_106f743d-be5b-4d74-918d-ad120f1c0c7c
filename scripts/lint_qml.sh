#!/bin/bash
# Lint all QML files in the project using qmllint

# Try to find qmllint in common locations
QMLLINT_BIN="${QMLLINT_BIN:-$(command -v qmllint || true)}"
if [ -z "$QMLLINT_BIN" ]; then
    if [ -x "/usr/lib/qt5/bin/qmllint" ]; then
        QMLLINT_BIN="/usr/lib/qt5/bin/qmllint"
    else
        echo "Error: qmllint not found in PATH or /usr/lib/qt5/bin. Please install qtdeclarative5-dev-tools."
        exit 1
    fi
fi

# Parse qmllint.ini for settings
QMLLINT_INI="$(dirname "$0")/../qmllint.ini"
QMLLINT_ARGS=""
if [ -f "$QMLLINT_INI" ]; then
    silent=$(awk -F '=' '/^silent[ ]*=/ {print $2}' "$QMLLINT_INI" | tr -d '[:space:]')
    check_unqualified=$(awk -F '=' '/^check_unqualified[ ]*=/ {print $2}' "$QMLLINT_INI" | tr -d '[:space:]')
    qmldirs=$(awk -F '=' '/^qmldirs[ ]*=/ {print $2}' "$QMLLINT_INI" | tr -d '[:space:]')
    qmltypes=$(awk -F '=' '/^qmltypes[ ]*=/ {print $2}' "$QMLLINT_INI" | tr -d '[:space:]')
    if [ "$silent" = "true" ]; then
        QMLLINT_ARGS+=" --silent"
    fi
    if [ "$check_unqualified" = "true" ]; then
        QMLLINT_ARGS+=" -U"
    fi
    if [ -n "$qmldirs" ]; then
        IFS=',' read -ra dirs <<< "$qmldirs"
        for dir in "${dirs[@]}"; do
            QMLLINT_ARGS+=" -I $dir"
        done
    fi
    if [ -n "$qmltypes" ]; then
        IFS=',' read -ra types <<< "$qmltypes"
        for type in "${types[@]}"; do
            QMLLINT_ARGS+=" -i $type"
        done
    fi
fi

# List of directories to ignore (relative to project root)
IGNORE_DIRS=(build context docs fonts qt_aarch64_builder .qm)

# Build the find prune expression from IGNORE_DIRS
PRUNE_EXPR=""
for dir in "${IGNORE_DIRS[@]}"; do
    PRUNE_EXPR+=" -path ./$dir -prune -o"
done

status=0
find_cmd='find .'
find_cmd+="$PRUNE_EXPR -type f -name \"*.qml\" -print"
eval $find_cmd | while read -r file; do
    echo "Linting: $file"
    if ! "$QMLLINT_BIN" $QMLLINT_ARGS "$file" 2>lint_qml_error.log; then
        echo " Error linting $file"
        cat lint_qml_error.log
        status=1
    fi
    rm -f lint_qml_error.log
done

if [ $status -eq 0 ]; then
    echo "All QML files linted successfully."
else
    echo "Linting failed on at least one file."
    exit 1
fi
