#!/bin/bash
# Script to prepare fonts for Qt application deployment
# This script copies system fonts to the deployment directory
#
# Prospr Light Brand Fonts (April 2025):
# - Young Serif: Primary font for headings (https://www.youngserif.com or https://fonts.google.com/?query=young+serif)
# - Clash Grotesk: Secondary font for UI elements (https://www.fontshare.com/fonts/clash-grotesk)
#
# These fonts should be placed in the resources/fonts directory as:
# - resources/fonts/YoungSerif-Regular.ttf
# - resources/fonts/ClashGrotesk-Regular.ttf
# - resources/fonts/ClashGrotesk-Medium.ttf
# - resources/fonts/ClashGrotesk-Semibold.ttf
# - resources/fonts/ClashGrotesk-Bold.ttf

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
FONTS_DIR="$PROJECT_DIR/fonts"

# Colors for output
GREEN="\033[0;32m"
RED="\033[0;31m"
YELLOW="\033[0;33m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

echo -e "${BLUE}Preparing fonts for Qt application deployment${NC}"

# Create fonts directory if it doesn't exist
mkdir -p "$FONTS_DIR"

# Clear old fonts.dir file and all existing fonts
rm -f "$FONTS_DIR/fonts.dir"
rm -f "$FONTS_DIR"/*.ttf "$FONTS_DIR"/*.otf 2>/dev/null

# List of possible system font locations
SYSTEM_FONT_DIRS=(
    "/usr/share/fonts/truetype"
    "/usr/share/fonts/TTF"
    "/usr/share/fonts"
    "/usr/local/share/fonts"
    "/usr/X11R6/lib/X11/fonts/TTF"
)

# Font families to look for (in order of preference)
FONT_FAMILIES=(
    # Primary Prospr brand fonts (April 2025 update)
    "youngserif"
    "young-serif"
    "young_serif"
    "young serif"
    "clashgrotesk"
    "clash-grotesk"
    "clash_grotesk"
    "clash grotesk"
    # Fallback system fonts
    "dejavu"
    "liberation"
    "ubuntu"
    "arial"
    "times"
    "helvetica"
    "freefont"
    "droid"
    "noto"
)

# Custom project fonts directory
CUSTOM_FONTS_DIR="$PROJECT_DIR/resources/fonts"

# First check for project-specific fonts
if [ -d "$CUSTOM_FONTS_DIR" ]; then
    echo -e "${GREEN}Checking custom project fonts directory...${NC}"
    custom_fonts=$(find "$CUSTOM_FONTS_DIR" -type f \( -name "*.ttf" -o -name "*.otf" \) 2>/dev/null)
    
    if [ -n "$custom_fonts" ]; then
        for font in $custom_fonts; do
            font_name=$(basename "$font")
            echo -e "${GREEN}Copying custom font: $font_name${NC}"
            cp "$font" "$FONTS_DIR/"
        done
    else
        echo -e "${YELLOW}No custom fonts found in $CUSTOM_FONTS_DIR${NC}"
    fi
fi

echo -e "${YELLOW}Searching for system fonts...${NC}"

# Find fonts from system directories
for dir in "${SYSTEM_FONT_DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        continue
    fi
    
    echo -e "${BLUE}Checking $dir for usable fonts...${NC}"
    
    # First try: look for specific font families
    for family in "${FONT_FAMILIES[@]}"; do
        # Use case-insensitive search
        found_fonts=$(find "$dir" -type f \( -iname "*${family}*.ttf" -o -iname "*${family}*.otf" \) 2>/dev/null)
        
        if [ -n "$found_fonts" ]; then
            echo -e "${GREEN}Found ${family} fonts in $dir${NC}"
            for font in $found_fonts; do
                # Copy only if we don't already have this font
                font_name=$(basename "$font")
                if [ ! -f "$FONTS_DIR/$font_name" ]; then
                    echo -e "${YELLOW}Copying $font_name${NC}"
                    cp "$font" "$FONTS_DIR/"
                fi
            done
        fi
    done
done

# If we still don't have fonts, just grab some generic TTF fonts as a last resort
FONT_COUNT=$(find "$FONTS_DIR" -type f \( -name "*.ttf" -o -name "*.otf" \) | wc -l)
if [ "$FONT_COUNT" -eq 0 ]; then
    echo -e "${YELLOW}No font families found, looking for any TTF/OTF fonts...${NC}"
    for dir in "${SYSTEM_FONT_DIRS[@]}"; do
        if [ ! -d "$dir" ]; then
            continue
        fi
        
        # Just grab the first few TTF/OTF fonts we find
        found_fonts=$(find "$dir" -type f \( -name "*.ttf" -o -name "*.otf" \) -not -path "*/\.*" | head -n 5 2>/dev/null)
        if [ -n "$found_fonts" ]; then
            for font in $found_fonts; do
                font_name=$(basename "$font")
                echo -e "${YELLOW}Copying $font_name${NC}"
                cp "$font" "$FONTS_DIR/"
            done
        fi
        
        # If we found some fonts, stop looking
        FONT_COUNT=$(find "$FONTS_DIR" -type f \( -name "*.ttf" -o -name "*.otf" \) | wc -l)
        if [ "$FONT_COUNT" -gt 0 ]; then
            break
        fi
    done
fi

# Check if we have any fonts now
FONT_COUNT=$(find "$FONTS_DIR" -type f \( -name "*.ttf" -o -name "*.otf" \) | wc -l)
if [ "$FONT_COUNT" -eq 0 ]; then
    echo -e "${RED}WARNING: No fonts found on system. Creating a minimal font...${NC}"
    
    # As a last resort, create a very minimal font.ttf file
    # (this is just a placeholder and won't display correctly)
    echo "MINIMUM FONT DATA" > "$FONTS_DIR/fallback.ttf"
    
    echo -e "${YELLOW}Created fallback placeholder font${NC}"
    FONT_COUNT=1
fi

# Show results
echo -e "${GREEN}Prepared $FONT_COUNT fonts in $FONTS_DIR${NC}"
ls -la "$FONTS_DIR"

# Create a Qt fonts.dir file
echo -e "${YELLOW}Creating fonts.dir file for Qt...${NC}"
cd "$FONTS_DIR"
echo "# Qt/X11 font directory" > fonts.dir

# Add all TTF and OTF fonts to the fonts.dir file
find . -maxdepth 1 -type f \( -name "*.ttf" -o -name "*.otf" \) | while read font; do
    # Get just the filename
    font_name=$(basename "$font")
    echo "$font_name" >> fonts.dir
done

echo -e "${GREEN}Font preparation complete with $FONT_COUNT fonts!${NC}"
