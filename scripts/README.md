# Prospr Light Scripts

This directory contains scripts for building, running, and setting up the Prospr Light printer management application for both development and target environments.

## Script Organization

The scripts directory is streamlined for clarity and direct use:

```
scripts/
├── build.sh           # Main build script for all platforms and configurations
├── prepare_fonts.sh   # Utility script for preparing font assets
├── run.sh             # Main script to launch the application
├── setup_dev_env.sh   # Development environment setup (installs dependencies, checks Qt modules, etc.)
└── README.md          # This file
```

## Main Entry Points

- `build.sh` — Universal build script for all platforms (development and target)
- `run.sh` — Launches the application with the correct environment
- `setup_dev_env.sh` — Sets up the development environment (dependencies, Qt modules, etc.)
- `prepare_fonts.sh` — Prepares and installs required fonts for the application

All scripts are designed to be run directly from the root of the repository. Each script is self-contained and does not depend on missing subdirectories or legacy scripts.
