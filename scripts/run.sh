#!/bin/bash
# Universal run script for Light application
# Works on both development and target environments

# Get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_DIR/build"

echo SCRIPT_DIR: $SCRIPT_DIR
echo PROJECT_DIR: $PROJECT_DIR
echo BUILD_DIR: $BUILD_DIR

# Parse arguments
RUN_TYPE="local"
DISPLAY_WIDTH=0
DISPLAY_HEIGHT=0
FORCE_FIXED_RESOLUTION=0

print_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -t, --type TYPE     Run type: 'local' or 'target' (default: local)"
    echo "  -w, --width WIDTH   Set display width (default: use system default)"
    echo "  -h, --height HEIGHT Set display height (default: use system default)"
    echo "  -f, --fixed         Force fixed resolution mode (sets QT_AARCH64_TARGET=1)"
    echo "  --help              Display this help message"
}

while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--type)
            RUN_TYPE="$2"
            shift 2
            ;;
        -w|--width)
            DISPLAY_WIDTH="$2"
            shift 2
            ;;
        -h|--height)
            DISPLAY_HEIGHT="$2"
            shift 2
            ;;
        -f|--fixed)
            FORCE_FIXED_RESOLUTION=1
            shift
            ;;
        --help)
            print_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            print_usage
            exit 1
            ;;
    esac
done

# Check if run type is valid
if [[ "$RUN_TYPE" != "local" && "$RUN_TYPE" != "target" ]]; then
    echo "Invalid run type: $RUN_TYPE"
    print_usage
    exit 1
fi

# Set resolution if specified
if [ $DISPLAY_WIDTH -gt 0 ] && [ $DISPLAY_HEIGHT -gt 0 ]; then
    echo "Setting display resolution to ${DISPLAY_WIDTH}x${DISPLAY_HEIGHT}"
    export DISPLAY_WIDTH=$DISPLAY_WIDTH
    export DISPLAY_HEIGHT=$DISPLAY_HEIGHT
    
    # Force Qt to use our specified resolution
    if [ $FORCE_FIXED_RESOLUTION -eq 1 ]; then
        echo "Forcing fixed resolution mode (QT_AARCH64_TARGET=1)"
        export QT_AARCH64_TARGET=1
        export QT_SCALE_FACTOR=1.0
        export QT_AUTO_SCREEN_SCALE_FACTOR=0
        export QT_ENABLE_HIGHDPI_SCALING=1
        # Pass environment variables to Qt
        export QT_ENV_DISPLAY_WIDTH=$DISPLAY_WIDTH
        export QT_ENV_DISPLAY_HEIGHT=$DISPLAY_HEIGHT
    fi
fi

# Source the appropriate environment
if [[ "$RUN_TYPE" == "local" ]]; then
    echo "Running for local development environment..."
    # Source development environment if exists
    ENV_DIR="$PROJECT_DIR/scripts/env"
    if [[ -f "$ENV_DIR/development.sh" ]]; then
        echo "Sourcing development environment..."
        source "$ENV_DIR/development.sh"
    fi
    # Determine application path based on platform
    # Use the x86_64-debug directory for local builds
    if [[ "$(uname -s)" == Darwin* ]]; then
        APP_PATH="$BUILD_DIR/x86_64-debug/light_qt5.app/Contents/MacOS/light_qt5"
    else
        APP_PATH="$BUILD_DIR/x86_64-debug/light_qt5"
    fi
    
    # Check if application exists
    if [ ! -f "$APP_PATH" ]; then
        echo "Application not found at $APP_PATH"
        echo "Please build the application first using: $PROJECT_DIR/scripts/build.sh"
        exit 1
    fi
    
    echo "Running Light application with Qt $QT_TARGET_VERSION..."
    echo "Qt base directory: $QT_BASE"
    
    # Add width and height args if specified
    ARGS="--development"
    if [ $DISPLAY_WIDTH -gt 0 ] && [ $DISPLAY_HEIGHT -gt 0 ]; then
        ARGS="$ARGS --width $DISPLAY_WIDTH --height $DISPLAY_HEIGHT"
    fi
    
    # Run the application with development arguments
    "$APP_PATH" $ARGS
    
elif [[ "$RUN_TYPE" == "target" ]]; then
    echo "Running on target environment (Allwinner T507)..."
    # Source target environment if exists
    ENV_DIR="$PROJECT_DIR/scripts/env"
    if [[ -f "$ENV_DIR/target.sh" ]]; then
        echo "Sourcing target environment..."
        source "$ENV_DIR/target.sh"
    fi
    # Run application on target
    # Note: Typically this would be handled differently since you're likely
    # running this script on the target device directly
    
    # Add width and height args if specified
    ARGS="--embedded"
    if [ $DISPLAY_WIDTH -gt 0 ] && [ $DISPLAY_HEIGHT -gt 0 ]; then
        ARGS="$ARGS --width $DISPLAY_WIDTH --height $DISPLAY_HEIGHT"
    fi
    
    # Run the application on target in embedded mode
    echo "Executing /opt/light/light_qt5 $ARGS"
    /opt/light/light_qt5 $ARGS
fi
