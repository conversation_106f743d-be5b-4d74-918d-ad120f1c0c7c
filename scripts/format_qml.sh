#!/bin/bash
# Format all QML files in the project using qmlformat

# Try to find qmlformat in common locations
QMLFORMAT_BIN="${QMLFORMAT_BIN:-$(command -v qmlformat || true)}"
if [ -z "$QMLFORMAT_BIN" ]; then
    if [ -x "/usr/lib/qt5/bin/qmlformat" ]; then
        QMLFORMAT_BIN="/usr/lib/qt5/bin/qmlformat"
    else
        echo "Error: qmlformat not found in PATH or /usr/lib/qt5/bin. Please install qtdeclarative5-dev-tools."
        exit 1
    fi
fi

# List of directories to ignore (relative to project root)
IGNORE_DIRS=(build context docs fonts qt_aarch64_builder .qm)

# Build the find prune expression from IGNORE_DIRS
PRUNE_EXPR=""
for dir in "${IGNORE_DIRS[@]}"; do
    PRUNE_EXPR+=" -path ./$dir -prune -o"
done

status=0
# shellcheck disable=SC2086
# The eval is safe here because IGNORE_DIRS is controlled in-script
# and we want to expand the dynamically built PRUNE_EXPR
# shellcheck disable=SC2016
find_cmd='find .'
find_cmd+="$PRUNE_EXPR -type f -name \"*.qml\" -print"
eval $find_cmd | while read -r file; do
    echo "Formatting: $file"
    if ! "$QMLFORMAT_BIN" -i "$file" 2>format_qml_error.log; then
        echo " Error formatting $file"
        cat format_qml_error.log
        status=1
        break
    fi
    rm -f format_qml_error.log

done

if [ $status -eq 0 ]; then
    echo "All QML files formatted."
else
    echo "Formatting failed on at least one file."
    exit 1
fi