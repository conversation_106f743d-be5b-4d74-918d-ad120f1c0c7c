#include <QGuiApplication>
#include <QQmlApplicationEngine>
#include <QFont>
#include <QFontDatabase>
#include <QDir>
#include <QLocale>
#include <QTranslator>
#include <QDebug>

#include <QmlRegistration.h>
#include <BackendSingletons.h>

int main(int argc, char *argv[])
{
#if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
    QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
#endif

    // Enable virtual keyboard
    qputenv("QT_IM_MODULE", QByteArray("cutekeyboard"));

    QGuiApplication app(argc, argv);

    // Load translations
    QTranslator translator;
    const QStringList uiLanguages = QLocale::system().uiLanguages();
    for (const QString &locale : uiLanguages) {
        const QString baseName = "light_qt5_" + QLocale(locale).name();
        if (translator.load(":/i18n/" + baseName)) {
            app.installTranslator(&translator);
            break;
        }
    }

    // Load fonts from the fonts directory
    QDir fontsDir("fonts");
    if (fontsDir.exists()) {
        QStringList filters;
        filters << "*.ttf" << "*.otf";
        fontsDir.setNameFilters(filters);
        for (const QString &fontFile : fontsDir.entryList()) {
            QString fontPath = fontsDir.absoluteFilePath(fontFile);
            if (QFontDatabase::addApplicationFont(fontPath) == -1) {
                qWarning() << "Failed to load font:" << fontFile;
            }
        }
    }

    // Set default application font
    QFont globalFont("Ubuntu", 12);
    app.setFont(globalFont);

    // Register custom types
    registerQmlTypes();
    registerBackendSingletons();

    // Initialize QML engine
    QQmlApplicationEngine engine;
    // Add project root to QML import path for singleton discovery
    engine.addImportPath(QCoreApplication::applicationDirPath());

    const QUrl url(QStringLiteral("qrc:/main.qml"));
    QObject::connect(
        &engine,
        &QQmlApplicationEngine::objectCreated,
        &app,
        [url](QObject *obj, const QUrl &objUrl) {
            if (!obj && url == objUrl)
                QCoreApplication::exit(-1);
        },
        Qt::QueuedConnection);
    engine.load(url);

    return app.exec();
}
