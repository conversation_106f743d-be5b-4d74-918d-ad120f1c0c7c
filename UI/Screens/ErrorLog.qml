/*
  ErrorLog Screen Component
  Displays the error log interface with a panel for viewing logs
  and controls for navigation.
*/

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import Components 1.0
import "qrc:/UI/Components/errorlog"
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme
import Backend.Error 1.0
import Backend.PrinterManager 1.0

Rectangle {
    anchors.fill: parent
    color: "transparent"

    property int headerTopMargin: 100

    TitledFrame {
        id: errorLogScreen

        anchors.fill: parent
        anchors.margins: Theme.Spacing.screenMargin
        title: "Error Log"
        titleFontPixelSize: 60
        titleHorizontalAlignment: Text.AlignLeft

        // Main error log panel
        ErrorLogPanel {
            id: errorLogPanel

            anchors.fill: parent
        }
    }

    property string pendingAction: ""

    // Use the new BlurOverlay component
    BlurOverlay {
        anchors.fill: parent
        sourceItem: errorLogScreen
        visible: confirmPopup.visible
    }

    // QPopup confirmation dialog
    QPopup {
        id: confirmPopup
        confirmationText: pendingAction === "reset" ? "Clear all errors and reset the machine status?" : "Clear all errors?"
        button1Text: pendingAction === "reset" ? "RESET" : "CLEAR"
        button1Color: Theme.Colors.red
        button1ImageSource: ""
        button2Text: "CANCEL"
        button2Color: Theme.Colors.black
        button2ImageSource: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_FORBIDDEN)
        numberOfButtons: 2
        onButton1Pressed: {
            if (pendingAction === "reset") {
                PrinterManager.setStatusIndicators(true, false, false);
                ErrorManager.clearErrors();
            } else if (pendingAction === "clear") {
                ErrorManager.clearErrors();
            }
            confirmPopup.close();
        }
        onButton2Pressed: {
            confirmPopup.close();
        }
    }

    // --- Top-right action buttons: REFRESH, RESET, CLEAR ---
    Row {
        spacing: 12
        anchors {
            right: errorLogScreen.right
            top: errorLogScreen.top
            rightMargin: 30
            topMargin: 10
        }
        // REFRESH button
        SquareButton {
            backgroundColor: Theme.Colors.darkBlue
            image.source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_RELOAD)
            onClicked: {
                ErrorManager.loadPendingErrors();
            }
            Accessible.name: "Refresh Error Log"

            // Pending error count overlay
            Item {
                anchors.fill: parent
                visible: ErrorManager.pendingErrorCount > 0

                Rectangle {
                    anchors.centerIn: parent
                    width: 28
                    height: 28
                    radius: 14
                    color: "white"
                    border.color: "transparent"
                    z: 2

                    Text {
                        anchors.centerIn: parent
                        text: ErrorManager.pendingErrorCount
                        color: "red"
                        font.bold: true
                        font.pixelSize: 20
                        z: 3
                    }
                }
            }
        }
        // RESET button
        SquareButton {
            backgroundColor: Theme.Colors.orange
            image.source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_RESET)
            onClicked: {
                pendingAction = "reset";
                confirmPopup.open();
            }
            Accessible.name: "Reset Error Log"
        }
        // CLEAR button
        SquareButton {
            backgroundColor: Theme.Colors.statusYellow
            image.source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_CLEAR)
            onClicked: {
                pendingAction = "clear";
                confirmPopup.open();
            }
            Accessible.name: "Clear Error Log"
        }
    }
}
