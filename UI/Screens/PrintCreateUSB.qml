import QtQuick 2.15
import QtQuick.Controls 2.15
import "qrc:/UI/Components"
// Import our modular components
import "qrc:/UI/Screens/printCreateUSB"
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: printScreen

    property bool showDeletePopup: false

    signal navigateToRequested(string to, string from)

    gradient: Theme.Colors.backgroundGradient

    // Main content layout using the modular PrintLayout component
    PrintLayoutUSB {
        id: printLayout

        anchors.fill: parent
        z: 0 // Ensure content is below the blur overlay
    }

}
