import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: fileManagerScreen

    color: Theme.Colors.backgroundSecondary

    // Main content layout
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: Theme.Spacing.large
        spacing: Theme.Spacing.medium

        // Title and action buttons
        RowLayout {
            Layout.fillWidth: true
            spacing: 15

            // Title
            Text {
                text: "File Manager"
                font: Theme.Typography.title
                color: Theme.Colors.textPrimary
                Layout.fillWidth: true
            }

            // Action buttons
            RowLayout {
                spacing: 10

                ProsprButton {
                    text: "Import"
                    buttonType: "secondary"
                    isOutlined: true
                }

                ProsprButton {
                    text: "Export Selected"
                    buttonType: "secondary"
                    isOutlined: true
                }

                ProsprButton {
                    text: "New Folder"
                    buttonType: "secondary"
                    isOutlined: true
                }

                ProsprButton {
                    text: "New Message"
                    buttonType: "primary"
                    onClicked: NavigationManager.go("CreateEditMessage")
                }

            }

        }

        // Main content area with sidebar and file list
        Rectangle {
            id: contentArea

            Layout.fillWidth: true
            Layout.fillHeight: true
            color: Theme.Colors.backgroundPrimary
            radius: 8
            Component.onCompleted: {
                Theme.Shadows.applyElevation(contentArea, 1);
            }

            RowLayout {
                anchors.fill: parent
                spacing: 0

                // Left sidebar for folder navigation
                Rectangle {
                    Layout.preferredWidth: 240
                    Layout.fillHeight: true
                    color: Theme.Colors.backgroundSecondary

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 0
                        spacing: 0

                        // Search folder
                        Rectangle {
                            Layout.fillWidth: true
                            height: 60
                            color: Theme.Colors.transparent

                            Rectangle {
                                anchors.fill: parent
                                anchors.margins: 15
                                radius: 4
                                border.width: 1
                                border.color: Theme.Colors.borderColor

                                RowLayout {
                                    anchors.fill: parent
                                    anchors.margins: 10
                                    spacing: 8

                                    Text {
                                        text: "🔍"
                                        font.pixelSize: Theme.Typography.caption
                                        color: Theme.Colors.textSecondary
                                    }

                                    TextField {
                                        Layout.fillWidth: true
                                        placeholderText: "Search folders..."
                                        font: Theme.Typography.inputText
                                        color: Theme.Colors.textPrimary
                                        background: null
                                    }

                                }

                            }

                        }

                        // Folder list header
                        Rectangle {
                            Layout.fillWidth: true
                            height: 40
                            color: Theme.Colors.borderColor

                            Text {
                                text: "FOLDERS"
                                font: Theme.Typography.caption
                                font.weight: Theme.Typography.weightMedium
                                color: Theme.Colors.textSecondary
                                anchors.verticalCenter: parent.verticalCenter
                                anchors.left: parent.left
                                anchors.leftMargin: 15
                            }

                        }

                        // Folder list
                        ListView {
                            id: folderListView

                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            clip: true

                            model: ListModel {
                                ListElement {
                                    name: "All Files"
                                    icon: "📂"
                                    count: 42
                                    isSelected: true
                                }

                                ListElement {
                                    name: "Recent"
                                    icon: "🕒"
                                    count: 15
                                    isSelected: false
                                }

                                ListElement {
                                    name: "Favorites"
                                    icon: "⭐"
                                    count: 7
                                    isSelected: false
                                }

                                ListElement {
                                    name: "Product Labels"
                                    icon: "📂"
                                    count: 12
                                    isSelected: false
                                }

                                ListElement {
                                    name: "Shipping Labels"
                                    icon: "📂"
                                    count: 8
                                    isSelected: false
                                }

                                ListElement {
                                    name: "Barcodes"
                                    icon: "📂"
                                    count: 5
                                    isSelected: false
                                }

                                ListElement {
                                    name: "Templates"
                                    icon: "📂"
                                    count: 10
                                    isSelected: false
                                }

                                ListElement {
                                    name: "Archive"
                                    icon: "🗄️"
                                    count: 20
                                    isSelected: false
                                }

                                ListElement {
                                    name: "Trash"
                                    icon: "🗑️"
                                    count: 3
                                    isSelected: false
                                }

                            }

                            delegate: Rectangle {
                                width: ListView.view.width
                                height: 50
                                color: model.isSelected ? Qt.rgba(Theme.Colors.secondary.r, Theme.Colors.secondary.g, Theme.Colors.secondary.b, 0.12) : "transparent"

                                RowLayout {
                                    anchors.fill: parent
                                    anchors.leftMargin: 15
                                    anchors.rightMargin: 15
                                    spacing: 10

                                    Text {
                                        text: model.icon
                                        font.pixelSize: 18
                                    }

                                    Text {
                                        text: model.name
                                        font.pixelSize: 14
                                        font.weight: model.isSelected ? Theme.Typography.weightMedium : Theme.Typography.weightRegular
                                        color: model.isSelected ? Theme.Colors.secondary : Theme.Colors.textSecondary
                                    }

                                    // Spacer
                                    Item {
                                        Layout.fillWidth: true
                                    }

                                    Text {
                                        text: model.count
                                        font.pixelSize: 14
                                        color: Theme.Colors.textSecondary
                                    }

                                }

                                MouseArea {
                                    anchors.fill: parent
                                    cursorShape: Qt.PointingHandCursor
                                    onClicked: {
                                        // Select this folder
                                        for (var i = 0; i < folderListView.model.count; i++) {
                                            folderListView.model.setProperty(i, "isSelected", i === index);
                                        }
                                    }
                                }

                            }

                            ScrollBar.vertical: ScrollBar {
                            }

                        }

                        // Storage usage
                        Rectangle {
                            Layout.fillWidth: true
                            height: 80
                            color: Theme.Colors.borderColor

                            ColumnLayout {
                                anchors.fill: parent
                                anchors.margins: 15
                                spacing: 5

                                Text {
                                    text: "Storage"
                                    font.pixelSize: 14
                                    font.weight: Font.Medium
                                    color: Theme.Colors.textSecondary
                                }

                                Rectangle {
                                    Layout.fillWidth: true
                                    height: 8
                                    radius: 4
                                    color: Theme.Colors.white

                                    Rectangle {
                                        width: parent.width * 0.65
                                        height: parent.height
                                        radius: 4
                                        color: Theme.Colors.secondary
                                    }

                                }

                                Text {
                                    text: "6.5 GB of 10 GB used"
                                    font.pixelSize: 12
                                    color: Theme.Colors.textSecondary
                                }

                            }

                        }

                    }

                }

                // Main file content area
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: Theme.Colors.white

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 20
                        spacing: 20

                        // Search and filter bar
                        RowLayout {
                            Layout.fillWidth: true
                            spacing: 20

                            // Search field
                            Rectangle {
                                Layout.preferredWidth: 300
                                height: 40
                                border.width: 1
                                border.color: Theme.Colors.borderColor
                                radius: 4

                                RowLayout {
                                    anchors.fill: parent
                                    anchors.margins: 10
                                    spacing: 8

                                    Text {
                                        text: "🔍"
                                        font.pixelSize: 16
                                        color: Theme.Colors.textSecondary
                                    }

                                    TextField {
                                        Layout.fillWidth: true
                                        placeholderText: "Search files..."
                                        background: null
                                        selectByMouse: true
                                    }

                                }

                            }

                            // Sort options
                            RowLayout {
                                spacing: 10

                                Text {
                                    text: "Sort by:"
                                    font.pixelSize: 14
                                    color: Theme.Colors.textSecondary
                                }

                                ComboBox {
                                    model: ["Name", "Date Modified", "Size", "Type"]
                                    currentIndex: 1
                                    Layout.preferredWidth: 150
                                }

                                // Sort direction toggle
                                Rectangle {
                                    width: 36
                                    height: 36
                                    radius: 4
                                    color: Theme.Colors.transparent

                                    Text {
                                        anchors.centerIn: parent
                                        text: "↓"
                                        font.pixelSize: 16
                                    }

                                    MouseArea {
                                        anchors.fill: parent
                                        cursorShape: Qt.PointingHandCursor
                                    }

                                }

                            }

                            // File type filter
                            RowLayout {
                                spacing: 10

                                Text {
                                    text: "Type:"
                                    font.pixelSize: 14
                                    color: Theme.Colors.textSecondary
                                }

                                ComboBox {
                                    model: ["All", "Messages", "Templates", "Configurations"]
                                    currentIndex: 0
                                    Layout.preferredWidth: 150
                                }

                            }

                            // Spacer
                            Item {
                                Layout.fillWidth: true
                            }

                            // View options: grid/list
                            RowLayout {
                                spacing: 10

                                Rectangle {
                                    width: 40
                                    height: 40
                                    radius: 4
                                    color: Theme.Colors.secondary

                                    Text {
                                        anchors.centerIn: parent
                                        text: "▦"
                                        font.pixelSize: 16
                                        color: Theme.Colors.white
                                    }

                                    MouseArea {
                                        anchors.fill: parent
                                        cursorShape: Qt.PointingHandCursor
                                    }

                                }

                                Rectangle {
                                    width: 40
                                    height: 40
                                    radius: 4
                                    color: Theme.Colors.transparent

                                    Text {
                                        anchors.centerIn: parent
                                        text: "≡"
                                        font.pixelSize: 16
                                        color: Theme.Colors.textSecondary
                                    }

                                    MouseArea {
                                        anchors.fill: parent
                                        cursorShape: Qt.PointingHandCursor
                                    }

                                }

                            }

                            // Selection actions
                            Rectangle {
                                width: 40
                                height: 40
                                radius: 4
                                color: Theme.Colors.transparent

                                Text {
                                    anchors.centerIn: parent
                                    text: "⋮"
                                    font.pixelSize: 18
                                    color: Theme.Colors.textSecondary
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    cursorShape: Qt.PointingHandCursor
                                }

                            }

                        }

                        // Path breadcrumbs
                        RowLayout {
                            Layout.fillWidth: true
                            spacing: 5

                            Text {
                                text: "Path:"
                                font.pixelSize: 14
                                color: Theme.Colors.textSecondary
                            }

                            Repeater {
                                model: ["Home", "All Files"]

                                RowLayout {
                                    spacing: 5

                                    Text {
                                        text: modelData
                                        font.pixelSize: 14
                                        font.weight: index === 1 ? Font.Medium : Font.Normal
                                        color: Theme.Colors.secondary

                                        MouseArea {
                                            anchors.fill: parent
                                            cursorShape: Qt.PointingHandCursor
                                        }

                                    }

                                    Text {
                                        text: "/"
                                        font.pixelSize: 14
                                        color: Theme.Colors.textSecondary
                                        visible: index < 1
                                    }

                                }

                            }

                        }

                        // File grid view
                        GridView {
                            id: fileGrid

                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            clip: true
                            cellWidth: 200
                            cellHeight: 220

                            model: ListModel {
                                ListElement {
                                    name: "Product Label 001"
                                    type: "message"
                                    icon: "📄"
                                    modified: "Today, 10:30 AM"
                                    size: "24 KB"
                                    isSelected: false
                                }

                                ListElement {
                                    name: "Shipping Label"
                                    type: "message"
                                    icon: "📄"
                                    modified: "Today, 09:15 AM"
                                    size: "32 KB"
                                    isSelected: false
                                }

                                ListElement {
                                    name: "Basic Template"
                                    type: "template"
                                    icon: "📋"
                                    modified: "Yesterday, 03:45 PM"
                                    size: "18 KB"
                                    isSelected: false
                                }

                                ListElement {
                                    name: "Barcode Config"
                                    type: "config"
                                    icon: "⚙️"
                                    modified: "Yesterday, 11:20 AM"
                                    size: "12 KB"
                                    isSelected: false
                                }

                                ListElement {
                                    name: "High Res Settings"
                                    type: "config"
                                    icon: "⚙️"
                                    modified: "Mar 28, 2025"
                                    size: "10 KB"
                                    isSelected: false
                                }

                                ListElement {
                                    name: "Product Label 002"
                                    type: "message"
                                    icon: "📄"
                                    modified: "Mar 27, 2025"
                                    size: "26 KB"
                                    isSelected: false
                                }

                                ListElement {
                                    name: "Batch Code Template"
                                    type: "template"
                                    icon: "📋"
                                    modified: "Mar 25, 2025"
                                    size: "15 KB"
                                    isSelected: false
                                }

                                ListElement {
                                    name: "Date & Time Template"
                                    type: "template"
                                    icon: "📋"
                                    modified: "Mar 22, 2025"
                                    size: "14 KB"
                                    isSelected: false
                                }

                                ListElement {
                                    name: "QR Code Message"
                                    type: "message"
                                    icon: "📄"
                                    modified: "Mar 20, 2025"
                                    size: "30 KB"
                                    isSelected: false
                                }

                                ListElement {
                                    name: "Economy Settings"
                                    type: "config"
                                    icon: "⚙️"
                                    modified: "Mar 18, 2025"
                                    size: "8 KB"
                                    isSelected: false
                                }

                                ListElement {
                                    name: "Text Only Label"
                                    type: "message"
                                    icon: "📄"
                                    modified: "Mar 15, 2025"
                                    size: "20 KB"
                                    isSelected: false
                                }

                                ListElement {
                                    name: "Counter Template"
                                    type: "template"
                                    icon: "📋"
                                    modified: "Mar 12, 2025"
                                    size: "16 KB"
                                    isSelected: false
                                }

                            }

                            delegate: Rectangle {
                                width: 180
                                height: 200
                                radius: 8
                                color: model.isSelected ? "Theme.Colors.secondary10" : "white"
                                border.width: 1
                                border.color: model.isSelected ? "Theme.Colors.secondary" : "#e0e0e0"

                                ColumnLayout {
                                    anchors.fill: parent
                                    anchors.margins: 15
                                    spacing: 10

                                    // File type icon
                                    Rectangle {
                                        Layout.alignment: Qt.AlignHCenter
                                        width: 60
                                        height: 60
                                        radius: 8
                                        color: {
                                            if (model.type === "message")
                                                return "#f04e2310";

                                            if (model.type === "template")
                                                return "Theme.Colors.secondary10";

                                            return "#00669910";
                                        }

                                        Text {
                                            anchors.centerIn: parent
                                            text: model.icon
                                            font.pixelSize: 24
                                        }

                                        // Favorite indicator (for Product Label 001)
                                        Text {
                                            visible: index === 0
                                            text: "⭐"
                                            font.pixelSize: 14
                                            anchors.right: parent.right
                                            anchors.top: parent.top
                                            anchors.topMargin: -5
                                            anchors.rightMargin: -5
                                        }

                                    }

                                    // File name
                                    Text {
                                        text: model.name
                                        font.pixelSize: 14
                                        font.weight: Font.Medium
                                        color: Theme.Colors.secondary
                                        elide: Text.ElideRight
                                        Layout.fillWidth: true
                                        horizontalAlignment: Text.AlignHCenter
                                    }

                                    // Type indicator
                                    Rectangle {
                                        Layout.alignment: Qt.AlignHCenter
                                        height: 24
                                        width: {
                                            if (model.type === "message")
                                                return 80;

                                            if (model.type === "template")
                                                return 90;

                                            return 110;
                                        }
                                        radius: 12
                                        color: {
                                            if (model.type === "message")
                                                return "#f04e2310";

                                            if (model.type === "template")
                                                return "Theme.Colors.secondary10";

                                            return "#00669910";
                                        }

                                        Text {
                                            anchors.centerIn: parent
                                            text: {
                                                if (model.type === "message")
                                                    return "Message";

                                                if (model.type === "template")
                                                    return "Template";

                                                return "Configuration";
                                            }
                                            font.pixelSize: 12
                                            font.weight: Font.Medium
                                            color: {
                                                if (model.type === "message")
                                                    return "#f04e23";

                                                if (model.type === "template")
                                                    return "Theme.Colors.secondary";

                                                return "#006699";
                                            }
                                        }

                                    }

                                    // Modified date
                                    Text {
                                        text: model.modified
                                        font.pixelSize: 12
                                        color: Theme.Colors.textSecondary
                                        Layout.alignment: Qt.AlignHCenter
                                    }

                                    // File size
                                    Text {
                                        text: model.size
                                        font.pixelSize: 12
                                        color: Theme.Colors.textSecondary
                                        Layout.alignment: Qt.AlignHCenter
                                    }

                                }

                                // Checkbox for selection (shown on hover or when selected)
                                CheckBox {
                                    anchors.right: parent.right
                                    anchors.top: parent.top
                                    anchors.margins: 5
                                    checked: model.isSelected
                                    visible: mouseArea.containsMouse || model.isSelected
                                    onClicked: {
                                        fileGrid.model.setProperty(index, "isSelected", checked);
                                    }
                                }

                                MouseArea {
                                    // Show context menu

                                    id: mouseArea

                                    anchors.fill: parent
                                    hoverEnabled: true
                                    acceptedButtons: Qt.LeftButton | Qt.RightButton
                                    onClicked: {
                                        if (mouse.button === Qt.RightButton) {
                                        } else {
                                            // Toggle selection
                                            fileGrid.model.setProperty(index, "isSelected", !model.isSelected);
                                        }
                                    }
                                    onDoubleClicked: {
                                        // Open file based on type
                                        if (model.type === "message" || model.type === "template")
                                            NavigationManager.go("CreateEditMessage");
                                        else if (model.type === "config")
                                            NavigationManager.go("PrinterSettings");
                                    }
                                }

                            }

                        }

                        // Status bar with item count and selection info
                        Rectangle {
                            Layout.fillWidth: true
                            height: 40
                            color: Theme.Colors.transparent
                            radius: 4

                            RowLayout {
                                anchors.fill: parent
                                anchors.leftMargin: 15
                                anchors.rightMargin: 15

                                Text {
                                    text: "12 items (0 selected)"
                                    font.pixelSize: 14
                                    color: Theme.Colors.textSecondary
                                }

                                // Spacer
                                Item {
                                    Layout.fillWidth: true
                                }

                                Text {
                                    text: "6.5 GB of 10 GB used"
                                    font.pixelSize: 14
                                    color: Theme.Colors.textSecondary
                                }

                            }

                        }

                    }

                }

            }

        }

    }

}
