import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Theme" as Theme
import "qrc:/UI/Core/constants.js" as CONST
import Backend.ConfigManager 1.0
import Backend.UserManager 1.0
import Backend.PrinterManager 1.0
import Backend.PrintfileManager 1.0

// Main print layout component that organizes the three panels
Item {
    id: printLayout

    // Main content area with shadow and rounded corners
    Rectangle {
        id: mainContent

        anchors.fill: parent
        color: "transparent" // Use transparent since parent already has gradient
        radius: Theme.Radius.large

        // Main layout with three panels
        Item {
            anchors.fill: parent
            anchors.margins: 20

            PrintPreviewPanel {
                id: topPanel

                anchors {
                    top: parent.top
                    left: parent.left
                    right: parent.right
                }
                height: parent.height * 0.3
                showGridLines: gridLinesSwitch.checked
            }

            // Left Panel - File Selection
            Rectangle {
                id: leftPanel

                anchors {
                    top: topPanel.bottom
                    left: parent.left
                    bottom: parent.bottom
                    topMargin: 20
                }
                width: parent.width * 0.6 - 10

                // --- API: Signals for navigation/actions ---
                signal navigateToRequested(string to, string from)

                // Helper to remove file and update selection by ID
                function deleteCurrentFile() {
                    if (PrintfileManager.selectedPrintfileId === "") {
                        console.log("[LeftPanel] No file selected to delete");
                        return ;
                    }

                    if (PrintfileManager.selectedPrintfileId === PrinterManager.currentPrintingFileId) {
                        console.log("Can't delete file that is currently printing");

                        deletePopup.open()

                        return ;
                    }

                    PrintfileManager.deletePrintfile(PrintfileManager.selectedPrintfileId)
                }

                function selectPrintfileId(printfileId) {
                    PrintfileManager.currentPrintfileId = printfileId;
                    PrintfileManager.selectedPrintfileId = printfileId;
                }

                radius: Theme.Radius.xxxlarge
                color: Theme.Colors.white

                Text {
                    id: fileHeaderText

                    text: "File"
                    font.family: Theme.Fonts.youngSerif
                    font.pixelSize: 46
                    font.weight: Font.Bold
                    color: Theme.Colors.black

                    anchors {
                        top: parent.top
                        left: parent.left
                        topMargin: 30
                        leftMargin: 30
                        rightMargin: 30
                    }

                }

                // Printfile/File List View
                ListView {
                    id: fileListView

                    clip: true
                    spacing: Theme.Spacing.small
                    model: PrintfileManager.printfileModel

                    anchors {
                        top: fileHeaderText.bottom
                        left: parent.left
                        right: actionsColumn.left
                        bottom: parent.bottom
                        topMargin: 10
                        leftMargin: 30
                        rightMargin: 30
                        bottomMargin: 10
                    }

                    delegate: Rectangle {
                        id: fileDelegate

                        // Access the printfile object for this index
                        property var printfileObj: model
                        readonly property bool isSelected: printfileObj ? printfileObj.printfileId === PrintfileManager.currentPrintfileId : false
                        readonly property bool isPrinting: printfileObj ? PrinterManager.currentPrintingFileId === printfileObj.printfileId : false

                        width: ListView.view.width - 15
                        height: 74
                        color: isPrinting ? Theme.Colors.orange : (isSelected ? Qt.darker(Theme.Colors.selected, 1.1) : Theme.Colors.noSelected)
                        radius: Theme.Radius.xmlarge

                        Text {
                            id: fileTextFromListView

                            text: printfileObj ? printfileObj.name : ""
                            color: isPrinting ? Theme.Colors.white : Theme.Colors.black
                            font.weight: Font.Medium
                            font.pixelSize: 28

                            anchors {
                                left: parent.left
                                verticalCenter: parent.verticalCenter
                                margins: 10
                            }

                        }

                        // Dot mark for selected printfile
                        Rectangle {
                            visible: isSelected
                            width: 16
                            height: 16
                            radius: 8
                            color: Theme.Colors.primary
                            anchors {
                                left: fileTextFromListView.right
                                verticalCenter: parent.verticalCenter
                                leftMargin: 10
                            }
                        }

                        // Status badge
                        Rectangle {
                            visible: isPrinting
                            width: statusText.width + 16
                            height: 34
                            radius: Theme.Radius.xxmlarge
                            color: Theme.Colors.white
                            border.width: 1
                            border.color: Theme.Colors.borderCol

                            anchors {
                                left: fileTextFromListView.right
                                verticalCenter: parent.verticalCenter
                                margins: 20
                            }

                            Text {
                                id: statusText

                                anchors.centerIn: parent
                                text: "Printing"
                                color: Theme.Colors.orange
                                font.pixelSize: 20
                                font.weight: Font.Medium
                            }

                        }

                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                if (printfileObj) {
                                    leftPanel.selectPrintfileId(printfileObj.printfileId);
                                }
                            }
                        }

                    }

                    ScrollBar.vertical: ScrollBar {
                        policy: ScrollBar.AsNeeded // Shows only when needed
                        width: 10
                        anchors.right: parent.right

                        background: Rectangle {
                            color: "transparent"
                        }

                        contentItem: Rectangle {
                            color: Theme.Colors.statusLightGrey // Light gray for the scrollbar handle
                            radius: Theme.Radius.large
                        }

                    }

                }

                // Action buttons column
                ColumnLayout {
                    id: actionsColumn

                    width: 330
                    spacing: Theme.Spacing.large

                    anchors {
                        top: fileHeaderText.bottom
                        right: parent.right
                        bottom: parent.bottom
                        rightMargin: 30
                        bottomMargin: 30
                    }

                    // CREATE button
                    LeftPanelButton {
                        text: "CREATE"
                        source: PathResolver.resolveAsset(CONST.ASSET_PATH.IMAGE_ADD_CIRCLE)
                        onClicked: {
                            console.log("Create button clicked");
                            PrintfileManager.startNewPrintfile()
                            NavigationManager.go(CONST.SCREEN.PRINT_CREATE, CONST.SCREEN.PRINT);
                        }
                    }

                    // EDIT button
                    LeftPanelButton {
                        text: "EDIT"
                        source: PathResolver.resolveAsset(CONST.ASSET_PATH.IMAGE_EDIT_RECTANGLE)
                        onClicked: {
                            console.log("Edit button clicked");

                            if (PrinterManager.currentPrintingFileId !== PrintfileManager.selectedPrintfileId) {
                                PrintfileManager.startEditPrintfile(PrintfileManager.currentPrintfileId);
                                NavigationManager.go(CONST.SCREEN.PRINT_CREATE, CONST.SCREEN.PRINT);
                            }
                        }
                    }

                    // DELETE button
                    LeftPanelButton {
                        text: "DELETE"
                        source: PathResolver.resolveAsset(CONST.ASSET_PATH.IMAGE_TRASH)
                        onClicked: {
                            leftPanel.deleteCurrentFile();
                        }
                    }

                    // PRINTER SETTING button
                    LeftPanelButton {
                        text: "PRINTER SETTING"
                        onClicked: {
                            console.log("Printer Setting button clicked");
                            NavigationManager.go(CONST.SCREEN.PRINT_SETTINGS, CONST.SCREEN.PRINT);
                        }
                    }

                    SwitchControl {
                        id: gridLinesSwitch

                        Layout.preferredHeight: 40
                        Layout.fillWidth: true

                        checked: ConfigManager.printCreateShowGridLines

                        text: "GRID LINES"
                        font.weight: Font.Bold
                        textColor: Theme.Colors.white

                        textAlignment: Text.AlignHCenter
                        backgroundRectangle.radius: 12
                        backgroundRectangle.color: Theme.Colors.darkBlue
                        backgroundRectangle.border.width: 0
                        font.pixelSize: 24
                        padding: 10

                        indicator: SwitchIndicator {
                            anchors.right: parent.right
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.rightMargin: 10

                            width: height * 2
                            height: gridLinesSwitch.height / 2

                            checked: parent.checked
                        }

                        onCheckedChanged: {
                            ConfigManager.printCreateShowGridLines = checked;
                        }
                    }


                }

            }

            // Print Controls and Status Panel
            Item {
                id: rightPanel

                anchors {
                    top: topPanel.bottom
                    left: leftPanel.right
                    right: parent.right
                    bottom: parent.bottom
                    topMargin: 20
                    leftMargin: 20
                }

                // Properties to expose print settings to other components
                property int copies: 1
                property int startAt: 1
                property int endAt: 100

                // Signals
                signal printRequested()
                signal settingsChanged()
                signal printButtonPressed()
                signal stopButtonPressed()

                // Print Controls Section (upper part)
                Rectangle {
                    id: printControlsSection

                    height: parent.height * 0.6
                    radius: Theme.Radius.large
                    color: "transparent"

                    anchors {
                        top: parent.top
                        left: parent.left
                        right: parent.right
                    }

                    // Controls layout
                    Column {
                        spacing: Theme.Spacing.small

                        anchors {
                            fill: parent
                            margins: 15
                        }

                        // Print Range inputs
                        Row {
                            width: parent.width
                            height: 50
                            spacing: Theme.Spacing.small

                            // Start At
                            Column {
                                visible: UserManager.currentUser.role !== "Guest"
                                width: (parent.width - parent.spacing) / 2
                                spacing: Theme.Spacing.xxsmall

                                Text {
                                    text: "Start At"
                                    font.pixelSize: 28
                                    color: Theme.Colors.secondary
                                }

                                Rectangle {
                                    width: parent.width
                                    height: 74
                                    radius: Theme.Radius.xmlarge
                                    color: Theme.Colors.white

                                    TextInput {
                                        id: startAtInput

                                        text: "500"
                                        color: Theme.Colors.black
                                        font.pixelSize: 28
                                        verticalAlignment: TextInput.AlignVCenter
                                        selectByMouse: true

                                        anchors {
                                            fill: parent
                                            margins: 8
                                        }

                                        validator: IntValidator {
                                            bottom: 1
                                            top: 1e+09
                                        }

                                    }

                                }

                            }

                            // End At
                            Column {
                                visible: UserManager.currentUser.role !== "Guest"
                                width: (parent.width - parent.spacing) / 2
                                spacing: Theme.Spacing.xxsmall

                                Text {
                                    text: "End At"
                                    font.pixelSize: 28
                                    color: Theme.Colors.secondary
                                }

                                Rectangle {
                                    width: parent.width
                                    height: 74
                                    radius: Theme.Radius.xmlarge
                                    color: Theme.Colors.white

                                    TextInput {
                                        id: endAtInput

                                        text: "1200"
                                        color: Theme.Colors.black
                                        font.pixelSize: 28
                                        verticalAlignment: TextInput.AlignVCenter
                                        selectByMouse: true

                                        anchors {
                                            fill: parent
                                            margins: 8
                                        }

                                        validator: IntValidator {
                                            bottom: 1
                                            top: 1e+09
                                        }

                                    }

                                }

                            }

                        }

                        // Print and Stop buttons
                        Item {
                            width: parent.width
                            height: 200

                            Row {
                                id: buttonRow

                                anchors.horizontalCenter: parent.horizontalCenter // Center the row
                                spacing: Theme.Spacing.small
                                topPadding: 60
                                height: parent.height

                                // PRINT button
                                Rectangle {
                                    width: buttonRow.height
                                    height: buttonRow.height
                                    radius: height / 2
                                    color: Qt.lighter(Theme.Colors.statusGreen, 1.2)

                                    Rectangle {
                                        anchors.centerIn: parent
                                        width: parent.height - 20
                                        height: parent.height - 20
                                        radius: height / 2
                                        color: Theme.Colors.statusGreen

                                        Text {
                                            anchors.centerIn: parent
                                            text: "PRINT"
                                            font.family: Theme.Fonts.youngSerif
                                            font.pixelSize: 48
                                            font.weight: Font.Bold
                                            color: Theme.Colors.white
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            enabled: !PrinterManager.statusIndicators[2] // disable when error state
                                            onClicked: {
                                                console.log("Print button clicked");
                                                printStatusValue.text = "Printing";
                                                printStatusValue.color = Theme.Colors.orange; // Orange
                                                rightPanel.printButtonPressed();
                                            }
                                        }

                                    }

                                }

                                // STOP button
                                Rectangle {
                                    width: buttonRow.height
                                    height: buttonRow.height
                                    radius: height / 2
                                    color: Qt.lighter(Theme.Colors.statusRed, 1.2)

                                    Rectangle {
                                        anchors.centerIn: parent
                                        width: parent.height - 20
                                        height: parent.height - 20
                                        radius: height / 2
                                        color: Theme.Colors.statusRed

                                        Text {
                                            anchors.centerIn: parent
                                            text: "STOP"
                                            font.family: Theme.Fonts.youngSerif
                                            font.pixelSize: 48
                                            font.weight: Font.Bold
                                            color: Theme.Colors.white
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                console.log("Stop button clicked");
                                                printStatusValue.text = "Stopped";
                                                printStatusValue.color = Theme.Colors.darkRed; // Red
                                                rightPanel.stopButtonPressed();
                                            }
                                        }

                                    }

                                }

                            }

                        }

                    }

                }

                // Print Status Section (lower part)
                Rectangle {
                    id: statusSection

                    radius: Theme.Radius.large
                    border.color: Theme.Colors.orange
                    border.width: 1
                    color: Theme.Colors.borderCol
                    height: 116 // Fixed height set to 116

                    anchors {
                        top: printControlsSection.bottom
                        left: parent.left
                        right: parent.right
                        topMargin: 15
                    }

                    Column {
                        spacing: Theme.Spacing.xsmall
                        anchors.fill: parent
                        anchors.margins: 10

                        // Header row: Labels
                        Row {
                            width: parent.width
                            height: 40

                            Repeater {
                                model: ["Status", "Printed", "History"]

                                delegate: Rectangle {
                                    width: parent.width / 3
                                    height: parent.height
                                    color: "transparent"

                                    Text {
                                        anchors.centerIn: parent
                                        text: modelData
                                        font.pixelSize: 28
                                        font.weight: Font.DemiBold
                                        color: Theme.Colors.black
                                    }

                                    Rectangle {
                                        anchors.top: parent.top
                                        anchors.topMargin: -10
                                        anchors.right: parent.right
                                        width: index < 2 ? 1 : 0
                                        height: 116
                                        color: Theme.Colors.orange
                                    }

                                }

                            }

                        }

                        // Value row
                        Row {
                            width: parent.width
                            height: 40

                            // Status value with pill
                            Rectangle {
                                width: parent.width / 3
                                height: parent.height
                                color: "transparent"

                                Rectangle {
                                    width: 111
                                    height: 34
                                    radius: Theme.Radius.large
                                    color: Theme.Colors.white
                                    anchors.centerIn: parent
                                    border.color: "transparent"

                                    Text {
                                        id: printStatusValue

                                        anchors.centerIn: parent
                                        text: "Stopped"
                                        font.pixelSize: 20
                                        font.weight: Font.DemiBold
                                        color: Theme.Colors.darkRed
                                    }

                                }

                            }

                            // Printed count
                            Rectangle {
                                width: parent.width / 3
                                height: parent.height
                                color: "transparent"

                                Text {
                                    id: printedCountValue

                                    anchors.centerIn: parent
                                    text: "520000"
                                    font.pixelSize: 28
                                    font.weight: Font.Bold
                                    color: Theme.Colors.orange
                                }

                            }

                            // History count
                            Rectangle {
                                width: parent.width / 3
                                height: parent.height
                                color: "transparent"

                                Text {
                                    id: historyCountValue

                                    anchors.centerIn: parent
                                    text: "410000"
                                    font.pixelSize: 28
                                    font.weight: Font.Bold
                                    color: Theme.Colors.orange
                                }

                            }

                        }

                    }

                }

                // RESET COUNTER button
                Rectangle {
                    id: resetCounterButton

                    width: parent.width
                    height: 78
                    radius: Theme.Radius.xmlarge
                    color: Theme.Colors.secondary

                    anchors {
                        top: statusSection.bottom
                        left: parent.left
                        right: parent.right
                        topMargin: 15
                    }

                    Text {
                        anchors.centerIn: parent
                        text: "RESET COUNTER"
                        font.pixelSize: 30
                        font.weight: Font.Bold
                        color: Theme.Colors.white
                    }

                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            // Reset counter
                            console.log("Reset counter clicked");
                            printedCountValue.text = "0";
                        }
                    }

                }

            }

            Connections {
                function onPrintButtonPressed() {
                    if (PrinterManager.currentPrintingFileId === "" && PrintfileManager.selectedPrintfileId !== "") {
                        PrinterManager.currentPrintingFileId = PrintfileManager.selectedPrintfileId;
                    }
                }

                function onStopButtonPressed() {
                    if (PrinterManager.currentPrintingFileId !== "") {
                        leftPanel.selectPrintfileId(PrintfileManager.selectedPrintfileId)
                        PrinterManager.currentPrintingFileId = "";
                    }
                }

                target: rightPanel
            }

            Connections {
                function onPrintfilesLoaded() {
                    // Update the list selection
                    if (PrintfileManager.selectedPrintfileId !== "") {
                        leftPanel.selectPrintfileId(PrintfileManager.selectedPrintfileId);
                    }
                }

                target: PrintfileManager
            }

            Component.onCompleted: {
                if (PrintfileManager.selectedPrintfileId !== "") {
                    leftPanel.selectPrintfileId(PrintfileManager.selectedPrintfileId);
                }
            }

        }

    }

    BlurOverlay {
        anchors.fill: mainContent
        source: mainContent
        visible: deletePopup.visible
    }

    QPopup {
        id: deletePopup

        confirmationText: "Cannot delete file currently printing"
        button1Text: "OK"
        button1Color: Theme.Colors.black
        numberOfButtons: 1
        onButton1Pressed: {
            deletePopup.close(); //Close the popup
        }
        onOpened: {
            mainLayout.popupVisible = true;
        }
        onClosed: {
            mainLayout.popupVisible = false;
        }
    }
}
