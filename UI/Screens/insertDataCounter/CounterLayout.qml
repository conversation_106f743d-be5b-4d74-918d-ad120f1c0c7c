import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Theme" as Theme

// Main print layout component that organizes the three panels
Item {
    id: counterLayout

    signal navigateToRequested(string to, string from)

    // Main content area with shadow and rounded corners
    Rectangle {
        id: mainContent

        anchors.fill: parent
        anchors.margins: 20
        color: "transparent" // Use transparent since parent already has gradient
        radius: Theme.Radius.large

        // Main layout with three panels
        Item {
            anchors.fill: parent

            // Top Panel - Print Preview
            TopPanelCounter {
                id: topPanel

                height: parent.height * 0.3

                anchors {
                    top: parent.top
                    left: parent.left
                    right: parent.right
                }

            }

            // Left Panel - File Selection
            LeftPanelCounter {
                id: leftPanel

                width: parent.width * 0.6 - 10

                anchors {
                    top: topPanel.bottom
                    left: parent.left
                    bottom: parent.bottom
                    topMargin: 20
                }

            }

            // Right Panel - Print Controls and Status
            RightPanelCounter {
                id: rightPanel

                width: parent.width * 0.4

                anchors {
                    top: topPanel.bottom
                    left: leftPanel.right
                    bottom: parent.bottom
                    topMargin: 20
                    leftMargin: -140
                }

            }

        }

    }

}
