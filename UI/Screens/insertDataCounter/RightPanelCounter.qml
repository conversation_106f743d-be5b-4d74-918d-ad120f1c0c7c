import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: printControlsSection

    height: parent.height
    width: parent.width * 0.7
    radius: Theme.Radius.xxxlarge
    color: "transparent"

    Rectangle {
        anchors.centerIn: parent
        height: parent.height
        width: parent.width * 0.45
        radius: Theme.Radius.xxxlarge
        color: Theme.Colors.white

        ColumnLayout {
            anchors.fill: parent
            spacing: 30
            anchors.margins: 30

            // Reverse Button
            CustomSwitchButton {
                Layout.fillWidth: true
                Layout.fillHeight: true
                imageSource: PathResolver.resolveAsset(CONST.ASSET_PATH.IMAGE_NUMBER_CIRCLE_ONE)
                buttonText: "Initial Zero"
            }

            //Repeat button
            CustomSwitchButton {
                Layout.fillWidth: true
                Layout.fillHeight: true
                imageSource: PathResolver.resolveAsset(CONST.ASSET_PATH.IMAGE_EXCHANGE)
                buttonText: "Repeat"
            }

        }

    }

}
