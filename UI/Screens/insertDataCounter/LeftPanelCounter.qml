import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: fileSelectionSection

    color: "transparent"
    radius: Theme.Radius.xxxlarge

    Rectangle {
        id: inputForm

        width: parent.width
        height: parent.height * 0.8
        color: Theme.Colors.white
        radius: parent.radius

        ColumnLayout {
            clip: true
            spacing: Theme.Spacing.small

            anchors {
                top: parent.top
                left: parent.left
                right: parent.right
                margins: 30
            }

            ColumnLayout {
                spacing: Theme.Spacing.xssmall

                Text {
                    text: "Starting number"
                    font.pixelSize: Theme.Typography.h2
                    font.weight: Theme.Typography.weightMedium
                    font.family: Theme.Typography.secondaryFontFamily
                    color: Theme.Colors.black
                }

                LightInput {
                    Layout.fillWidth: true
                    placeholderText: "Enter Starting Number"
                    font.pixelSize: Theme.Typography.h2
                }

            }

            ColumnLayout {
                spacing: Theme.Spacing.xssmall

                Text {
                    text: "Ending number"
                    font.pixelSize: Theme.Typography.h2
                    font.weight: Theme.Typography.weightMedium
                    font.family: Theme.Typography.secondaryFontFamily
                    color: Theme.Colors.black
                }

                LightInput {
                    Layout.fillWidth: true
                    placeholderText: "Enter Ending Number"
                    font.pixelSize: Theme.Typography.h2
                }

            }

            ColumnLayout {
                spacing: Theme.Spacing.xssmall

                RowLayout {
                    spacing: Theme.Spacing.usmall

                    ColumnLayout {
                        spacing: Theme.Spacing.xssmall
                        Layout.fillWidth: true

                        Text {
                            text: "Digit Selection"
                            font.pixelSize: Theme.Typography.h2
                            font.weight: Theme.Typography.weightMedium
                            font.family: Theme.Typography.secondaryFontFamily
                            color: Theme.Colors.black
                        }

                    }

                }

                RowLayout {
                    spacing: Theme.Spacing.usmall

                    LightInput {
                        id: inputLight

                        Layout.fillWidth: true
                        placeholderText: "Select Digit"
                        font.pixelSize: Theme.Typography.h2
                    }

                    LeftPanelButton {
                        text: "Select"
                        height: inputLight.height
                        width: 210
                        color: Theme.Colors.orange
                        onClicked: {
                            console.log("Select button clicked");
                        }
                    }

                }

            }

        }

    }

}
