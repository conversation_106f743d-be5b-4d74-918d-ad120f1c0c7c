import QtGraphicalEffects 1.14
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: printerSettingsScreen

    color: Theme.Colors.backgroundSecondary

    // Main content layout
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: Theme.Spacing.large
        spacing: Theme.Spacing.medium

        // Title
        Text {
            text: "Printer Settings"
            font: Theme.Typography.title
            color: Theme.Colors.textPrimary
            Layout.fillWidth: true
        }

        // Create new button
        ProsprButton {
            text: "Create New Configuration"
            buttonType: "primary"
            Layout.alignment: Qt.AlignRight
            onClicked: {
                // Open create/edit dialog with empty configuration
                stackLayout.currentIndex = 1;
            }
        }

        // Main content with stacked layout for list and edit views
        StackLayout {
            id: stackLayout

            Layout.fillWidth: true
            Layout.fillHeight: true
            currentIndex: 0

            // List view - index 0
            Rectangle {
                id: listViewContainer

                color: Theme.Colors.backgroundPrimary
                radius: 8
                Component.onCompleted: {
                    Theme.Shadows.applyElevation(listViewContainer, 1);
                }

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 20
                    spacing: 20

                    // Search and filter row
                    RowLayout {
                        Layout.fillWidth: true
                        spacing: 20

                        // Search field
                        Rectangle {
                            Layout.preferredWidth: 300
                            height: 40
                            border.width: 1
                            border.color: Theme.Colors.borderColor
                            radius: 4
                            color: Theme.Colors.backgroundPrimary

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 10
                                spacing: 8

                                Text {
                                    text: "🔍"
                                    font.pixelSize: Theme.Typography.bodyLarge
                                    color: Theme.Colors.textSecondary
                                }

                                TextField {
                                    Layout.fillWidth: true
                                    placeholderText: "Search configurations..."
                                    background: null
                                    selectByMouse: true
                                    color: Theme.Colors.textPrimary
                                    font: Theme.Typography.inputText
                                }

                            }

                        }

                        // Filter by printer type
                        RowLayout {
                            spacing: 10

                            Text {
                                text: "Printer Type:"
                                font: Theme.Typography.caption
                                color: Theme.Colors.textSecondary
                            }

                            ComboBox {
                                model: ["All", "Inkjet", "Thermal", "Laser"]
                                currentIndex: 0
                                Layout.preferredWidth: 150
                            }

                        }

                        // Spacer
                        Item {
                            Layout.fillWidth: true
                        }

                        // View options: grid/list
                        RowLayout {
                            spacing: 10

                            Rectangle {
                                width: 40
                                height: 40
                                radius: 4
                                color: Theme.Colors.secondary

                                Text {
                                    anchors.centerIn: parent
                                    text: "▦"
                                    font.pixelSize: 16
                                    color: Theme.Colors.white
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    cursorShape: Qt.PointingHandCursor
                                }

                            }

                            Rectangle {
                                width: 40
                                height: 40
                                radius: 4
                                color: Theme.Colors.transparent

                                Text {
                                    anchors.centerIn: parent
                                    text: "≡"
                                    font.pixelSize: 16
                                    color: Theme.Colors.textSecondary
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    cursorShape: Qt.PointingHandCursor
                                }

                            }

                        }

                    }

                    // Configuration grid
                    GridView {
                        // Shadow effect applied via Theme.Shadows.applyElevation

                        id: configGrid

                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        clip: true
                        cellWidth: 250
                        cellHeight: 220

                        model: ListModel {
                            ListElement {
                                name: "High Speed"
                                type: "Inkjet"
                                resolution: "300 DPI"
                                lastUsed: "Today"
                                isDefault: true
                            }

                            ListElement {
                                name: "High Resolution"
                                type: "Inkjet"
                                resolution: "600 DPI"
                                lastUsed: "Yesterday"
                                isDefault: false
                            }

                            ListElement {
                                name: "Barcode Optimized"
                                type: "Thermal"
                                resolution: "300 DPI"
                                lastUsed: "3 days ago"
                                isDefault: false
                            }

                            ListElement {
                                name: "Text Mode"
                                type: "Inkjet"
                                resolution: "300 DPI"
                                lastUsed: "1 week ago"
                                isDefault: false
                            }

                            ListElement {
                                name: "Economy"
                                type: "Inkjet"
                                resolution: "150 DPI"
                                lastUsed: "2 weeks ago"
                                isDefault: false
                            }

                            ListElement {
                                name: "Packaging"
                                type: "Thermal"
                                resolution: "300 DPI"
                                lastUsed: "1 month ago"
                                isDefault: false
                            }

                        }

                        delegate: Rectangle {
                            id: configCard

                            width: 230
                            height: 200
                            radius: 8
                            color: Theme.Colors.backgroundPrimary
                            border.width: 1
                            border.color: Theme.Colors.borderColor
                            Component.onCompleted: {
                                Theme.Shadows.applyElevation(configCard, 2);
                            }

                            // Default indicator badge
                            Rectangle {
                                visible: isDefault
                                width: 80
                                height: 24
                                anchors.right: parent.right
                                anchors.top: parent.top
                                color: Theme.Colors.success
                                radius: 4

                                Text {
                                    anchors.centerIn: parent
                                    text: "DEFAULT"
                                    font.pixelSize: Theme.Typography.helperText
                                    font.weight: Theme.Typography.weightMedium
                                    color: Theme.Colors.white
                                }

                            }

                            ColumnLayout {
                                anchors.fill: parent
                                anchors.margins: 15
                                spacing: 10

                                // Printer type icon
                                Rectangle {
                                    width: 50
                                    height: 50
                                    radius: 25
                                    color: Theme.Colors.white
                                    Layout.alignment: Qt.AlignHCenter

                                    Text {
                                        anchors.centerIn: parent
                                        text: type === "Inkjet" ? "💧" : (type === "Thermal" ? "🔥" : "📄")
                                        font.pixelSize: 20
                                    }

                                }

                                // Configuration name
                                Text {
                                    text: name
                                    font.pixelSize: 18
                                    font.weight: Font.Medium
                                    color: Theme.Colors.secondary
                                    Layout.alignment: Qt.AlignHCenter
                                }

                                // Configuration details
                                ColumnLayout {
                                    spacing: 5
                                    Layout.fillWidth: true

                                    RowLayout {
                                        Layout.fillWidth: true

                                        Text {
                                            text: "Type:"
                                            font.pixelSize: 14
                                            color: Theme.Colors.textSecondary
                                            Layout.preferredWidth: 80
                                        }

                                        Text {
                                            text: type
                                            font.pixelSize: 14
                                            font.weight: Font.Medium
                                            color: Theme.Colors.secondary
                                        }

                                    }

                                    RowLayout {
                                        Layout.fillWidth: true

                                        Text {
                                            text: "Resolution:"
                                            font.pixelSize: 14
                                            color: Theme.Colors.textSecondary
                                            Layout.preferredWidth: 80
                                        }

                                        Text {
                                            text: resolution
                                            font.pixelSize: 14
                                            font.weight: Font.Medium
                                            color: Theme.Colors.secondary
                                        }

                                    }

                                    RowLayout {
                                        Layout.fillWidth: true

                                        Text {
                                            text: "Last Used:"
                                            font.pixelSize: 14
                                            color: Theme.Colors.textSecondary
                                            Layout.preferredWidth: 80
                                        }

                                        Text {
                                            text: lastUsed
                                            font.pixelSize: 14
                                            color: Theme.Colors.textSecondary
                                        }

                                    }

                                }

                                // Actions
                                RowLayout {
                                    spacing: 10
                                    Layout.alignment: Qt.AlignHCenter
                                    Layout.topMargin: 5

                                    // Edit button
                                    Rectangle {
                                        width: 36
                                        height: 36
                                        radius: 18
                                        color: Theme.Colors.transparent

                                        Text {
                                            anchors.centerIn: parent
                                            text: "✏️"
                                            font.pixelSize: 16
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            cursorShape: Qt.PointingHandCursor
                                            onClicked: {
                                                // Open edit view with this configuration
                                                stackLayout.currentIndex = 1;
                                            }
                                        }

                                    }

                                    // Set default button (not shown if already default)
                                    Rectangle {
                                        visible: !isDefault
                                        width: 36
                                        height: 36
                                        radius: 18
                                        color: Theme.Colors.transparent

                                        Text {
                                            anchors.centerIn: parent
                                            text: "★"
                                            font.pixelSize: 16
                                            color: Theme.Colors.red
                                        }

                                        MouseArea {
                                            // Set as default

                                            anchors.fill: parent
                                            cursorShape: Qt.PointingHandCursor
                                            onClicked: {
                                            }
                                        }

                                    }

                                    // Delete button
                                    Rectangle {
                                        width: 36
                                        height: 36
                                        radius: 18
                                        color: Theme.Colors.transparent

                                        Text {
                                            anchors.centerIn: parent
                                            text: "🗑️"
                                            font.pixelSize: 16
                                        }

                                        MouseArea {
                                            // Confirm and delete

                                            anchors.fill: parent
                                            cursorShape: Qt.PointingHandCursor
                                            onClicked: {
                                            }
                                        }

                                    }

                                }

                            }

                            // Overall clickable behavior
                            MouseArea {
                                anchors.fill: parent
                                onClicked: {
                                    // Select this configuration
                                    stackLayout.currentIndex = 1;
                                }
                                z: -1 // Behind the buttons
                            }

                        }

                    }

                }

            }

            // Create/Edit view - index 1
            Rectangle {
                id: editViewContainer

                color: Theme.Colors.backgroundPrimary
                radius: 8
                Component.onCompleted: {
                    Theme.Shadows.applyElevation(editViewContainer, 1);
                }

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 20
                    spacing: 20

                    // Form title
                    RowLayout {
                        Layout.fillWidth: true

                        Text {
                            text: "Create New Configuration"
                            font.pixelSize: 20
                            font.weight: Font.Bold
                            color: Theme.Colors.secondary
                        }

                        // Spacer
                        Item {
                            Layout.fillWidth: true
                        }

                        // Close button
                        Rectangle {
                            width: 36
                            height: 36
                            radius: 18
                            color: Theme.Colors.transparent

                            Text {
                                anchors.centerIn: parent
                                text: "✕"
                                font.pixelSize: 16
                                color: Theme.Colors.textSecondary
                            }

                            MouseArea {
                                anchors.fill: parent
                                cursorShape: Qt.PointingHandCursor
                                onClicked: {
                                    // Return to list view
                                    stackLayout.currentIndex = 0;
                                }
                            }

                        }

                    }

                    // Form content in scrollable area
                    ScrollView {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        clip: true

                        ColumnLayout {
                            width: parent.width
                            spacing: 30

                            // Basic section
                            GroupBox {
                                title: "Basic Settings"
                                Layout.fillWidth: true

                                GridLayout {
                                    columns: 2
                                    rowSpacing: 20
                                    columnSpacing: 30
                                    anchors.fill: parent
                                    anchors.margins: 20

                                    // Configuration name
                                    Text {
                                        text: "Configuration Name:"
                                        font.pixelSize: 14
                                        color: Theme.Colors.textSecondary
                                    }

                                    ProsprTextField {
                                        text: "High Speed"
                                        Layout.preferredWidth: 250
                                    }

                                    // Printer Type
                                    Text {
                                        text: "Printer Type:"
                                        font.pixelSize: 14
                                        color: Theme.Colors.textSecondary
                                    }

                                    ComboBox {
                                        model: ["Inkjet", "Thermal", "Laser"]
                                        currentIndex: 0
                                        Layout.preferredWidth: 250
                                    }

                                    // Resolution
                                    Text {
                                        text: "Resolution:"
                                        font.pixelSize: 14
                                        color: Theme.Colors.textSecondary
                                    }

                                    ComboBox {
                                        model: ["150 DPI", "300 DPI", "600 DPI"]
                                        currentIndex: 1
                                        Layout.preferredWidth: 250
                                    }

                                    // Default configuration checkbox
                                    Text {
                                        text: "Set as Default:"
                                        font.pixelSize: 14
                                        color: Theme.Colors.textSecondary
                                    }

                                    CheckBox {
                                        checked: true
                                    }

                                }

                                background: Rectangle {
                                    color: Theme.Colors.white
                                    radius: 8
                                    border.width: 1
                                    border.color: Theme.Colors.borderColor
                                }

                                label: Text {
                                    text: parent.title
                                    font.pixelSize: 16
                                    font.weight: Font.Medium
                                    color: Theme.Colors.secondary
                                    leftPadding: 10
                                }

                            }

                            // Print Settings
                            GroupBox {
                                title: "Print Settings"
                                Layout.fillWidth: true

                                GridLayout {
                                    columns: 2
                                    rowSpacing: 20
                                    columnSpacing: 30
                                    anchors.fill: parent
                                    anchors.margins: 20

                                    // Print Speed
                                    Text {
                                        text: "Print Speed:"
                                        font.pixelSize: 14
                                        color: Theme.Colors.textSecondary
                                    }

                                    RowLayout {
                                        SpinBox {
                                            from: 1
                                            to: 100
                                            value: 70
                                            Layout.preferredWidth: 200
                                        }

                                        Text {
                                            text: "m/min"
                                            font.pixelSize: 14
                                            color: Theme.Colors.textSecondary
                                        }

                                    }

                                    // Print Delay
                                    Text {
                                        text: "Print Delay:"
                                        font.pixelSize: 14
                                        color: Theme.Colors.textSecondary
                                    }

                                    RowLayout {
                                        SpinBox {
                                            from: 0
                                            to: 1000
                                            value: 50
                                            Layout.preferredWidth: 200
                                        }

                                        Text {
                                            text: "ms"
                                            font.pixelSize: 14
                                            color: Theme.Colors.textSecondary
                                        }

                                    }

                                    // Print Direction
                                    Text {
                                        text: "Print Direction:"
                                        font.pixelSize: 14
                                        color: Theme.Colors.textSecondary
                                    }

                                    ComboBox {
                                        model: ["Left to Right", "Right to Left", "Bidirectional"]
                                        currentIndex: 0
                                        Layout.preferredWidth: 250
                                    }

                                    // Print Orientation
                                    Text {
                                        text: "Print Orientation:"
                                        font.pixelSize: 14
                                        color: Theme.Colors.textSecondary
                                    }

                                    ComboBox {
                                        model: ["Normal", "Inverted", "Mirror", "Rotate 180°"]
                                        currentIndex: 0
                                        Layout.preferredWidth: 250
                                    }

                                }

                                background: Rectangle {
                                    color: Theme.Colors.white
                                    radius: 8
                                    border.width: 1
                                    border.color: Theme.Colors.borderColor
                                }

                                label: Text {
                                    text: parent.title
                                    font.pixelSize: 16
                                    font.weight: Font.Medium
                                    color: Theme.Colors.secondary
                                    leftPadding: 10
                                }

                            }

                            // Advanced Settings
                            GroupBox {
                                title: "Advanced Settings"
                                Layout.fillWidth: true

                                GridLayout {
                                    columns: 2
                                    rowSpacing: 20
                                    columnSpacing: 30
                                    anchors.fill: parent
                                    anchors.margins: 20

                                    // Darkness/Intensity
                                    Text {
                                        text: "Print Intensity:"
                                        font.pixelSize: 14
                                        color: Theme.Colors.textSecondary
                                    }

                                    RowLayout {
                                        Slider {
                                            from: 1
                                            to: 5
                                            value: 3
                                            stepSize: 1
                                            Layout.preferredWidth: 200
                                        }

                                        Text {
                                            text: "3"
                                            font.pixelSize: 14
                                            color: Theme.Colors.textSecondary
                                        }

                                    }

                                    // Dot Size
                                    Text {
                                        text: "Dot Size:"
                                        font.pixelSize: 14
                                        color: Theme.Colors.textSecondary
                                    }

                                    ComboBox {
                                        model: ["Small", "Medium", "Large"]
                                        currentIndex: 1
                                        Layout.preferredWidth: 250
                                    }

                                    // Stroke Adjustment
                                    Text {
                                        text: "Stroke Adjustment:"
                                        font.pixelSize: 14
                                        color: Theme.Colors.textSecondary
                                    }

                                    RowLayout {
                                        SpinBox {
                                            from: -10
                                            to: 10
                                            value: 0
                                            Layout.preferredWidth: 200
                                        }

                                        Text {
                                            text: "pt"
                                            font.pixelSize: 14
                                            color: Theme.Colors.textSecondary
                                        }

                                    }

                                    // Encoding
                                    Text {
                                        text: "Character Encoding:"
                                        font.pixelSize: 14
                                        color: Theme.Colors.textSecondary
                                    }

                                    ComboBox {
                                        model: ["UTF-8", "ASCII", "ISO-8859-1"]
                                        currentIndex: 0
                                        Layout.preferredWidth: 250
                                    }

                                }

                                background: Rectangle {
                                    color: Theme.Colors.white
                                    radius: 8
                                    border.width: 1
                                    border.color: Theme.Colors.borderColor
                                }

                                label: Text {
                                    text: parent.title
                                    font.pixelSize: 16
                                    font.weight: Font.Medium
                                    color: Theme.Colors.secondary
                                    leftPadding: 10
                                }

                            }

                        }

                    }

                    // Action buttons
                    RowLayout {
                        Layout.fillWidth: true
                        Layout.topMargin: 20
                        spacing: 15

                        // Spacer
                        Item {
                            Layout.fillWidth: true
                        }

                        ProsprButton {
                            text: "Cancel"
                            buttonType: "secondary"
                            isOutlined: true
                            onClicked: stackLayout.currentIndex = 0
                        }

                        ProsprButton {
                            text: "Save Configuration"
                            buttonType: "primary"
                            onClicked: {
                                // Save configuration and return to list
                                stackLayout.currentIndex = 0;
                            }
                        }

                    }

                }

            }

        }

    }

}
