import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Theme" as Theme

// Main print layout component that organizes the three panels
Item {
    id: printLayout

    property Item selectedItem: null

    signal navigateToRequested(string to, string from)
    signal deletePopupRequested()

    // Main content area with shadow and rounded corners
    Rectangle {
        id: mainContent

        anchors.fill: parent
        anchors.margins: 20
        color: "transparent" // Use transparent since parent already has gradient
        radius: Theme.Radius.large

        // Main layout with three panels
        Item {
            anchors.fill: parent

            // Top Panel - Print Preview
            TopPanelUSB {
                id: topPanel

                height: parent.height * 0.3
                onItemSelected: printLayout.selectedItem = item

                anchors {
                    top: parent.top
                    left: parent.left
                    right: parent.right
                }

            }

            // Left Panel - File Selection
            LeftPanelUSB {
                id: leftPanel

                width: parent.width * 0.6 - 10

                anchors {
                    top: topPanel.bottom
                    left: parent.left
                    bottom: parent.bottom
                    topMargin: 20
                }

                onDeleteButtonClicked: {
                    printLayout.deletePopupRequested()
                }

                onClearButtonClicked: {
                    topPanel.clearContent()
                }
            }

            // Forward navigateToRequested signal from LeftPanel
            Connections {
                function onNavigateToRequested(to, from) {
                    printScreen.navigateToRequested(to, from);
                }

                target: leftPanel
            }

            // Connections to handle data passing between panels
            Connections {
                function onFileSelected(fileName, lotNum, bestByDate) {
                    topPanel.updatePreview(fileName, lotNum, bestByDate);
                }

                target: leftPanel
            }

        }

    }

}
