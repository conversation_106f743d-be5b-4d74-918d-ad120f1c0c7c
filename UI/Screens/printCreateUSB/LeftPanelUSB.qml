import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Theme" as Theme

// File Selection Panel
Rectangle {
    id: fileSelectionSection

    // --- API: Signals for navigation/actions ---
    signal navigateToRequested(string to, string from)
    signal deleteButtonClicked()
    // Signal to notify when a file is selected
    signal fileSelected(string fileName, string lotNum, string bestByDate)
    signal clearButtonClicked()

    radius: Theme.Radius.xxxlarge
    color: "transparent"
    anchors.horizontalCenter: parent.horizontalCenter

    Rectangle {
        id: contentContainerRect

        width: uploadInputRect.width + browseButton.width + uploadRow.spacing + (contentColumn.leftPadding * 2)
        height: 270
        color: Theme.Colors.white
        radius: Theme.Radius.xxxlarge

        Column {
            id: contentColumn

            leftPadding: 30
            topPadding: 30
            spacing: Theme.Spacing.small
            width: parent.width
            height: parent.height

            Text {
                text: "Upload"
                font.pixelSize: 28
            }

            Row {
                id: uploadRow

                spacing: Theme.Spacing.large

                Rectangle {
                    id: uploadInputRect

                    width: 500
                    height: 74
                    border.color: Theme.Colors.borderColor
                    border.width: 1
                    color: Theme.Colors.white
                    radius: Theme.Radius.xmlarge

                    Text {
                        text: "Upload Here"
                        font.pixelSize: 28
                        color: Theme.Colors.textGray

                        anchors {
                            left: parent.left
                            verticalCenter: parent.verticalCenter
                            margins: 20
                        }

                    }

                }

                LeftPanelButton {
                    id: browseButton

                    color: Theme.Colors.orange
                    width: 210
                    height: 74
                    text: "BROWSE"
                }

            }

            Row {
                spacing: Theme.Spacing.large

                Text {
                    width: uploadInputRect.width
                    text: "*Only accepts .BMP files (dot matrix)"
                    font.pixelSize: 24
                    color: Theme.Colors.statusMidGrey
                }

                DefaultButton {
                    id: clearButton

                    width: 210
                    height: 74

                    text: "CLEAR"
                    font.weight: Font.Bold

                    onClicked: {
                        fileSelectionSection.clearButtonClicked()
                    }
                }
            }


        }

    }

}
