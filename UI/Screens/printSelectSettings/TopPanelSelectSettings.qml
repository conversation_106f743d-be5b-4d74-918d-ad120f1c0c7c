import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

Item {
    id: root

    Item {
        id: contentArea

        anchors.fill: parent

        Rectangle {
            id: printPreviewSection

            anchors.centerIn: parent
            height: parent.height * 0.95
            width: parent.width * 0.6
            color: Theme.Colors.white
            radius: Theme.Radius.xmlarge

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: Theme.Spacing.usmall

                Text {
                    text: "Select a Setting"
                    font.pixelSize: 46
                    font.family: "Georgia"
                    font.bold: true
                    color: Theme.Colors.black
                    horizontalAlignment: Text.AlignHCenter
                    Layout.alignment: Qt.AlignHCenter
                }

                ListView {
                    id: settingsListView

                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    clip: true

                    model: ListModel {
                        id: settingsModel

                        ListElement {
                            name: "Default Setting"
                            active: false
                        }

                        ListElement {
                            name: "Test Setting 2"
                            active: false
                        }

                        ListElement {
                            name: "Test Setting 3"
                            active: true
                        }

                        ListElement {
                            name: "Test Setting 4"
                            active: false
                        }

                        ListElement {
                            name: "Test Setting 5"
                            active: false
                        }

                        ListElement {
                            name: "Test Setting 6"
                            active: false
                        }

                        ListElement {
                            name: "Test Setting 7"
                            active: false
                        }

                        ListElement {
                            name: "Test Setting 8"
                            active: false
                        }

                        ListElement {
                            name: "Test Setting 9"
                            active: false
                        }

                        ListElement {
                            name: "Test Setting 10"
                            active: false
                        }

                        ListElement {
                            name: "Test Setting 11"
                            active: false
                        }

                        ListElement {
                            name: "Test Setting 12"
                            active: false
                        }

                        ListElement {
                            name: "Test Setting 13"
                            active: false
                        }

                        ListElement {
                            name: "Test Setting 14"
                            active: false
                        }

                    }

                    delegate: Rectangle {
                        width: settingsListView.width
                        height: 70
                        color: ListView.isCurrentItem ? Theme.Colors.addOpacity(Theme.Colors.primary, 0.2) : Theme.Colors.transparent

                        Row {
                            anchors.fill: parent
                            anchors.margins: 20
                            spacing: Theme.Spacing.xssmall

                            // Radio circle (only for active)
                            Rectangle {
                                id: radioCircle

                                width: 28
                                height: 28
                                radius: Theme.Radius.xlarge
                                visible: active
                                border.color: Theme.Colors.black
                                border.width: 1
                                color: Theme.Colors.black

                                Rectangle {
                                    width: 24
                                    height: 24
                                    radius: Theme.Radius.xlarge
                                    color: Theme.Colors.white
                                    anchors.centerIn: parent

                                    Rectangle {
                                        width: 20
                                        height: 20
                                        radius: Theme.Radius.large
                                        color: Theme.Colors.black
                                        anchors.centerIn: parent
                                    }

                                }

                            }

                            Text {
                                anchors.verticalCenter: parent.verticalCenter
                                text: name
                                font.pixelSize: 28
                                color: Theme.Colors.black
                                verticalAlignment: Text.AlignVCenter
                                elide: Text.ElideRight
                                Layout.fillWidth: true
                            }

                            // Badge (only for active)
                            Rectangle {
                                visible: active
                                radius: Theme.Radius.xmlarge
                                color: Theme.Colors.orange
                                height: 42
                                width: 244
                                anchors.verticalCenter: parent.verticalCenter

                                Text {
                                    text: "Currently In Use"
                                    color: "white"
                                    font.pixelSize: 20
                                    anchors.centerIn: parent
                                }

                            }

                        }

                        // Bottom separator
                        Rectangle {
                            anchors.left: parent.left
                            anchors.right: parent.right
                            anchors.bottom: parent.bottom
                            height: 1
                            color: Theme.Colors.lightGrey
                            visible: index < settingsListView.count - 1
                        }

                        // Click handling
                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                settingsListView.currentIndex = index;
                            }
                        }

                    }

                    ScrollBar.vertical: ScrollBar {
                        policy: ScrollBar.AsNeeded
                    }

                }

            }

        }

    }

    BlurOverlay {
        anchors.fill: contentArea
        source: contentArea
        visible: deletePopup.visible
    }

    QPopup {
        id: deletePopup

        property int settingIndex: settingsListView.currentIndex

        confirmationText: settingIndex >= 0 ? `Are you sure you want to delete ${settingsListView.model.get(settingIndex).name}?` : ""
        button1Text: "DELETE"
        button2Text: "CANCEL"
        button2Color: Theme.Colors.black
        numberOfButtons: 2
        button1ImageSource: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_TRASH)
        button2ImageSource: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_FORBIDDEN)
        onButton1Pressed: {
            console.log("Delete confirmed");
            if (settingIndex >= 0)
                settingsListView.model.remove(settingIndex, 1);

            deletePopup.close(); //Close the popup
        }
        onButton2Pressed: {
            console.log("Delete canceled");
            deletePopup.close(); // Close the popup
        }
        onOpened: {
            mainLayout.popupVisible = true;
        }
        onClosed: {
            mainLayout.popupVisible = false;
        }
    }

    Connections {
        function onAction(action) {
            if (action === "delete") {
                deletePopup.open();
            } else if (action === "select") {
                if (settingsListView.currentItem) {
                    // Reset 'active' for all items
                    for (var i = 0; i < settingsModel.count; ++i) {
                        settingsModel.setProperty(i, "active", false);
                    }
                    // Set 'active' for the selected item
                    settingsModel.setProperty(settingsListView.currentIndex, "active", true);
                }
            }
        }

        // TODO this is only for demo, the direct connection to a separated outside component is bad, should be reworked
        target: bottomBar
    }

}
