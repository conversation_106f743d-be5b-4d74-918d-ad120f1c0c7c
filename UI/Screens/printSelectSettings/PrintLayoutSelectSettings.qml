import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Theme" as Theme

// Main print layout component that organizes the three panels
Item {
    id: printLayout

    signal navigateToRequested(string to, string from)

    Item {
        id: mainContent // I don't know if it's used anywhere else so I left it here

        anchors.fill: parent

        // Main layout with three panels
        Item {
            anchors.fill: parent

            // Top Panel - Print Preview
            TopPanelSelectSettings {
                id: topPanel

                // we have to fill all over the content area to be able to blur everything
                anchors.fill: parent
            }

        }

    }

}
