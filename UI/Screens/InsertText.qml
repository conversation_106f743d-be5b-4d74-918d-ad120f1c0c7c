import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: insertTextScreen

    color: Theme.Colors.backgroundSecondary // Light gray background

    // Main content layout
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 30
        spacing: 25

        // Title
        Text {
            text: "Insert Text"
            font.pixelSize: 24
            font.weight: Font.Bold
            color: Theme.Colors.secondary
            Layout.fillWidth: true
        }

        // Main content with two panels
        RowLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true
            spacing: 30

            // Left panel - Text settings
            Rectangle {
                Layout.preferredWidth: 400
                Layout.fillHeight: true
                color: Theme.Colors.white
                radius: 8

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 20
                    spacing: 20

                    // Text settings section
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 15

                        Text {
                            text: "Text Settings"
                            font.pixelSize: 18
                            font.weight: Font.Medium
                            color: Theme.Colors.secondary
                        }

                        // Text input
                        ColumnLayout {
                            Layout.fillWidth: true
                            spacing: 8

                            Text {
                                text: "Text Content"
                                font.pixelSize: 14
                                color: Theme.Colors.textSecondary
                            }

                            Rectangle {
                                Layout.fillWidth: true
                                height: 100
                                border.width: 1
                                border.color: Theme.Colors.borderColor
                                radius: 4

                                TextArea {
                                    id: textContentInput

                                    anchors.fill: parent
                                    anchors.margins: 10
                                    placeholderText: "Enter text here..."
                                    text: "Sample Product Label"
                                    wrapMode: TextEdit.Wrap
                                    font.pixelSize: 14
                                }

                            }

                        }

                        // Font settings
                        GridLayout {
                            Layout.fillWidth: true
                            columns: 2
                            rowSpacing: 15
                            columnSpacing: 15

                            // Font family
                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 8

                                Text {
                                    text: "Font Family"
                                    font.pixelSize: 14
                                    color: Theme.Colors.textSecondary
                                }

                                ComboBox {
                                    model: ["Arial", "Helvetica", "Roboto", "Times New Roman", "Courier New"]
                                    currentIndex: 0
                                    Layout.fillWidth: true
                                }

                            }

                            // Font size
                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 8

                                Text {
                                    text: "Font Size"
                                    font.pixelSize: 14
                                    color: Theme.Colors.textSecondary
                                }

                                SpinBox {
                                    from: 6
                                    to: 72
                                    value: 12
                                    Layout.fillWidth: true
                                }

                            }

                            // Font style
                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 8

                                Text {
                                    text: "Font Style"
                                    font.pixelSize: 14
                                    color: Theme.Colors.textSecondary
                                }

                                ComboBox {
                                    model: ["Regular", "Bold", "Italic", "Bold Italic"]
                                    currentIndex: 0
                                    Layout.fillWidth: true
                                }

                            }

                            // Text alignment
                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 8

                                Text {
                                    text: "Alignment"
                                    font.pixelSize: 14
                                    color: Theme.Colors.textSecondary
                                }

                                RowLayout {
                                    Layout.fillWidth: true
                                    spacing: 0

                                    // Left alignment
                                    Rectangle {
                                        Layout.fillWidth: true
                                        height: 40
                                        color: Theme.Colors.transparent
                                        border.width: 1
                                        border.color: Theme.Colors.borderColor

                                        Text {
                                            anchors.centerIn: parent
                                            text: "⫷"
                                            font.pixelSize: 16
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            cursorShape: Qt.PointingHandCursor
                                        }

                                    }

                                    // Center alignment
                                    Rectangle {
                                        Layout.fillWidth: true
                                        height: 40
                                        color: Theme.Colors.secondary
                                        border.width: 1
                                        border.color: Theme.Colors.secondary

                                        Text {
                                            anchors.centerIn: parent
                                            text: "≡"
                                            font.pixelSize: 16
                                            color: Theme.Colors.white
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            cursorShape: Qt.PointingHandCursor
                                        }

                                    }

                                    // Right alignment
                                    Rectangle {
                                        Layout.fillWidth: true
                                        height: 40
                                        color: Theme.Colors.transparent
                                        border.width: 1
                                        border.color: Theme.Colors.borderColor

                                        Text {
                                            anchors.centerIn: parent
                                            text: "⫸"
                                            font.pixelSize: 16
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            cursorShape: Qt.PointingHandCursor
                                        }

                                    }

                                }

                            }

                        }

                    }

                    // Divider
                    Rectangle {
                        Layout.fillWidth: true
                        height: 1
                        color: Theme.Colors.borderColor
                    }

                    // Advanced settings
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 15

                        Text {
                            text: "Advanced Settings"
                            font.pixelSize: 18
                            font.weight: Font.Medium
                            color: Theme.Colors.secondary
                        }

                        // Text rotation
                        ColumnLayout {
                            Layout.fillWidth: true
                            spacing: 8

                            Text {
                                text: "Rotation (degrees)"
                                font.pixelSize: 14
                                color: Theme.Colors.textSecondary
                            }

                            Slider {
                                id: rotationSlider

                                from: 0
                                to: 360
                                value: 0
                                stepSize: 5
                                Layout.fillWidth: true

                                Text {
                                    anchors.bottom: parent.top
                                    anchors.bottomMargin: 5
                                    anchors.horizontalCenter: parent.horizontalCenter
                                    text: Math.round(parent.value) + "°"
                                    font.pixelSize: 12
                                    color: Theme.Colors.textSecondary
                                }

                            }

                        }

                        // Fixed width settings
                        RowLayout {
                            Layout.fillWidth: true
                            spacing: 15

                            CheckBox {
                                id: fixedWidthCheckbox

                                text: "Fixed Width"
                                checked: false
                            }

                            SpinBox {
                                from: 10
                                to: 500
                                value: 100
                                enabled: fixedWidthCheckbox.checked
                                Layout.fillWidth: true
                                textFromValue: function(value) {
                                    return value + " px";
                                }
                            }

                        }

                    }

                    // Spacer
                    Item {
                        Layout.fillHeight: true
                    }

                    // Action buttons
                    RowLayout {
                        Layout.fillWidth: true
                        spacing: 15

                        // Spacer
                        Item {
                            Layout.fillWidth: true
                        }

                        // Cancel button
                        ProsprButton {
                            text: "Cancel"
                            buttonType: "secondary"
                            isOutlined: true
                            onClicked: NavigationManager.go("CreateEditMessage")
                        }

                        // Insert button
                        ProsprButton {
                            text: "Insert"
                            buttonType: "primary"
                            onClicked: NavigationManager.go("CreateEditMessage")
                        }

                    }

                }

            }

            // Right panel - Preview
            Rectangle {
                Layout.fillWidth: true
                Layout.fillHeight: true
                color: Theme.Colors.white
                radius: 8

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 20
                    spacing: 20

                    // Preview title
                    Text {
                        text: "Preview"
                        font.pixelSize: 18
                        font.weight: Font.Medium
                        color: Theme.Colors.secondary
                    }

                    // Preview canvas
                    Rectangle {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: Theme.Colors.white
                        border.width: 1
                        border.color: Theme.Colors.borderColor

                        // Text preview with applied styles
                        Rectangle {
                            anchors.centerIn: parent
                            width: 300
                            height: 100
                            color: Theme.Colors.white
                            border.width: 1
                            border.color: Theme.Colors.borderColor

                            // Text with applied styles
                            Text {
                                anchors.centerIn: parent
                                text: textContentInput.text
                                font.pixelSize: 20
                                font.family: "Arial"
                                rotation: rotationSlider.value
                            }

                        }

                    }

                }

            }

        }

    }

}
