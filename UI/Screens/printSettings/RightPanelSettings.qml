import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: printControlsSection

    height: parent.height
    width: parent.width * 0.7
    radius: Theme.Radius.xxxlarge
    color: "transparent"

    Rectangle {
        anchors.centerIn: parent
        height: parent.height
        width: parent.width * 0.9
        radius: Theme.Radius.xxxlarge
        color: Theme.Colors.white

        GridLayout {
            anchors.fill: parent
            rows: 2
            columns: 2
            rowSpacing: 30
            columnSpacing: 30
            anchors.margins: 30

            // Reverse Button
            CardSwitch {
                Layout.fillWidth: true
                Layout.fillHeight: true
                icon.source: PathResolver.resolveAsset(CONST.ASSET_PATH.IMAGE_RELOAD)
                text: "Reverse"
            }

            //Repeat button
            CardSwitch {
                Layout.fillWidth: true
                Layout.fillHeight: true
                icon.source: PathResolver.resolveAsset(CONST.ASSET_PATH.IMAGE_EXCHANGE)
                text: "Repeat"
            }

            //Invert button
            CardSwitch {
                Layout.fillWidth: true
                Layout.fillHeight: true
                icon.source: PathResolver.resolveAsset(CONST.ASSET_PATH.IMAGE_HALF)
                text: "Invert"
            }

            //Bold button
            CardSwitch {
                Layout.fillWidth: true
                Layout.fillHeight: true
                icon.source: PathResolver.resolveAsset(CONST.ASSET_PATH.IMAGE_BOLD)
                text: "Bold"
            }

        }

    }

}
