import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: fileSelectionSection

    signal testButtonClicked()

    color: "transparent"
    radius: Theme.Radius.xxxlarge

    Rectangle {
        id: inputForm

        width: parent.width
        height: parent.height * 0.8
        color: Theme.Colors.white
        radius: parent.radius

        ColumnLayout {
            clip: true
            spacing: Theme.Spacing.small

            anchors {
                top: parent.top
                left: parent.left
                right: parent.right
                margins: 30
            }

            ColumnLayout {
                spacing: Theme.Spacing.xssmall

                Text {
                    text: "Printer Setting Name"
                    font.pixelSize: Theme.Typography.h2
                    font.weight: Theme.Typography.weightMedium
                    font.family: Theme.Typography.secondaryFontFamily
                    color: Theme.Colors.black
                }

                LightInput {
                    Layout.fillWidth: true
                    placeholderText: "Default Setting #1"
                    font.pixelSize: Theme.Typography.h2
                }

            }

            ColumnLayout {
                spacing: Theme.Spacing.xssmall

                RowLayout {
                    spacing: Theme.Spacing.usmall

                    ColumnLayout {
                        spacing: Theme.Spacing.xssmall
                        Layout.fillWidth: true

                        Text {
                            text: "Delay (MM)"
                            font.pixelSize: Theme.Typography.h2
                            font.weight: Theme.Typography.weightMedium
                            font.family: Theme.Typography.secondaryFontFamily
                            color: Theme.Colors.black
                        }

                        LightInput {
                            Layout.preferredWidth: inputForm.width / 2
                            placeholderText: "100"
                            font.pixelSize: Theme.Typography.h2

                            validator: IntValidator {
                                bottom: 0
                            }

                        }

                    }

                    ColumnLayout {
                        spacing: Theme.Spacing.xssmall
                        Layout.fillWidth: true

                        Text {
                            text: "Speed"
                            font.pixelSize: Theme.Typography.h2
                            font.weight: Theme.Typography.weightMedium
                            font.family: Theme.Typography.secondaryFontFamily
                            color: Theme.Colors.black
                        }

                        LightInput {
                            Layout.fillWidth: true
                            placeholderText: "100"
                            font.pixelSize: Theme.Typography.h2

                            validator: IntValidator {
                                bottom: 0
                            }

                        }

                    }

                }

            }

            ColumnLayout {
                id: sensorSelection

                property var model

                function updateText() {
                    var activeSensors = [];
                    for (var i = 0; i < sensorSelection.model.count; ++i) {
                        if (sensorSelection.model.get(i).active)
                            activeSensors.push(sensorSelection.model.get(i).name);

                    }
                    sensorTextInput.text = activeSensors.length > 0 ? activeSensors.join(", ") : "None";
                }

                spacing: Theme.Spacing.xssmall
                Component.onCompleted: {
                    updateText();
                }

                Text {
                    text: "Sensor"
                    font.pixelSize: Theme.Typography.h2
                    font.weight: Theme.Typography.weightMedium
                    font.family: Theme.Typography.secondaryFontFamily
                    color: Theme.Colors.black
                }

                LightInput {
                    id: sensorTextInput

                    property bool showOptions: false

                    Layout.fillWidth: true
                    placeholderText: "Select Sensor"
                    font.pixelSize: Theme.Typography.h2
                    color: Theme.Colors.textGrey
                    readOnly: true

                    Image {
                        rotation: sensorTextInput.showOptions ? 180 : 0
                        source: PathResolver.resolveAsset("Images/dropdown-arrow.png")

                        anchors {
                            right: parent.right
                            rightMargin: 10
                            verticalCenter: parent.verticalCenter
                        }

                        Text {
                            anchors.centerIn: parent
                            visible: parent.status !== Image.Ready
                            text: "▼"
                            font.pixelSize: Theme.Typography.h4
                            color: Theme.Colors.borderColor
                        }

                    }

                    MouseArea {
                        anchors.fill: parent
                        cursorShape: Qt.PointingHandCursor
                        onClicked: {
                            sensorTextInput.showOptions = !sensorTextInput.showOptions;
                        }
                    }

                }

                model: ListModel {
                    ListElement {
                        name: 'Sensor'
                        active: false
                    }

                    ListElement {
                        name: 'Sensor + Encoder'
                        active: false
                    }

                    ListElement {
                        name: 'Continuous'
                        active: true
                    }

                    ListElement {
                        name: 'Encoder'
                        active: false
                    }

                }

            }

        }

    }

    Loader {
        id: sensorDialogLoader

        active: sensorTextInput.showOptions
        sourceComponent: sensorDialog
        onLoaded: {
            y = parent.mapFromItem(sensorTextInput, 0, 0).y - height - 10;
        }

        anchors {
            left: inputForm.left
            right: inputForm.right
            leftMargin: 30
            rightMargin: 30
        }

    }

    Component {
        id: sensorDialog

        Rectangle {
            property alias model: listViewSensors.model

            height: listViewSensors.height
            color: Theme.Colors.inputBackground
            border.width: 1
            border.color: Theme.Colors.borderColor
            radius: Theme.Radius.item / 2

            ButtonGroup { id: btnGroup }

            ListView {
                id: listViewSensors

                height: Math.min(contentHeight, 400)
                clip: true
                interactive: false
                model: sensorSelection.model

                anchors {
                    left: parent.left
                    right: parent.right
                    leftMargin: 20
                    rightMargin: 20
                }

                delegate: CheckDelegate {
                    id: control

                    anchors.left: parent.left
                    anchors.right: parent.right
                    height: 100
                    checked: active
                    font.family: Theme.Typography.secondaryFontFamily
                    text: name

                    onCheckedChanged: {
                        listViewSensors.model.setProperty(index, "active", checked);
                        sensorSelection.updateText();
                    }

                    Rectangle {
                        width: parent.width
                        height: 1
                        color: Theme.Colors.borderColor
                        anchors.bottom: parent.bottom
                    }

                    contentItem: Text {
                        text: control.text
                        font.pixelSize: Theme.Typography.h2
                        font.family: Theme.Typography.secondaryFontFamily
                        color: active ? Theme.Colors.orange : Theme.Colors.black
                        opacity: enabled ? 1 : 0.3
                        verticalAlignment: Text.AlignVCenter
                    }

                    indicator: CheckBoxControl {
                        anchors.right: parent.right
                        anchors.verticalCenter: parent.verticalCenter
                        checked: control.checked
                        enabled: false
                    }

                    background: Item {
                    }

                    Component.onCompleted: {
                        btnGroup.addButton(control)
                    }
                }

            }

        }

    }

    LeftPanelButton {
        text: "TEST"
        width: parent.width
        color: Theme.Colors.orange
        onClicked: {
            console.log("TEST button clicked");
            console.log("Selected Sensor(s):", sensorTextInput.text);
            testButtonClicked();
        }

        anchors {
            top: inputForm.bottom
            margins: 20
        }

    }

}
