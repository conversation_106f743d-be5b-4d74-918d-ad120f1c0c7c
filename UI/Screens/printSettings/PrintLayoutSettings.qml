import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

// Main print layout component that organizes the three panels
Item {
    id: printLayout

    property bool testPopupVisible: false

    signal navigateToRequested(string to, string from)

    // Main content area with shadow and rounded corners
    Rectangle {
        id: mainContent

        anchors.fill: parent
        anchors.margins: 20
        color: "transparent" // Use transparent since parent already has gradient
        radius: Theme.Radius.large

        // Main layout with three panels
        Item {
            anchors.fill: parent

            // Top Panel - Print Preview
            TopPanelSettings {
                id: topPanel

                height: parent.height * 0.3

                anchors {
                    top: parent.top
                    left: parent.left
                    right: parent.right
                }

            }

            // Left Panel - File Selection
            LeftPanelSettings {
                id: leftPanel

                width: parent.width * 0.6 - 10
                onTestButtonClicked: {
                    mainLayout.popupVisible = true;
                    testPopupVisible = true;
                }

                anchors {
                    top: topPanel.bottom
                    left: parent.left
                    bottom: parent.bottom
                    topMargin: 20
                }

            }

            // Right Panel - Print Controls and Status
            RightPanelSettings {
                id: rightPanel

                anchors {
                    top: topPanel.bottom
                    left: leftPanel.right
                    right: parent.right
                    bottom: parent.bottom
                    topMargin: 20
                    leftMargin: 20
                }

            }

        }

    }

    Connections {
        function onSaveChangesPopupRequested() {
            savePopup.open();
            mainLayout.popupVisible = true;
        }

        target: bottomBar
    }

    // Use the new BlurOverlay component
    BlurOverlay {
        anchors.fill: parent
        source: mainContent
        visible: mainLayout.popupVisible
    }

    QPopup {
        id: savePopup

        confirmationText: "Are you sure you want to back from edit?"
        button1Text: "SAVE"
        button2Text: "DISCARD"
        button3Text: "BACK"
        button1Color: Theme.Colors.black
        button2Color: Theme.Colors.red
        numberOfButtons: 3
        button1ImageSource: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_SAVE_CHANGES)
        button2ImageSource: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_FORBIDDEN)
        button3ImageSource: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_BACK)
        onButton1Pressed: {
            console.log("Save confirmed");
            savePopup.close(); //Close the popup
            mainLayout.popupVisible = false;
            navigateToRequested("Print", "PrintSettings");
        }
        onButton2Pressed: {
            console.log("Changes canceled");
            savePopup.close(); // Close the popup
            mainLayout.popupVisible = false;
            navigateToRequested("Print", "PrintSettings");
        }
        onButton3Pressed: {
            console.log("Changes canceled");
            savePopup.close(); // Close the popup
            mainLayout.popupVisible = false;
        }
    }

    TestPrintMode {
        id: testPopup

        visible: testPopupVisible
        onClosePopup: {
            mainLayout.popupVisible = false;
            testPopupVisible = false;
        }
    }

}
