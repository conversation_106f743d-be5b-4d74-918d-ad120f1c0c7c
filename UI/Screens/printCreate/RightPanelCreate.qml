import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Theme" as Theme

// Print Controls and Status Panel
Item {
    id: rightPanel

    // Properties to expose print settings to other components
    property int copies: 1
    property int startAt: 1
    property int endAt: 100

    // Signals
    signal printRequested()
    signal settingsChanged()
    signal leftButtonClicked()
    signal rightButtonClicked()
    signal upButtonClicked()
    signal downButtonClicked()
    signal testButtonClicked()

    // Print Controls Section (upper part)
    Rectangle {
        id: printControlsSection

        height: parent.height
        radius: Theme.Radius.xxxlarge
        color: Theme.Colors.white

        anchors {
            top: parent.top
            left: parent.left
            right: parent.right
        }

        // Test button
        Rectangle {
            id: testButton

            width: parent.width * 0.7
            height: parent.height * 0.16
            radius: 20
            color: Theme.Colors.orange
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.bottom: parent.bottom
            anchors.bottomMargin: 30

            Text {
                text: "TEST"
                color: Theme.Colors.white
                font.pixelSize: 30
                font.weight: Font.Bold
                anchors.centerIn: parent
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    // Handle delete action
                    console.log("TEST button clicked");
                    testButtonClicked();
                }
            }

        }

        Rectangle {
            id: upButton

            width: parent.width * 0.16
            height: parent.width * 0.16
            radius: Theme.Radius.xmlarge
            color: Theme.Colors.secondary
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.top
            anchors.topMargin: 30

            Image {
                // Keep the original aspect ratio by adjusting one dimension based on the parent size
                // Calculate the aspect ratio (width / height)
                property real aspectRatio: implicitWidth / implicitHeight

                source: "qrc:/UI/Assets/Images/arrow.png"
                anchors.centerIn: parent
                // Set width to a percentage of parent width (e.g., 40%) and calculate height to preserve aspect ratio
                width: parent.width * 0.4
                height: width / aspectRatio // Adjust height based on width and aspect ratio
            }

            MouseArea {
                anchors.fill: parent
                onClicked: rightPanel.upButtonClicked()
            }

        }

        Rectangle {
            id: leftButton

            width: parent.width * 0.16
            height: parent.width * 0.16
            radius: Theme.Radius.xmlarge
            rotation: -90
            color: Theme.Colors.secondary
            anchors.top: upButton.bottom
            anchors.topMargin: 30
            anchors.right: upButton.left
            anchors.rightMargin: 30

            Image {
                // Keep the original aspect ratio by adjusting one dimension based on the parent size
                // Calculate the aspect ratio (width / height)
                property real aspectRatio: implicitWidth / implicitHeight

                source: "qrc:/UI/Assets/Images/arrow.png"
                anchors.centerIn: parent
                // Set width to a percentage of parent width (e.g., 40%) and calculate height to preserve aspect ratio
                width: parent.width * 0.4
                height: width / aspectRatio // Adjust height based on width and aspect ratio
            }

            MouseArea {
                anchors.fill: parent
                onClicked: rightPanel.leftButtonClicked()
            }

        }

        Rectangle {
            id: rightButton

            width: parent.width * 0.16
            height: parent.width * 0.16
            radius: Theme.Radius.xmlarge
            rotation: 90
            color: Theme.Colors.secondary
            anchors.top: upButton.bottom
            anchors.topMargin: 30
            anchors.left: upButton.right
            anchors.leftMargin: 30

            Image {
                // Keep the original aspect ratio by adjusting one dimension based on the parent size
                // Calculate the aspect ratio (width / height)
                property real aspectRatio: implicitWidth / implicitHeight

                source: "qrc:/UI/Assets/Images/arrow.png"
                anchors.centerIn: parent
                // Set width to a percentage of parent width (e.g., 40%) and calculate height to preserve aspect ratio
                width: parent.width * 0.4
                height: width / aspectRatio // Adjust height based on width and aspect ratio
            }

            MouseArea {
                anchors.fill: parent
                onClicked: rightPanel.rightButtonClicked()
            }

        }

        Rectangle {
            id: bottomButton

            width: parent.width * 0.16
            height: parent.width * 0.16
            radius: Theme.Radius.xmlarge
            color: Theme.Colors.secondary
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: leftButton.bottom
            anchors.topMargin: 30

            Image {
                // Keep the original aspect ratio by adjusting one dimension based on the parent size
                // Calculate the aspect ratio (width / height)
                property real aspectRatio: implicitWidth / implicitHeight

                source: "qrc:/UI/Assets/Images/arrow.png"
                anchors.centerIn: parent
                rotation: 180
                // Set width to a percentage of parent width (e.g., 40%) and calculate height to preserve aspect ratio
                width: parent.width * 0.4
                height: width / aspectRatio // Adjust height based on width and aspect ratio
            }

            MouseArea {
                anchors.fill: parent
                onClicked: rightPanel.downButtonClicked()
            }

        }

    }

}
