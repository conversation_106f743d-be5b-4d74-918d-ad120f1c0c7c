import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Theme" as Theme

// Main print layout component that organizes the three panels
Item {
    id: printLayout

    property Item selectedItem: null

    signal navigateToRequested(string to, string from)
    signal deletePopupRequested()
    signal backPopupRequested()
    signal testButtonCreateClicked()

    // Main content area with shadow and rounded corners
    Rectangle {
        id: mainContent

        anchors.fill: parent
        anchors.margins: 20
        color: "transparent" // Use transparent since parent already has gradient
        radius: Theme.Radius.large

        // Main layout with three panels
        Item {
            anchors.fill: parent

            PrintPreviewPanel {
                id: topPanel

                anchors {
                    top: parent.top
                    left: parent.left
                    right: parent.right
                }

                editMode: true
                height: parent.height * 0.3
                showGridLines: leftPanel.showGridLines

                onItemSelected: printLayout.selectedItem = item
            }

            // Left Panel - File Selection
            LeftPanelCreate {
                id: leftPanel

                width: parent.width * 0.6 - 10
                onSelectButtonClicked: {
                    console.log("CLICKED!!!!!!!!!!!!!!!!!!!!!!!");
                    navigateToRequested("printSelectSettings/PrintLayoutSelectSettings", "PrintLayoutCreate");
                }

                anchors {
                    top: topPanel.bottom
                    left: parent.left
                    bottom: parent.bottom
                    topMargin: 20
                }

                onEditItem: { topPanel.editSelectedItem() }
                onCopyItem: { topPanel.copySelectedItem() }
                onDeleteItem: { topPanel.deleteSelectedItem() }
            }

            // Right Panel - Print Controls and Status
            RightPanelCreate {
                id: rightPanel

                onTestButtonClicked: testButtonCreateClicked()
                onLeftButtonClicked: {
                    topPanel.moveSelectedItemLeft();
                }
                onRightButtonClicked: {
                    topPanel.moveSelectedItemRight();
                }
                onUpButtonClicked: {
                    topPanel.moveSelectedItemUp();
                }
                onDownButtonClicked: {
                    topPanel.moveSelectedItemDown();
                }

                anchors {
                    top: topPanel.bottom
                    left: leftPanel.right
                    right: parent.right
                    bottom: parent.bottom
                    topMargin: 20
                    leftMargin: 20
                }

            }

            // Forward navigateToRequested signal from LeftPanel
            Connections {
                function onNavigateToRequested(to, from) {
                    printScreen.navigateToRequested(to, from);
                }

                target: leftPanel
            }

        }

    }

}
