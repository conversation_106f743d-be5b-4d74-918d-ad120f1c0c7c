import Backend.UserManager 1.0
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Theme" as Theme
import "qrc:/UI/Core/constants.js" as CONST

Item {
    id: serviceScreen

    layer.enabled: true

    GridLayout {
        anchors.centerIn: parent
        columns: 2
        columnSpacing: 30
        rowSpacing: 30

        CardButton {
            Layout.fillWidth: true
            iconSource: PathResolver.resolveAsset("Images/phase.png")
            text: "Phase"
            onClicked: NavigationManager.go(CONST.SCREEN.SERVICE_PHASE, CONST.SCREEN.SERVICE)
            // TODO permissions check can be imporoved
            visible: UserManager.testPermission("Configuration")
        }

        CardButton {
            Layout.fillWidth: true
            iconSource: PathResolver.resolveAsset("Images/function.png")
            text: "Function"
            onClicked: NavigationManager.go(CONST.SCREEN.SERVICE_FUNCTION, CONST.SCREEN.SERVICE)
        }

        CardButton {
            Layout.fillWidth: true
            iconSource: PathResolver.resolveAsset("Images/status.png")
            text: "Status"
            onClicked: NavigationManager.go(CONST.SCREEN.SERVICE_STATUS, CONST.SCREEN.SERVICE)
        }

        CardButton {
            Layout.fillWidth: true
            iconSource: PathResolver.resolveAsset("Images/service-mode.png")
            text: "Service Mode"
            onClicked: {
                serviceModePopup.open()
            }
            // TODO permissions check can be imporoved
            visible: UserManager.testPermission("Configuration")
        }

    }

    BlurOverlay {
        anchors.fill: parent
        source: parent
        visible: serviceModePopup.visible
    }

    QPopup {
        id: serviceModePopup

        confirmationText: "Are you sure you want to go into service mode?"
        button1Text: "Continue"
        button1Color: Theme.Colors.red
        button2Text: "Cancel"
        button2Color: Theme.Colors.black
        onButton1Pressed: {
            close();
            NavigationManager.go(CONST.SCREEN.SERVICE_SERVICE, CONST.SCREEN.SERVICE)
        }
        onButton2Pressed: {
            close();
        }

        onOpened: {
            mainLayout.popupVisible = true
        }

        onClosed: {
            mainLayout.popupVisible = false;
        }
    }

}
