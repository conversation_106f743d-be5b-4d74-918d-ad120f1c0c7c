import QtQuick 2.15
import QtQuick.Controls 2.15
// Import our modular components
import "qrc:/UI/Screens/print"
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: printScreen

    signal navigateToRequested(string to, string from)

    gradient: Theme.Colors.backgroundGradient

    // Main content layout using the modular PrintLayout component
    PrintLayout {
        anchors.fill: parent
    }

}
