import "../Components/bottombar/BottomBarConfigs.js" as BottomBarConfigs
import Backend.UserManager 1.0
import QtGraphicalEffects 1.14
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Assets"
import "qrc:/UI/Components" as Components
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Screens/login" as LoginComponents
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: loginScreen

    property alias usernameField: loginCard.usernameField
    property alias passwordField: loginCard.passwordField

    signal loginSuccessful(string username)

    function validateLoginForm() {
        var isValid = true;
        if (usernameField.text.trim() === "") {
            usernameField.isValid = false;
            usernameField.errorMessage = "Username is required";
            isValid = false;
        } else if (usernameField.text.length < 3) {
            usernameField.isValid = false;
            usernameField.errorMessage = "Username too short";
            isValid = false;
        } else {
            usernameField.isValid = true;
            usernameField.errorMessage = "";
        }
        if (passwordField.text === "") {
            passwordField.isValid = false;
            passwordField.errorMessage = "Password is required";
            isValid = false;
        } else if (passwordField.text.length < 3) {
            passwordField.isValid = false;
            passwordField.errorMessage = "Password too short";
            isValid = false;
        } else {
            passwordField.isValid = true;
            passwordField.errorMessage = "";
        }
        return isValid;
    }

    anchors.fill: parent
    color: "transparent"

    Image {
        source: PathResolver.resolveAsset(CONST.ASSET_PATH.BG_LOGIN)
        anchors.fill: parent
        fillMode: Image.PreserveAspectCrop
        z: -1
    }

    Item {
        id: logoContainer

        width: parent.width * 0.42
        height: parent.height
        anchors.left: parent.left

        Column {
            anchors.centerIn: parent
            spacing: 20

            Image {
                id: logoImage

                source: PathResolver.resolveAsset(CONST.ASSET_PATH.LOGO_PROSPR)
                width: Math.min(parent.width * 1.1, 600)
                height: Math.min(parent.width * 0.5, 220)
                fillMode: Image.PreserveAspectFit
                anchors.horizontalCenter: parent.horizontalCenter
                layer.enabled: true

                layer.effect: DropShadow {
                    transparentBorder: true
                    horizontalOffset: 0
                    verticalOffset: 8
                    radius: 16
                    samples: 25
                    color: Theme.Colors.logoDropShadow
                }

            }

            Rectangle {
                width: Math.min(parent.width * 0.8, 600)
                height: 2
                color: Theme.Colors.white
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.topMargin: 5
                anchors.bottomMargin: 5
            }

            Text {
                text: "MARK AMBITIOUSLY."
                font.pixelSize: 16
                font.letterSpacing: 3
                font.weight: Font.Bold
                color: Theme.Colors.white
                anchors.horizontalCenter: parent.horizontalCenter
            }

        }

    }

    LoginComponents.LoginCard {
        id: loginCard

        width: parent.width * 0.55
        height: parent.height * 0.65
        anchors.right: parent.right
        anchors.rightMargin: parent.width * 0.05
        anchors.verticalCenter: parent.verticalCenter
        onLoginRequested: {
            if (validateLoginForm()) {
                console.log("Login attempt with username:", usernameField.text);
                UserManager.setCurrentUser(usernameField.text);
                NavigationManager.go(CONST.SCREEN.HOME, CONST.SCREEN.LOG_IN);
            }
        }

        onCancelRequested: {
            usernameField.text = "";
            passwordField.text = "";
            usernameField.isValid = true;
            passwordField.isValid = true;
        }

        onOpenUserPermissionsRequested: {
            NavigationManager.go("settings/UserPermissions", "LogIn", { user: UserManager.currentUser });
        }

        onCreateUserRequested: {
            NavigationManager.go(CONST.SCREEN.SETTINGS_ADD_USER, CONST.SCREEN.LOG_IN);

        }
    }

}
