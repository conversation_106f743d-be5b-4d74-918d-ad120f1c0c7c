import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: upgradeScreen

    signal navigateToRequested(string to, string from)

    color: Theme.Colors.white

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 30
        spacing: 20

        // Title
        Text {
            text: "Software Upgrade"
            font.pixelSize: 24
            font.weight: Font.Bold
            color: Theme.Colors.secondary
            Layout.alignment: Qt.AlignHCenter
        }

        // Robot graphic
        Item {
            Layout.preferredHeight: 200
            Layout.fillWidth: true

            Image {
                source: "../Assets/robot.png" // You would need to create this asset
                anchors.centerIn: parent
                width: 150
                height: 150
                fillMode: Image.PreserveAspectFit

                // Fallback if image is missing
                Text {
                    visible: parent.status !== Image.Ready
                    text: "🤖"
                    font.pixelSize: 120
                    anchors.centerIn: parent
                    color: Theme.Colors.red
                }

            }

        }

        // Upgrade file selection
        ColumnLayout {
            Layout.fillWidth: true
            spacing: 10

            Text {
                text: "Upgrade"
                font.pixelSize: 16
                font.weight: Font.Medium
                color: Theme.Colors.secondary
            }

            Rectangle {
                Layout.fillWidth: true
                height: 50
                border.width: 1
                border.color: Theme.Colors.borderColor
                radius: 4

                RowLayout {
                    anchors.fill: parent
                    anchors.margins: 10
                    spacing: 10

                    // File path or name
                    Text {
                        text: "No file selected"
                        font.pixelSize: 14
                        color: Theme.Colors.textSecondary
                        Layout.fillWidth: true
                    }

                    // Browse button
                    Button {
                        // File browser dialog would open here

                        Layout.preferredWidth: 120
                        Layout.preferredHeight: 30
                        onClicked: {
                        }

                        contentItem: Text {
                            text: "BROWSE"
                            font.pixelSize: 14
                            font.weight: Font.Medium
                            color: Theme.Colors.white
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }

                        background: Rectangle {
                            radius: 4
                            color: Theme.Colors.red
                        }

                    }

                }

            }

        }

        // Upgrade button
        Button {
            // Start upgrade process

            Layout.preferredWidth: 300
            Layout.preferredHeight: 50
            Layout.alignment: Qt.AlignHCenter
            Layout.topMargin: 20
            onClicked: {
            }

            contentItem: Text {
                text: "UPGRADE"
                font.pixelSize: 16
                font.weight: Font.Medium
                color: Theme.Colors.white
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }

            background: Rectangle {
                radius: 4
                color: Theme.Colors.secondary
            }

        }

        // Filler space
        Item {
            Layout.fillHeight: true
        }

    }

    // Bottom action buttons
    Rectangle {
        id: bottomBar

        width: parent.width
        height: 60
        anchors.bottom: parent.bottom
        color: Theme.Colors.backgroundPrimary

        RowLayout {
            anchors.left: parent.left
            anchors.leftMargin: 10
            anchors.verticalCenter: parent.verticalCenter
            spacing: 10

            // Back button
            Button {
                Layout.preferredWidth: 120
                Layout.preferredHeight: 40
                onClicked: mainLayout.navigateToRequested("Settings", currentScreen)

                contentItem: RowLayout {
                    spacing: 5

                    Text {
                        text: "←"
                        font.pixelSize: 16
                        color: Theme.Colors.secondary
                    }

                    Text {
                        text: "BACK"
                        font.pixelSize: 14
                        font.weight: Font.Medium
                        color: Theme.Colors.secondary
                    }

                }

                background: Rectangle {
                    radius: 4
                    border.color: Theme.Colors.borderColor
                    border.width: 1
                    color: Theme.Colors.white
                }

            }

        }

    }

}
