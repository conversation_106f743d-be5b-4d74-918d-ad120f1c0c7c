import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Theme" as Theme

Item {
    function action(name) {
        if (name === "save")
            toast.show();

    }

    ToastPopup {
        id: toast

        text: "Save Changes"

        anchors {
            right: parent.right
            top: parent.top
            margins: 20
        }

    }

    Text {
        anchors.top: parent.top
        anchors.topMargin: Theme.Spacing.medium
        anchors.horizontalCenter: parent.horizontalCenter
        text: "Date & Time"
        font.pointSize: Theme.Typography.h1
        font.family: Theme.Typography.primaryFontFamily
    }

    ColumnLayout {
        anchors.fill: parent

        Item {
            Layout.fillHeight: true
        }

        Row {
            Layout.alignment: Qt.AlignHCenter
            spacing: Theme.Spacing.large

            CardSpinBox {
                title: "Year"
                fromValue: 1900
                toValue: 2100
                value: 2025
            }

            CardSpinBox {
                title: "Month"
                fromValue: 1
                toValue: 12
                value: 11
            }

            CardSpinBox {
                title: "Day"
                fromValue: 1
                toValue: 31
                value: 15
            }

            Item {
                height: 1
                width: 20
            }

            CardSpinBox {
                title: "Hour"
                fromValue: 0
                toValue: 23
                value: 10
            }

            CardSpinBox {
                title: "Minute"
                fromValue: 0
                toValue: 59
                value: 30
            }

            CardSpinBox {
                title: "Second"
                fromValue: 0
                toValue: 59
                value: 30
            }

        }

        Item {
            Layout.fillHeight: true
        }

    }

}
