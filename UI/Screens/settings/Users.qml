import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme
import Backend.UserManager 1.0

Item {
    function action(actionName) {
        var selectedUser = userListView.model[userListView.currentIndex];
        if (!selectedUser || selectedUser.username === "admin" || selectedUser.username === "guest" || selectedUser.username === "") {
            return;
        }

        if (actionName === "delete") {
            deletePopup.open();
            mainLayout.popupVisible = true;
        } else if (actionName === "edit") {
            NavigationManager.go(CONST.SCREEN.SETTINGS_USER_PERMISSIONS, CONST.SCREEN.SETTINGS_USERS, { user: selectedUser });
        }
    }

    anchors.fill: parent
    layer.enabled: true

    TitledFrame {
        id: frame

        width: 1100
        title: "User & Permission"

        anchors {
            horizontalCenter: parent.horizontalCenter
            top: parent.top
            bottom: parent.bottom
            margins: Theme.Spacing.large
        }

        // User list
        ListView {
            id: userListView

            anchors.fill: parent
            layer.enabled: true
            layer.effect: OpacityMask {
                maskSource: Rectangle {
                    width: frame.width
                    height: frame.height
                    // TODO the mask should crop only bottom border, top shouldn't be masked out
                    // radius: frame.radius
                }
            }
            model: UserManager.users

            delegate: ItemDelegate {
                id: delegate

                height: 80
                width: parent.width
                leftPadding: 30
                rightPadding: 30
                topInset: -1

                // Divider
                Rectangle {
                    anchors.left: contentItem.left
                    anchors.bottom: parent.bottom
                    width: contentItem.width
                    height: 1
                    color: Theme.Colors.borderColor
                }

                background: Rectangle {
                    color: delegate.ListView.isCurrentItem ? Theme.Colors.addOpacity(Theme.Colors.primary, 0.2) : Theme.Colors.transparent
                }

                contentItem: RowLayout {
                    spacing: 15
                    opacity: delegate.disabled ? 0.4 : 1

                    Image {
                        Layout.alignment: Qt.AlignVCenter
                        source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_USER)
                        sourceSize.width: 50
                        sourceSize.height: 50
                    }

                    // User name
                    Text {
                        Layout.alignment: Qt.AlignVCenter
                        text: modelData.username
                        font.pointSize: Theme.Typography.h3
                        font.family: Theme.Typography.secondaryFontFamily
                    }

                    // Current user tag
                    Rectangle {
                        Layout.alignment: Qt.AlignVCenter
                        visible: UserManager.currentUser.username === modelData.username
                        color: Theme.Colors.primary
                        radius: Theme.Radius.round
                        height: 42
                        width: currentText.width + 20

                        Text {
                            id: currentText

                            text: "Current"
                            color: Theme.Colors.white
                            font.pointSize: Theme.Typography.h3
                            font.family: Theme.Typography.secondaryFontFamily
                            anchors.centerIn: parent
                        }

                    }

                    Item {
                        Layout.fillWidth: true
                    }
                }

                onClicked: {
                    userListView.currentIndex = index;
                }
            }

            ScrollBar.vertical: ScrollBar {
                policy: ScrollBar.AsNeeded // Shows only when needed
                width: 10

                contentItem: Rectangle {
                    color: Theme.Colors.statusLightGrey // Light gray for the scrollbar handle
                    radius: 10
                }

            }

        }

    }

    // Use the new BlurOverlay component
    BlurOverlay {
        anchors.fill: parent
        source: parent
        visible: deletePopup.visible
    }

    QPopup {
        id: deletePopup

        property int userIndex: userListView.currentIndex

        confirmationText: userIndex >= 0 ? `Are you sure you want to delete ${userListView.model[userIndex].username}?` : ""
        button1Text: "DELETE"
        button1Color: Theme.Colors.red
        button1ImageSource: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_TRASH)
        button2Text: "CANCEL"
        button2Color: Theme.Colors.black
        button2ImageSource: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_FORBIDDEN)
        onButton1Pressed: {
            UserManager.deleteUser(userListView.model[userIndex].username)
            UserManager.saveUsers()
            close();
        }
        onButton2Pressed: {
            close();
        }
        onClosed: {
            mainLayout.popupVisible = false;
        }
    }

}
