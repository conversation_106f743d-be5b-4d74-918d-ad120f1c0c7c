import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Theme" as Theme

Item {
    TitledFrame {
        anchors.centerIn: parent
        width: 1100
        height: 800
        title: "System Information"

        ListView {
            id: systemInfoList

            anchors.fill: parent
            anchors.bottomMargin: 20
            clip: true
            interactive: true
            spacing: 0

            model: ListModel {
                ListElement {
                    label: "Printer Type"
                    value: "Prospr Light"
                }

                ListElement {
                    label: "Printer ID"
                    value: "7560375"
                }

                ListElement {
                    label: "Main Board ID"
                    value: "6730967"
                }

                ListElement {
                    label: "Main Board Hardware"
                    value: "V4.2.1"
                }

                ListElement {
                    label: "Main Board Software"
                    value: "V4.22.7"
                }

                ListElement {
                    label: "System Software"
                    value: "?"
                }

                ListElement {
                    label: "Update Time"
                    value: "02/11/25 13:45:12:54"
                }

                ListElement {
                    label: "Machine Run Time"
                    value: "3 Days"
                }

                ListElement {
                    label: "Jet Time"
                    value: "2h 35m 48s"
                }

                ListElement {
                    label: "Nozzle Size"
                    value: "0.4 mm"
                }

            }

            delegate: Column {
                anchors {
                    left: parent.left
                    leftMargin: 20
                    right: parent.right
                    rightMargin: 20
                }

                Item {
                    width: parent.width
                    height: 80

                    Text {
                        id: labelText

                        color: Theme.Colors.darkGrey
                        text: model.label
                        font.pixelSize: Theme.Typography.h3
                        font.family: Theme.Fonts.youngSerif
                        anchors.left: parent.left
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    Text {
                        color: Theme.Colors.black
                        text: model.value
                        font.pixelSize: Theme.Typography.h3
                        font.family: Theme.Fonts.youngSerif
                        anchors.right: parent.right
                        anchors.verticalCenter: parent.verticalCenter
                    }

                }

                // Divider
                Rectangle {
                    width: parent.width
                    height: 1
                    color: Theme.Colors.borderColor
                }

            }

            ScrollBar.vertical: ScrollBar {
                policy: ScrollBar.AlwaysOn
            }

        }

    }

}
