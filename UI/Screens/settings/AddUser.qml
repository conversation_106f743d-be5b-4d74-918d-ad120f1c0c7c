import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Theme" as Theme
import "qrc:/UI/Core/constants.js" as CONST
import Backend.UserManager 1.0

Item {
    TitledFrame {
        anchors.centerIn: parent
        title: "Add User"
        width: 1100
        height: 600

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 25

            // Username field
            Column {
                Layout.fillWidth: true
                spacing: 10

                Text {
                    text: "User Name"
                    font.pixelSize: Theme.Typography.h2
                    font.family: Theme.Typography.secondaryFontFamily
                    color: Theme.Colors.black
                }

                LightInput {
                    id: userNameInput
                    anchors.left: parent.left
                    anchors.right: parent.right
                    font.family: Theme.Typography.secondaryFontFamily
                    font.pixelSize: Theme.Typography.h2
                    placeholderText: "Enter User Name"
                    validator: RegExpValidator { regExp: /^[A-Za-z0-9]+$/ }
                    isValid: acceptableInput && !UserManager.userExists(text)
                    errorMessage: {
                        if (!acceptableInput) {
                            return "User Name must contain letters and numbers only"
                        }
                        if (UserManager.userExists(text)) {
                            return "User already exists"
                        }
                        return ""
                    }
                }
            }

            // Password field
            Column {
                Layout.fillWidth: true
                spacing: 10

                Text {
                    text: "Password"
                    font.pixelSize: Theme.Typography.h2
                    font.family: Theme.Typography.secondaryFontFamily
                    color: Theme.Colors.black
                }

                LightInput {
                    id: passwordInput
                    anchors.left: parent.left
                    anchors.right: parent.right
                    font.family: Theme.Typography.secondaryFontFamily
                    font.pixelSize: Theme.Typography.h2
                    placeholderText: "Enter Password"
                    validator: RegExpValidator { regExp: /^[A-Za-z0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+$/ }
                    errorMessage: isValid ? "" : "Password contains invalid characters"
                }
            }

            DefaultButton {
                Layout.alignment: Qt.AlignRight
                text: "Continue to permissions"
                enabled: userNameInput.isValid && passwordInput.isValid

                onClicked: {
                    // Check if user already exists
                    if (UserManager.userExists(userNameInput.text)) {
                        console.log("User already exists:", userNameInput.text)
                        userNameInput.errorMessage = "User already exists";
                        return
                    }

                    var newUser = UserManager.createUserInfo(userNameInput.text, passwordInput.text);
                    NavigationManager.go(CONST.SCREEN.SETTINGS_USER_PERMISSIONS, CONST.SCREEN.SETTINGS_ADD_USER, { user: newUser })
                }
            }
        }
    }
}
