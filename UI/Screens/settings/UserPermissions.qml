import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme
import Backend.UserManager 1.0

Item {
    id: root

    function action(actionName) {
        if (actionName === "save") {
            if (UserManager.userExists(user.username)) {
                UserManager.deleteUser(user.username)
            }

            UserManager.addUser(user)
            UserManager.saveUsers()
            NavigationManager.go(CONST.SCREEN.SETTINGS_USERS, CONST.SCREEN.SETTINGS_USER_PERMISSIONS)
        }
    }

    property var user
    anchors.fill: parent

    PermissionsList {
        id: allowedPermissionsList

        groupFilter: groupComboBox.currentValue
    }

    PermissionsList {
        id: disallowedPermissionsList

        groupFilter: groupComboBox.currentValue
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 30
        spacing: 20

        Row {
            spacing: 20

            Text {
                anchors.verticalCenter: parent.verticalCenter
                text: "User: "
                font.pixelSize: Theme.Typography.h2
                font.family: Theme.Typography.secondaryFontFamily
                color: Theme.Colors.black
            }

            LightInput {
                text: root.user.username
                font.pixelSize: Theme.Typography.h2
                readOnly: true
            }

            Text {
                anchors.verticalCenter: parent.verticalCenter
                text: "Permissions:"
                font.pixelSize: Theme.Typography.h2
                font.family: Theme.Typography.secondaryFontFamily
                color: Theme.Colors.black
            }

            ComboBoxControl {
                id: groupComboBox

                textRole: "display"
                valueRole: "value"

                Component.onCompleted: {
                    // TODO: Should be done in a better way
                    var groups = UserManager.getAllPermissionGroups();
                    var modelArr = [];
                    for (var i = 0; i < groups.length; ++i) {
                        modelArr.push({ display: groups[i], value: groups[i] });
                    }
                    modelArr.push({ display: "All", value: "" });
                    model = modelArr;
                }
            }
        }

        // Permissions containers
        RowLayout {
            spacing: 20

            // Allowed permissions
            ColumnLayout {
                spacing: 10

                Text {
                    text: "Allowed"
                    font.pixelSize: Theme.Typography.h2
                    font.family: Theme.Typography.secondaryFontFamily
                    font.weight: Font.Bold
                    color: Theme.Colors.black
                }

                Rectangle {
                    id: rect
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    radius: Theme.Radius.item

                    ListView {
                        id: allowedList

                        anchors.fill: parent
                        model: allowedPermissionsList.model

                        layer.enabled: true
                        layer.effect: OpacityMask {
                            maskSource: Rectangle {
                                radius: Theme.Radius.item
                                width: allowedList.width
                                height: allowedList.height
                            }
                        }

                        delegate: ItemDelegate {
                            property string permissionName: name

                            width: allowedList.contentItem.width
                            height: 100
                            leftPadding: 30
                            rightPadding: 30
                            topInset: -1

                            // Divider
                            Rectangle {
                                anchors.bottom: parent.bottom
                                anchors.left: contentItem.left
                                width: contentItem.width
                                height: 1
                                color: Theme.Colors.borderColor
                            }

                            background: Rectangle {
                                color: allowedList.currentIndex === index ? Theme.Colors.addOpacity(Theme.Colors.primary, 0.2) : Theme.Colors.transparent
                            }

                            contentItem: Text {
                                verticalAlignment: Text.AlignVCenter
                                text: name
                                font.pixelSize: Theme.Typography.h2
                                font.family: Theme.Typography.secondaryFontFamily
                                color: ListView.isCurrentItem ? Theme.Colors.red : Theme.Colors.black
                                opacity: enabled ? 1 : 0.3
                            }

                            onClicked: {
                                allowedList.currentIndex = index;
                            }

                        }

                    }

                }

            }

            // Transfer buttons
            ColumnLayout {
                Layout.preferredWidth: 60
                Layout.alignment: Qt.AlignCenter
                spacing: 20

                // Move to blocked
                SquareButton {
                    enabled: allowedList.currentIndex !== -1
                    image.source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_RIGHT_ARROW_WHITE)
                    backgroundRectangle.color: Theme.Colors.primary
                    onClicked: {
                        if (allowedList.currentIndex !== -1) {
                            user.removePermission(allowedList.currentItem.permissionName)
                            allowedPermissionsList.movePermissionTo(disallowedPermissionsList, allowedList.currentIndex)
                        }
                    }
                }

                // Move to allowed
                SquareButton {
                    enabled: disallowedList.currentIndex !== -1
                    image.source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_LEFT_ARROW_BLACK)
                    backgroundRectangle.color: Theme.Colors.addOpacity(Theme.Colors.primary, 0.2)
                    onClicked: {
                        if (disallowedList.currentIndex !== -1) {
                            user.addPermission(disallowedList.currentItem.permissionName)
                            disallowedPermissionsList.movePermissionTo(allowedPermissionsList, disallowedList.currentIndex)
                        }
                    }

                    backgroundRectangle.border {
                        color: Theme.Colors.primary
                        width: 1
                    }

                }

            }

            // Blocked permissions
            ColumnLayout {
                spacing: 10

                Text {
                    text: "Blocked"
                    font.pixelSize: Theme.Typography.h2
                    font.family: Theme.Typography.secondaryFontFamily
                    font.weight: Font.Bold
                    color: Theme.Colors.black
                }

                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    radius: Theme.Radius.item

                    ListView {
                        id: disallowedList

                        anchors.fill: parent
                        model: disallowedPermissionsList.model
                        layer.enabled: true
                        layer.effect: OpacityMask {
                            maskSource: Rectangle {
                                radius: Theme.Radius.item
                                width: disallowedList.width
                                height: disallowedList.height
                            }
                        }

                        delegate: ItemDelegate {
                            property string permissionName: name

                            width: disallowedList.contentItem.width
                            height: 100
                            leftPadding: 30
                            rightPadding: 30
                            topInset: -1
                            font.family: Theme.Typography.secondaryFontFamily

                            // Divider
                            Rectangle {
                                width: parent.width
                                height: 1
                                color: Theme.Colors.borderColor

                                anchors {
                                    left: parent.left
                                    right: parent.right
                                    bottom: parent.bottom
                                }

                            }

                            background: Rectangle {
                                color: disallowedList.currentIndex === index ? Theme.Colors.addOpacity(Theme.Colors.primary, 0.2) : Theme.Colors.transparent
                            }

                            contentItem: Text {
                                verticalAlignment: Text.AlignVCenter
                                text: name
                                font.pixelSize: Theme.Typography.h2
                                font.family: Theme.Typography.secondaryFontFamily
                                color: ListView.isCurrentItem ? Theme.Colors.red : Theme.Colors.black
                                opacity: enabled ? 1 : 0.3
                            }

                            onClicked: {
                                disallowedList.currentIndex = index;
                            }
                        }

                    }

                }

            }

        }

    }

    onUserChanged: {
        allowedPermissionsList.initializePermissions(UserManager.getAllowedPermissions(user.username))
        disallowedPermissionsList.initializePermissions(UserManager.getDisallowedPermissions(user.username))
    }
}
