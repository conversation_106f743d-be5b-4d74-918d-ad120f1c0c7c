import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Theme" as Theme

Item {
    anchors.fill: parent

    Text {
        anchors.top: parent.top
        anchors.topMargin: Theme.Spacing.medium
        anchors.horizontalCenter: parent.horizontalCenter
        text: "Others"
        font.pointSize: Theme.Typography.h1
        font.family: Theme.Typography.primaryFontFamily
        color: Theme.Colors.black
    }

    Row {
        anchors.centerIn: parent
        spacing: 30

        CardButton {
            text: "Communication"
            iconSource: PathResolver.resolveAsset("Images/messages.png")
        }

        CardButton {
            text: "Touch Calibration"
            iconSource: PathResolver.resolveAsset("Images/hand-touch.png")
        }

    }

}
