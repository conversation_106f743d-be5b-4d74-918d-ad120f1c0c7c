import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

Item {
    id: languageScreen

    // Model for languages
    property var languageModel: [{
        "id": "en",
        "name": "English",
        "flagIcon": "Images/flag-us.png",
        "selected": true
    }, {
        "id": "es",
        "name": "Españo<PERSON>",
        "flagIcon": "Images/flag-es.png",
        "selected": false
    }]
    // Track currently selected language
    property string selectedLanguageId: "en"

    signal selectLanguage(string lang)

    function action(name) {
        if (name === "save")
            toast.show();

    }

    ToastPopup {
        id: toast

        text: "Save Changes"

        anchors {
            right: parent.right
            top: parent.top
            margins: 20
        }

    }

    TitledFrame {
        anchors.centerIn: parent
        height: 640
        width: 1100
        title: "Language"

        ButtonGroup {
            id: btnGroup
        }

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 20

            // Language selection using Repeater
            Repeater {
                model: languageModel

                delegate: Loader {
                    Layout.fillWidth: true
                    sourceComponent: languageCheckBox
                    onLoaded: {
                        btnGroup.addButton(item);
                        item.checked = modelData.selected;
                        item.languageName = modelData.name;
                        item.flagIcon = modelData.flagIcon;
                        item.langId = modelData.id;
                    }
                }

            }

        }

    }

    Component {
        id: languageCheckBox

        CheckBox {
            id: customCheckBox

            // Property to receive the language name from the Loader
            property string languageName: ""
            property string flagIcon: ""
            property string langId: ""

            implicitHeight: 210
            padding: 30
            // Set the language name and checked state from the model
            onCheckedChanged: {
                if (checked)
                    selectLanguage(langId);

            }

            background: Rectangle {
                border.width: checked ? 2 : 1
                border.color: checked ? Theme.Colors.primary : Theme.Colors.addOpacity(Theme.Colors.primary, 0.2)
                radius: Theme.Radius.item

                gradient: Gradient {
                    orientation: Gradient.Vertical

                    GradientStop {
                        position: 0
                        color: Theme.Colors.primaryLight
                    }

                    GradientStop {
                        position: 1
                        color: Theme.Colors.white
                    }

                }

            }

            indicator: Rectangle {
                id: indicatorRect

                anchors.right: parent.right
                anchors.rightMargin: 30
                anchors.verticalCenter: parent.verticalCenter
                color: checked ? Theme.Colors.primary : Theme.Colors.secondary
                height: 100
                width: 300
                radius: 20

                Row {
                    anchors.horizontalCenter: parent.horizontalCenter
                    height: parent.height
                    spacing: 15

                    Image {
                        anchors.verticalCenter: parent.verticalCenter
                        source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_CHECKMARK_LANGUAGE)
                        visible: checked
                    }

                    Text {
                        anchors.verticalCenter: parent.verticalCenter
                        color: Theme.Colors.white
                        font.family: Theme.Typography.secondaryFontFamily
                        font.pixelSize: Theme.Typography.h1
                        font.bold: true
                        text: checked ? "SELECTED" : "SELECT"
                    }

                }

            }

            contentItem: Row {
                spacing: 30

                Image {
                    source: flagIcon !== "" ? PathResolver.resolveAsset(flagIcon) : ""
                }

                // Language name
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    text: languageName
                    font.family: Theme.Typography.secondaryFontFamily
                    font.pixelSize: 50
                    color: Theme.Colors.secondary
                }

            }

        }

    }

}
