import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Theme" as Theme

Item {
    signal uploadQR()
    signal browseFiles()

    TitledFrame {
        anchors.centerIn: parent
        width: 1100
        height: 485
        title: "QR Data"

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 25

            // QR Data input field with browse button
            Column {
                Layout.fillWidth: true
                spacing: 10

                Text {
                    text: "QR Data"
                    font.pixelSize: Theme.Typography.h2
                    font.family: Theme.Typography.secondaryFontFamily
                    color: Theme.Colors.black
                }

                RowLayout {
                    anchors.left: parent.left
                    anchors.right: parent.right
                    spacing: 15

                    LightInput {
                        Layout.fillWidth: true
                        font.family: Theme.Typography.secondaryFontFamily
                        font.pixelSize: Theme.Typography.h2
                        placeholderText: "Select QR file or enter data"
                        readOnly: false
                    }

                    // Browse button styled similar to Language.qml buttons
                    Rectangle {
                        Layout.preferredWidth: 200
                        Layout.preferredHeight: 60
                        color: Theme.Colors.orange
                        radius: 20

                        Text {
                            anchors.centerIn: parent
                            text: "BROWSE"
                            font.family: Theme.Typography.secondaryFontFamily
                            font.pixelSize: Theme.Typography.h3
                            color: Theme.Colors.white
                        }

                        MouseArea {
                            anchors.fill: parent
                            cursorShape: Qt.PointingHandCursor
                            onClicked: browseFiles()
                        }

                    }

                }

            }

            // Upload QR button styled similar to Language.qml indicator
            Rectangle {
                Layout.alignment: Qt.AlignHCenter
                Layout.preferredWidth: 500
                Layout.preferredHeight: 100
                color: Theme.Colors.secondary
                radius: 20

                Text {
                    anchors.centerIn: parent
                    text: "UPLOAD QR"
                    font.family: Theme.Typography.secondaryFontFamily
                    font.pixelSize: Theme.Typography.h1
                    color: Theme.Colors.white
                }

                MouseArea {
                    anchors.fill: parent
                    cursorShape: Qt.PointingHandCursor
                    onClicked: uploadQR()
                }

            }

        }

    }

}
