import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: messageEditorScreen

    color: Theme.Colors.backgroundSecondary

    // Three-panel layout: Left menu, canvas, properties
    RowLayout {
        anchors.fill: parent
        spacing: 0

        // Left menu panel
        Rectangle {
            Layout.preferredWidth: 240
            Layout.fillHeight: true
            color: Theme.Colors.secondary

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 0
                spacing: 0

                // Back to Home button
                Item {
                    Layout.fillWidth: true
                    height: 60

                    RowLayout {
                        anchors.fill: parent
                        anchors.leftMargin: 20
                        spacing: 10

                        // Back arrow
                        Text {
                            text: "←"
                            font.pixelSize: Theme.Typography.title
                            color: Theme.Colors.white
                        }

                        // Back text
                        Text {
                            text: "Back to Home"
                            font: Theme.Typography.bodyLarge
                            font.weight: Theme.Typography.weightMedium
                            color: Theme.Colors.white
                        }

                    }

                    MouseArea {
                        anchors.fill: parent
                        cursorShape: Qt.PointingHandCursor
                        onClicked: NavigationManager.go("Home")
                    }

                }

                // Separator
                Rectangle {
                    Layout.fillWidth: true
                    height: 1
                    color: Qt.rgba(Theme.Colors.white.r, Theme.Colors.white.g, Theme.Colors.white.b, 0.12)
                }

                // Insert components section
                Item {
                    Layout.fillWidth: true
                    height: 50

                    Text {
                        text: "INSERT COMPONENTS"
                        font: Theme.Typography.caption
                        font.weight: Theme.Typography.weightMedium
                        color: Theme.Colors.textTertiary
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.left: parent.left
                        anchors.leftMargin: Theme.Spacing.medium
                    }

                }

                // Component options
                Repeater {
                    model: [{
                        "name": "Text",
                        "icon": "T",
                        "description": "Add static text"
                    }, {
                        "name": "Metering",
                        "icon": "📏",
                        "description": "Add metering data"
                    }, {
                        "name": "Barcode",
                        "icon": "|||",
                        "description": "Add barcode/QR code"
                    }, {
                        "name": "Counter",
                        "icon": "123",
                        "description": "Add sequential counter"
                    }, {
                        "name": "Date & Time",
                        "icon": "🕒",
                        "description": "Add date/time field"
                    }, {
                        "name": "Batch Code",
                        "icon": "#",
                        "description": "Add batch identifier"
                    }, {
                        "name": "Shift",
                        "icon": "👥",
                        "description": "Add shift code"
                    }]

                    delegate: Item {
                        property bool isSelected: index === 0 // First item selected by default

                        Layout.fillWidth: true
                        height: 60

                        Rectangle {
                            anchors.fill: parent
                            color: isSelected ? Qt.rgba(Theme.Colors.white.r, Theme.Colors.white.g, Theme.Colors.white.b, 0.12) : "transparent"
                        }

                        RowLayout {
                            anchors.fill: parent
                            anchors.leftMargin: 20
                            spacing: 15

                            // Icon container
                            Rectangle {
                                width: 32
                                height: 32
                                radius: 4
                                color: Theme.Colors.primary

                                Text {
                                    anchors.centerIn: parent
                                    text: modelData.icon
                                    font.pixelSize: Theme.Typography.bodyLarge
                                    color: Theme.Colors.white
                                }

                            }

                            // Name and description
                            Column {
                                spacing: 2

                                Text {
                                    text: modelData.name
                                    font.pixelSize: Theme.Typography.bodyLarge
                                    font.weight: isSelected ? Theme.Typography.weightMedium : Theme.Typography.weightRegular
                                    color: Theme.Colors.white
                                }

                                Text {
                                    text: modelData.description
                                    font.pixelSize: Theme.Typography.helperText
                                    color: Theme.Colors.textTertiary
                                }

                            }

                        }

                        MouseArea {
                            // +3 to skip non-component items

                            anchors.fill: parent
                            cursorShape: Qt.PointingHandCursor
                            onClicked: {
                                // Update selection
                                for (var i = 0; i < parent.parent.children.length; i++) {
                                    if (parent.parent.children[i].isSelected !== undefined)
                                        parent.parent.children[i].isSelected = (i === index + 3);

                                }
                            }
                        }

                    }

                }

                // Spacer
                Item {
                    Layout.fillHeight: true
                }

                // Save/Test actions
                ColumnLayout {
                    Layout.fillWidth: true
                    Layout.margins: 15
                    spacing: 10

                    ProsprButton {
                        text: "Save Message"
                        buttonType: "primary"
                        Layout.fillWidth: true
                    }

                    ProsprButton {
                        text: "Test Print"
                        buttonType: "secondary"
                        isOutlined: true
                        Layout.fillWidth: true
                    }

                }

            }

        }

        // Center canvas area
        Rectangle {
            id: canvasArea

            Layout.fillWidth: true
            Layout.fillHeight: true
            color: Theme.Colors.backgroundPrimary
            Component.onCompleted: {
                Theme.Shadows.applyElevation(canvasArea, 1);
            }

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 20

                // Message name and template controls
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 15

                    ProsprTextField {
                        id: messageNameField

                        placeholderText: "Message Name"
                        text: "Product Label 001"
                        Layout.preferredWidth: 250
                    }

                    // Spacer
                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: "Template:"
                        font.pixelSize: 14
                        color: Theme.Colors.textTertiary
                    }

                    ComboBox {
                        id: templateSelector

                        model: ["Custom", "Template 1", "Template 2", "Template 3"]
                        Layout.preferredWidth: 150
                    }

                }

                // Label preview area
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    border.width: 1
                    border.color: Theme.Colors.borderColor
                    color: Theme.Colors.backgroundPrimary

                    // Grid background for visual reference
                    Canvas {
                        anchors.fill: parent
                        onPaint: {
                            var ctx = getContext("2d");
                            var gridSize = 20;
                            ctx.strokeStyle = "#e0e0e0";
                            ctx.lineWidth = 1;
                            // Draw vertical lines
                            for (var x = gridSize; x < width; x += gridSize) {
                                ctx.beginPath();
                                ctx.moveTo(x, 0);
                                ctx.lineTo(x, height);
                                ctx.stroke();
                            }
                            // Draw horizontal lines
                            for (var y = gridSize; y < height; y += gridSize) {
                                ctx.beginPath();
                                ctx.moveTo(0, y);
                                ctx.lineTo(width, y);
                                ctx.stroke();
                            }
                        }
                    }

                    // Example label content
                    Item {
                        anchors.centerIn: parent
                        width: 400
                        height: 200

                        // Product name
                        Rectangle {
                            id: textElement

                            width: 250
                            height: 40
                            x: 20
                            y: 20
                            border.width: 1
                            border.color: Theme.Colors.primary
                            color: Theme.Colors.transparent

                            Text {
                                anchors.centerIn: parent
                                text: "PROSPR INDUSTRIAL CLEANER"
                                font.pixelSize: 16
                                font.weight: Font.Bold
                                color: Theme.Colors.secondary
                            }

                            // Selection handles
                            Rectangle {
                                width: 8
                                height: 8
                                x: -4
                                y: -4
                                color: Theme.Colors.primary
                            }

                            Rectangle {
                                width: 8
                                height: 8
                                x: parent.width - 4
                                y: -4
                                color: Theme.Colors.primary
                            }

                            Rectangle {
                                width: 8
                                height: 8
                                x: -4
                                y: parent.height - 4
                                color: Theme.Colors.primary
                            }

                            Rectangle {
                                width: 8
                                height: 8
                                x: parent.width - 4
                                y: parent.height - 4
                                color: Theme.Colors.primary
                            }

                        }

                        // Batch code
                        Rectangle {
                            width: 180
                            height: 25
                            x: 20
                            y: 80
                            border.width: 1
                            border.color: Theme.Colors.borderColor
                            color: Theme.Colors.transparent

                            Text {
                                anchors.centerIn: parent
                                text: "BATCH: {BATCH_CODE}"
                                font.pixelSize: 14
                                color: Theme.Colors.textTertiary
                            }

                        }

                        // Date field
                        Rectangle {
                            width: 200
                            height: 25
                            x: 20
                            y: 120
                            border.width: 1
                            border.color: Theme.Colors.borderColor
                            color: Theme.Colors.transparent

                            Text {
                                anchors.centerIn: parent
                                text: "MFG: {DATE DD/MM/YYYY}"
                                font.pixelSize: 14
                                color: Theme.Colors.textTertiary
                            }

                        }

                        // Barcode
                        Rectangle {
                            width: 120
                            height: 80
                            x: 250
                            y: 80
                            border.width: 1
                            border.color: Theme.Colors.borderColor
                            color: Theme.Colors.transparent

                            Image {
                                anchors.fill: parent
                                source: "file://placeholder_barcode.png"
                                fillMode: Image.PreserveAspectFit
                            }

                        }

                    }

                    // Zoom controls
                    Rectangle {
                        anchors.right: parent.right
                        anchors.bottom: parent.bottom
                        anchors.margins: 10
                        width: 100
                        height: 30
                        radius: 15
                        color: Theme.Colors.secondary
                        opacity: 0.8

                        RowLayout {
                            anchors.centerIn: parent
                            spacing: 15

                            Text {
                                text: "-"
                                font.pixelSize: 18
                                font.weight: Font.Bold
                                color: Theme.Colors.white

                                MouseArea {
                                    // Zoom out

                                    anchors.fill: parent
                                    cursorShape: Qt.PointingHandCursor
                                    onClicked: {
                                    }
                                }

                            }

                            Text {
                                text: "100%"
                                font.pixelSize: 14
                                color: Theme.Colors.white
                            }

                            Text {
                                text: "+"
                                font.pixelSize: 18
                                font.weight: Font.Bold
                                color: Theme.Colors.white

                                MouseArea {
                                    // Zoom in

                                    anchors.fill: parent
                                    cursorShape: Qt.PointingHandCursor
                                    onClicked: {
                                    }
                                }

                            }

                        }

                    }

                }

                // Toolbar
                Rectangle {
                    Layout.fillWidth: true
                    height: 50
                    color: Theme.Colors.backgroundPrimary
                    radius: 4

                    RowLayout {
                        anchors.fill: parent
                        anchors.margins: 10
                        spacing: 15

                        // Alignment controls
                        RowLayout {
                            spacing: 5

                            Rectangle {
                                width: 30
                                height: 30
                                radius: 4
                                color: Theme.Colors.borderColor

                                Text {
                                    anchors.centerIn: parent
                                    text: "⬌"
                                    font.pixelSize: 16
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    cursorShape: Qt.PointingHandCursor
                                }

                            }

                            Rectangle {
                                width: 30
                                height: 30
                                radius: 4
                                color: Theme.Colors.borderColor

                                Text {
                                    anchors.centerIn: parent
                                    text: "⬍"
                                    font.pixelSize: 16
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    cursorShape: Qt.PointingHandCursor
                                }

                            }

                        }

                        // Separator
                        Rectangle {
                            width: 1
                            height: 30
                            color: Theme.Colors.borderColor
                        }

                        // Delete button
                        Rectangle {
                            width: 30
                            height: 30
                            radius: 4
                            color: Theme.Colors.borderColor

                            Text {
                                anchors.centerIn: parent
                                text: "🗑️"
                                font.pixelSize: 16
                            }

                            MouseArea {
                                anchors.fill: parent
                                cursorShape: Qt.PointingHandCursor
                            }

                        }

                        // Duplicate button
                        Rectangle {
                            width: 30
                            height: 30
                            radius: 4
                            color: Theme.Colors.borderColor

                            Text {
                                anchors.centerIn: parent
                                text: "📋"
                                font.pixelSize: 16
                            }

                            MouseArea {
                                anchors.fill: parent
                                cursorShape: Qt.PointingHandCursor
                            }

                        }

                        // Spacer
                        Item {
                            Layout.fillWidth: true
                        }

                        // Print preview
                        ProsprButton {
                            text: "Print Preview"
                            buttonType: "secondary"
                            isOutlined: true
                        }

                    }

                }

            }

        }

        // Right properties panel
        Rectangle {
            id: propertiesPanel

            Layout.preferredWidth: 300
            Layout.fillHeight: true
            color: Theme.Colors.backgroundSecondary
            border.width: 1
            border.color: Theme.Colors.borderColor

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 20

                // Properties title
                Text {
                    text: "Element Properties"
                    font.pixelSize: 18
                    font.weight: Font.Medium
                    color: Theme.Colors.secondary
                }

                // Selected element name
                Text {
                    text: "Text Element"
                    font.pixelSize: 16
                    color: Theme.Colors.textSecondary
                }

                // Properties form
                ColumnLayout {
                    spacing: 15
                    Layout.fillWidth: true

                    // Text content
                    Text {
                        text: "Content:"
                        font.pixelSize: 14
                        font.weight: Font.Medium
                        color: Theme.Colors.textSecondary
                    }

                    TextArea {
                        Layout.fillWidth: true
                        height: 60
                        text: "PROSPR INDUSTRIAL CLEANER"
                        font.pixelSize: 14

                        background: Rectangle {
                            border.width: 1
                            border.color: Theme.Colors.borderColor
                            radius: 4
                        }

                    }

                    // Font properties
                    Text {
                        text: "Font:"
                        font.pixelSize: 14
                        font.weight: Font.Medium
                        color: Theme.Colors.textSecondary
                    }

                    ComboBox {
                        Layout.fillWidth: true
                        model: ["Arial", "Helvetica", "Times New Roman", "Courier"]
                        currentIndex: 0
                    }

                    // Font size
                    Text {
                        text: "Size:"
                        font.pixelSize: 14
                        font.weight: Font.Medium
                        color: Theme.Colors.textSecondary
                    }

                    SpinBox {
                        Layout.fillWidth: true
                        from: 6
                        to: 72
                        value: 16
                    }

                    // Style options
                    GridLayout {
                        Layout.fillWidth: true
                        columns: 2
                        columnSpacing: 10
                        rowSpacing: 10

                        CheckBox {
                            text: "Bold"
                            checked: true
                        }

                        CheckBox {
                            text: "Italic"
                            checked: false
                        }

                        CheckBox {
                            text: "Underline"
                            checked: false
                        }

                        // Empty spacer
                        Item {
                            width: 1
                            height: 1
                        }

                    }

                    // Position
                    Text {
                        text: "Position:"
                        font.pixelSize: 14
                        font.weight: Font.Medium
                        color: Theme.Colors.textSecondary
                        Layout.topMargin: 10
                    }

                    GridLayout {
                        Layout.fillWidth: true
                        columns: 4

                        Text {
                            text: "X:"
                            font.pixelSize: 14
                            color: Theme.Colors.textSecondary
                        }

                        SpinBox {
                            Layout.fillWidth: true
                            from: 0
                            to: 1000
                            value: 20
                        }

                        Text {
                            text: "Y:"
                            font.pixelSize: 14
                            color: Theme.Colors.textSecondary
                        }

                        SpinBox {
                            Layout.fillWidth: true
                            from: 0
                            to: 1000
                            value: 20
                        }

                    }

                    // Dimensions
                    Text {
                        text: "Size:"
                        font.pixelSize: 14
                        font.weight: Font.Medium
                        color: Theme.Colors.textSecondary
                        Layout.topMargin: 10
                    }

                    GridLayout {
                        Layout.fillWidth: true
                        columns: 4

                        Text {
                            text: "W:"
                            font.pixelSize: 14
                            color: Theme.Colors.textSecondary
                        }

                        SpinBox {
                            Layout.fillWidth: true
                            from: 1
                            to: 1000
                            value: 250
                        }

                        Text {
                            text: "H:"
                            font.pixelSize: 14
                            color: Theme.Colors.textSecondary
                        }

                        SpinBox {
                            Layout.fillWidth: true
                            from: 1
                            to: 1000
                            value: 40
                        }

                    }

                }

                // Spacer
                Item {
                    Layout.fillHeight: true
                }

                // Apply button
                ProsprButton {
                    text: "Apply Changes"
                    buttonType: "primary"
                    Layout.fillWidth: true
                }

            }

        }

    }

}
