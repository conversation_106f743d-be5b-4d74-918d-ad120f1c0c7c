import Backend.UserManager 1.0
import QtGraphicalEffects 1.14
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: card

    // Property aliases for field access
    property alias usernameField: usernameInput
    property alias passwordField: passwordInput

    // Signals
    signal loginRequested()
    signal cancelRequested()
    signal createUserRequested()
    signal openUserPermissionsRequested()

    radius: Theme.Radius.item

    QtObject {
        id: constants

        property int userNameTextPixelSize: 80
        property int permissionsTextPixelSize: 58
        property int profileIconCardSize: 420
    }

    StackLayout {
        anchors.fill: parent
        anchors.margins: 30
        currentIndex: UserManager.currentUser.valid ? 1 : 0

        ColumnLayout {
            id: loginCard

            spacing: 20

            // Title
            Text {
                Layout.alignment: Qt.AlignHCenter
                height: 100
                text: "WELCOME BACK!"
                font.pointSize: Theme.Typography.h1
                font.family: Theme.Typography.primaryFontFamily
            }

            // Username field
            Column {
                Layout.fillWidth: true
                spacing: 10

                Text {
                    text: "User Name"
                    font.pixelSize: Theme.Typography.h2
                    font.family: Theme.Typography.secondaryFontFamily
                    color: Theme.Colors.black
                }

                LightInput {
                    id: usernameInput

                    anchors.left: parent.left
                    anchors.right: parent.right
                    font.family: Theme.Typography.secondaryFontFamily
                    font.pixelSize: Theme.Typography.h2
                    placeholderText: "Enter User Name"
                    focus: true
                }

            }

            // Password field
            Column {
                Layout.fillWidth: true
                spacing: 10

                Text {
                    text: "Password"
                    font.pixelSize: Theme.Typography.h2
                    font.family: Theme.Typography.secondaryFontFamily
                    color: Theme.Colors.black
                }

                LightInput {
                    id: passwordInput

                    anchors.left: parent.left
                    anchors.right: parent.right
                    font.family: Theme.Typography.secondaryFontFamily
                    font.pixelSize: Theme.Typography.h2
                    placeholderText: "Enter Password"
                    echoMode: TextInput.Password
                }

            }

            Item {
                Layout.fillHeight: true
            }

            RowLayout {
                Layout.fillWidth: true
                spacing: 20

                DefaultButton {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 85
                    backgroundRectangle.color: Theme.Colors.primary
                    font.weight: Theme.Typography.weightBold
                    text: "LOG IN"
                    onClicked: loginRequested()
                }

                DefaultButton {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 85
                    font.weight: Theme.Typography.weightBold
                    text: "CANCEL"
                    onClicked: cancelRequested()
                }

            }

            Item {
                Layout.fillHeight: true
            }

            // DefaultButton {
            //     Layout.fillWidth: true
            //     Layout.preferredHeight: 78
            //     text: "CREATE NEW USER"
            //     font.weight: Theme.Typography.weightBold
            //     image.source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_CREATE)
            //     image.sourceSize.width: 40
            //     image.sourceSize.height: 40
            //     onClicked: createUserRequested()
            // }

        }

        ColumnLayout {
            id: userCard

            spacing: 20

            Row {
                spacing: 30

                // User avatar placeholder
                Rectangle {
                    id: userIconRect

                    width: constants.profileIconCardSize
                    height: constants.profileIconCardSize
                    radius: 20
                    color: Theme.Colors.darkBlue

                    ColorOverlay {
                        anchors.fill: parent
                        anchors.margins: 55
                        color: "white"

                        source: Image {
                            source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_USER)
                            sourceSize: Qt.size(userIconRect.width, userIconRect.height)
                            fillMode: Image.PreserveAspectFit
                        }

                    }

                }

                // User name and permission
                Column {
                    anchors.verticalCenter: parent.verticalCenter

                    // User first name
                    Text {
                        text: UserManager.currentUser.valid ? UserManager.currentUser.firstName : ""
                        font.family: Theme.Typography.primaryFontFamily
                        font.pixelSize: constants.userNameTextPixelSize
                        color: Theme.Colors.black
                    }

                    // User last name
                    Text {
                        text: UserManager.currentUser.valid ? UserManager.currentUser.lastName : ""
                        font.family: Theme.Typography.primaryFontFamily
                        font.pixelSize: constants.userNameTextPixelSize
                        color: Theme.Colors.black
                    }

                    Item {
                        width: 1
                        height: 20
                    }

                    Text {
                        id: permissionText

                        text: "View Permissions"
                        textFormat: Text.StyledText
                        font.family: Theme.Typography.secondaryFontFamily
                        font.pixelSize: constants.permissionsTextPixelSize
                        color: Theme.Colors.orange

                        MouseArea {
                            anchors.fill: parent
                            cursorShape: Qt.PointingHandCursor
                            onClicked: {
                                openUserPermissionsRequested();
                            }
                        }

                    }

                    Rectangle {
                        height: 4
                        width: permissionText.width
                        color: Theme.Colors.orange
                    }

                }

            }

            // Spacer
            Item {
                Layout.fillHeight: true
            }

            // Bottom button row
            RowLayout {
                Layout.fillWidth: true
                spacing: 20

                // DefaultButton {
                //     Layout.fillWidth: true
                //     Layout.preferredHeight: 78
                //     backgroundRectangle.color: Theme.Colors.darkBlue
                //     font.weight: Theme.Typography.weightBold
                //     text: "SWITCH USER"
                //     icon.source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_USER_CIRCLE)
                //     onClicked: {
                //         UserManager.setCurrentUser("");
                //     }
                // }

                DefaultButton {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 78
                    backgroundRectangle.color: Theme.Colors.red
                    font.weight: Theme.Typography.weightBold
                    text: "LOG OUT"
                    icon.source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_LOGOUT)
                    onClicked: {
                        UserManager.setCurrentUser("");
                    }
                }

                // DefaultButton {
                //     Layout.fillWidth: true
                //     Layout.preferredHeight: 78
                //     backgroundRectangle.color: Theme.Colors.orange
                //     font.weight: Theme.Typography.weightBold
                //     text: "GO TO HOME"
                //     onClicked: NavigationManager.goHome()
                //     image.source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_HOME_SMALL)
                // }

            }

        }

    }

}
