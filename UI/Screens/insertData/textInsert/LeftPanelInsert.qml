import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

// File Selection Panel
Rectangle {
    id: fileSelectionSection

    // --- API: Signals for navigation/actions ---
    signal navigateToRequested(string to, string from)
    // Signal to notify when a file is selected
    signal fileSelected(string fileName, string lotNum, string bestByDate)

    radius: Theme.Radius.xxxlarge
    color: Theme.Colors.white

    // File List View
    ColumnLayout {
        clip: true
        spacing: Theme.Spacing.small

        anchors {
            top: parent.top
            left: parent.left
            right: actionsColumn.left
            topMargin: 30
            leftMargin: 30
            rightMargin: 30
        }

        ColumnLayout {
            spacing: Theme.Spacing.xssmall

            Text {
                text: "Message Name"
                font.pixelSize: Theme.Typography.labelText
                font.weight: Theme.Typography.weightMedium
                font.family: Theme.Typography.secondaryFontFamily
                color: Theme.Colors.darkGrey
            }

            LightInput {
                Layout.fillWidth: true
                placeholderText: "Enter Message Name"
                text: "File #1"
            }

        }

        ColumnLayout {
            id: sensorSelection

            property var model

            model: ListModel {
                ListElement {
                    name: '12'
                    active: true
                }

                ListElement {
                    name: '16'
                    active: false
                }

                ListElement {
                    name: '20'
                    active: false
                }

                ListElement {
                    name: '22'
                    active: false
                }

            }

            function updateText() {
                var activeSensors = [];
                for (var i = 0; i < sensorSelection.model.count; ++i) {
                    if (sensorSelection.model.get(i).active)
                        activeSensors.push(sensorSelection.model.get(i).name);

                }
                sensorTextInput.text = activeSensors.length > 0 ? activeSensors.join(", ") : "Select Size";
            }

            spacing: Theme.Spacing.xssmall
            Component.onCompleted: {
                updateText();
            }
            Text {
                text: "Select Size"
                font.pixelSize: Theme.Typography.labelText
                font.weight: Theme.Typography.weightMedium
                font.family: Theme.Typography.secondaryFontFamily
                color: Theme.Colors.darkGrey
            }

            LightInput {
                id: sensorTextInput

                property bool showOptions: false

                Layout.fillWidth: true
                placeholderText: "Select Size"
                font.pixelSize: Theme.Typography.labelText
                color: Theme.Colors.textGrey
                readOnly: true

                Image {
                    rotation: sensorTextInput.showOptions ? 180 : 0
                    source: PathResolver.resolveAsset("Images/dropdown-arrow.png")

                    anchors {
                        right: parent.right
                        rightMargin: 10
                        verticalCenter: parent.verticalCenter
                    }

                    Text {
                        anchors.centerIn: parent
                        visible: parent.status !== Image.Ready
                        text: "▼"
                        font.pixelSize: Theme.Typography.h4
                        color: Theme.Colors.borderColor
                    }

                }

                MouseArea {
                    anchors.fill: parent
                    cursorShape: Qt.PointingHandCursor
                    onClicked: {
                        sensorTextInput.showOptions = !sensorTextInput.showOptions;
                    }
                }

            }

        }

        ColumnLayout {
            spacing: Theme.Spacing.xssmall

            Text {
                text: "Select Setting"
                font.pixelSize: Theme.Typography.labelText
                font.weight: Theme.Typography.weightMedium
                font.family: Theme.Typography.secondaryFontFamily
                color: Theme.Colors.darkGrey
            }

            RowLayout {
                LightInput {
                    id: inputLight

                    Layout.fillWidth: true
                    placeholderText: "Select Setting"
                    text: "Default Printer Settings"
                    color: Theme.Colors.textGray
                    readOnly: true
                }

                Rectangle {
                    width: 210
                    height: inputLight.height
                    radius: Theme.Radius.xmlarge
                    color: Theme.Colors.orange

                    Text {
                        text: "SELECT"
                        color: Theme.Colors.white
                        font.pixelSize: 30
                        font.weight: Font.Bold
                        anchors.centerIn: parent
                    }

                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            console.log("SELECT button clicked");
                        }
                    }

                }

            }

        }

    }

    // Action buttons column
    ColumnLayout {
        id: actionsColumn

        width: 330
        spacing: Theme.Spacing.large

        anchors {
            top: parent.top
            right: parent.right
            bottom: parent.bottom
            topMargin: 30
            rightMargin: 30
            bottomMargin: 30
        }

        // EDIT button
        LeftPanelButton {
            text: "EDIT"
            source: PathResolver.resolveAsset(CONST.ASSET_PATH.IMAGE_EDIT_RECTANGLE)
            onClicked: {
                console.log("Edit button clicked");
            }
        }

        // COPY button
        LeftPanelButton {
            text: "COPY"
            source: PathResolver.resolveAsset("icons/copy.svg")
            onClicked: {
                console.log("Copy button clicked");
            }
        }

        // DELETE button
        LeftPanelButton {
            text: "DELETE"
            source: PathResolver.resolveAsset(CONST.ASSET_PATH.IMAGE_TRASH)
            onClicked: {
                console.log("Delete button clicked");
            }
        }

        // INSERT button
        LeftPanelButton {
            text: "INSERT"
            source: PathResolver.resolveAsset(CONST.ASSET_PATH.IMAGE_INSERT)
            onClicked: {
                console.log("Insert button clicked");
            }
        }

        // USB button
        LeftPanelButton {
            text: "USB"
            source: PathResolver.resolveAsset(CONST.ASSET_PATH.IMAGE_USB)
            onClicked: {
                console.log("USB button clicked");
            }
        }

    }

    // Loader for the sensor selection dialog
    Loader {
        id: sensorDialogLoader

        active: sensorTextInput.showOptions
        sourceComponent: sensorDialog
        onLoaded: {
            y = parent.mapFromItem(sensorTextInput, 0, 0).y - height - 10;
        }

        anchors {
            left: parent.left
            right: actionsColumn.left
            leftMargin: 30
            rightMargin: 30
        }

    }

    Component {
        id: sensorDialog

        Rectangle {
            property alias model: listViewSensors.model

            height: listViewSensors.height
            color: Theme.Colors.inputBackground
            border.width: 1
            border.color: Theme.Colors.borderColor
            radius: Theme.Radius.item / 2

            ButtonGroup {
                id: checkButtonGroup

                exclusive: true
                onClicked: function(button) {
                    if (checkState === Qt.Unchecked)
                        button.checked = true;

                }
            }

            ListView {
                id: listViewSensors

                height: Math.min(contentHeight, 400)
                clip: true
                model: sensorSelection.model

                anchors {
                    left: parent.left
                    right: parent.right
                    leftMargin: 20
                    rightMargin: 20
                }

                delegate: CheckDelegate {
                    id: control

                    anchors.left: parent.left
                    anchors.right: parent.right
                    height: 100
                    checked: active
                    font.family: Theme.Typography.secondaryFontFamily
                    text: name
                    ButtonGroup.group: checkButtonGroup
                    onCheckedChanged: {
                        listViewSensors.model.setProperty(index, "active", checked);
                        sensorSelection.updateText();
                    }

                    Rectangle {
                        width: parent.width
                        height: 1
                        color: Theme.Colors.borderColor
                        anchors.bottom: parent.bottom
                    }

                    contentItem: Text {
                        text: control.text
                        font.pixelSize: Theme.Typography.h2
                        font.family: Theme.Typography.secondaryFontFamily
                        color: active ? Theme.Colors.orange : Theme.Colors.black
                        opacity: enabled ? 1 : 0.3
                        verticalAlignment: Text.AlignVCenter
                    }

                    indicator: CheckBoxControl {
                        anchors.right: parent.right
                        anchors.verticalCenter: parent.verticalCenter
                        checked: control.checked
                        indicatorRadius: 25
                        enabled: false
                    }

                    background: Item {
                    }

                }

            }

        }

    }

}
