import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Theme" as Theme

// Main print layout component that organizes the three panels
Item {
    id: printLayout

    property Item selectedItem: null

    // Main content area with shadow and rounded corners
    Rectangle {
        id: mainContent

        anchors.fill: parent
        anchors.margins: 20
        color: "transparent" // Use transparent since parent already has gradient
        radius: Theme.Radius.large

        // Main layout with three panels
        Item {
            anchors.fill: parent

            // Top Panel
            TopPanelInsert {
                id: topPanel

                height: parent.height * 0.3
                onItemSelected: printLayout.selectedItem = item

                anchors {
                    top: parent.top
                    left: parent.left
                    right: parent.right
                }

            }

            // Left Panel
            LeftPanelInsert {
                id: leftPanel

                width: parent.width * 0.6 - 10

                anchors {
                    top: topPanel.bottom
                    left: parent.left
                    bottom: parent.bottom
                    topMargin: 20
                }

            }

            // Right Panel
            RightPanelInsert {
                id: rightPanel

                onLeftButtonClicked: {
                    if (printLayout.selectedItem != null)
                        printLayout.selectedItem.x -= 5;

                }
                onRightButtonClicked: {
                    if (printLayout.selectedItem != null)
                        printLayout.selectedItem.x += 5;

                }
                onUpButtonClicked: {
                    if (printLayout.selectedItem != null)
                        printLayout.selectedItem.y -= 5;

                }
                onDownButtonClicked: {
                    if (printLayout.selectedItem != null)
                        printLayout.selectedItem.y += 5;

                }

                anchors {
                    top: topPanel.bottom
                    left: leftPanel.right
                    right: parent.right
                    bottom: parent.bottom
                    topMargin: 20
                    leftMargin: 20
                }

            }

        }

    }

}
