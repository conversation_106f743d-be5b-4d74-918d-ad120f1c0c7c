import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Theme" as Theme

// Print Preview Panel
Rectangle {
    // Defined property for the preview content

    id: printPreviewSection

    // Signal when text is selected
    signal itemSelected(Item item)

    color: Theme.Colors.white
    radius: Theme.Radius.large

    // Placeholder for the actual preview content
    Item {
        anchors.fill: parent
        anchors.margins: 30
        clip: true

        Text {
            id: lotNumber

            x: 0
            y: parent.height - bestByDate.height - height
            text: "LOT #12345"
            font.pixelSize: 30
            font.weight: Font.Bold
            color: Theme.Colors.black

            MouseArea {
                anchors.fill: parent
                onClicked: printPreviewSection.itemSelected(parent)
            }

        }

        Text {
            id: bestByDate

            x: 0
            y: parent.height - height
            text: "BEST BUY 02/17/26"
            font.pixelSize: 30
            font.weight: Font.Bold
            color: Theme.Colors.black

            MouseArea {
                anchors.fill: parent
                onClicked: printPreviewSection.itemSelected(parent)
            }

        }

    }

}
