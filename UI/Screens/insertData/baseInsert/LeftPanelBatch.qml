import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

// File Selection Panel
Rectangle {
    id: fileSelectionSection

    radius: Theme.Radius.xxxlarge
    color: "transparent"

    Rectangle {
        id: innerRectangle

        radius: parent.radius
        color: Theme.Colors.white
        width: parent.width
        height: columnLayoutContent.implicitHeight + columnLayoutContent.anchors.topMargin + 30

        // File List View
        ColumnLayout {
            id: columnLayoutContent // Give an ID to the ColumnLayout

            clip: true
            spacing: Theme.Spacing.small

            anchors {
                top: parent.top
                left: parent.left
                right: parent.right
                topMargin: 30
                leftMargin: 30
                rightMargin: 30
            }

            ColumnLayout {
                spacing: Theme.Spacing.xssmall

                Text {
                    text: "Setting"
                    font.pixelSize: Theme.Typography.labelText
                    font.weight: Theme.Typography.weightMedium
                    font.family: Theme.Typography.secondaryFontFamily
                    color: Theme.Colors.black
                }

                LightInput {
                    Layout.fillWidth: true
                    placeholderText: "250"
                }

            }

        }

    }

    LeftPanelButton {
        text: "CUSTOM"
        width: parent.width / 4
        color: Theme.Colors.orange
        onClicked: {
            console.log("CUSTOM button clicked");
        }

        anchors {
            top: innerRectangle.bottom
            margins: 50
        }

    }

}
