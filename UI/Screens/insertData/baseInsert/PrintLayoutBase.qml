import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: mainContent

    property Item selectedItem: null
    property Component leftPanelContent: null
    property alias topPanelBottomAnchor: topPanel.bottom

    signal navigateToRequested(string to, string from)
    signal deletePopupRequested()

    anchors.fill: parent
    anchors.margins: 20
    color: "transparent"
    radius: Theme.Radius.xxxlarge

    TopPanelBarcode {
        id: topPanel

        height: parent.height * 0.3
        onItemSelected: mainContent.selectedItem = item

        anchors {
            top: parent.top
            left: parent.left
            right: parent.right
        }

    }

}
