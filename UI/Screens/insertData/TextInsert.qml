import "../../Components"
import "../../Theme" as Theme
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Screens/insertData/textInsert"

Rectangle {
    id: textInsertScreen

    gradient: Theme.Colors.backgroundGradient

    // Main content layout using the modular PrintLayout component
    PrintLayoutInsert {
        id: printLayout

        anchors.fill: parent
        z: 0 // Ensure content is below the blur overlay
    }

}
