import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: printUsbScreen

    color: Theme.Colors.backgroundSecondary

    // Main content layout
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 30
        spacing: 25

        // Header with navigation
        RowLayout {
            Layout.fillWidth: true
            spacing: 15

            // Back button
            Rectangle {
                width: 40
                height: 40
                radius: 20
                color: Theme.Colors.secondary

                Text {
                    anchors.centerIn: parent
                    text: "←"
                    font.pixelSize: 18
                    color: Theme.Colors.white
                }

                MouseArea {
                    anchors.fill: parent
                    cursorShape: Qt.PointingHandCursor
                    onClicked: NavigationManager.go("Home")
                }

            }

            // Title
            Text {
                text: "Print to USB"
                font.pixelSize: 24
                font.weight: Font.Bold
                color: Theme.Colors.secondary
            }

            // Separator
            Rectangle {
                Layout.preferredWidth: 1
                Layout.preferredHeight: 30
                color: Theme.Colors.borderColor
            }

            // Description
            Text {
                text: "Export your jobs to a USB drive"
                font.pixelSize: 16
                color: Theme.Colors.textSecondary
            }

            // Spacer
            Item {
                Layout.fillWidth: true
            }

        }

        // Main content with two columns
        RowLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true
            spacing: 30

            // Left column - USB devices
            Rectangle {
                Layout.preferredWidth: 400
                Layout.fillHeight: true
                color: Theme.Colors.white
                radius: 8

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 20
                    spacing: 20

                    // Section title
                    Text {
                        text: "USB Devices"
                        font.pixelSize: 18
                        font.weight: Font.Medium
                        color: Theme.Colors.secondary
                    }

                    // No device connected state
                    Rectangle {
                        Layout.fillWidth: true
                        height: 120
                        color: Theme.Colors.white
                        border.width: 1
                        border.color: Theme.Colors.borderColor
                        radius: 8
                        visible: true // Change this to false when USB is connected

                        ColumnLayout {
                            anchors.centerIn: parent
                            spacing: 10

                            Text {
                                text: "📂"
                                font.pixelSize: 32
                                Layout.alignment: Qt.AlignHCenter
                            }

                            Text {
                                text: "No USB device detected"
                                font.pixelSize: 16
                                font.weight: Font.Medium
                                color: Theme.Colors.textSecondary
                                Layout.alignment: Qt.AlignHCenter
                            }

                            Text {
                                text: "Connect a USB drive to continue"
                                font.pixelSize: 14
                                color: Theme.Colors.textSecondary
                                Layout.alignment: Qt.AlignHCenter
                            }

                        }

                    }

                    // USB device connected state (hidden by default)
                    Rectangle {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: Theme.Colors.white
                        visible: false // Change this to true when USB is connected

                        ColumnLayout {
                            anchors.fill: parent
                            anchors.margins: 15
                            spacing: 15

                            // USB drive info
                            Rectangle {
                                Layout.fillWidth: true
                                height: 100
                                color: Theme.Colors.white
                                border.width: 1
                                border.color: Theme.Colors.borderColor
                                radius: 8

                                RowLayout {
                                    anchors.fill: parent
                                    anchors.margins: 15
                                    spacing: 15

                                    // USB icon
                                    Rectangle {
                                        width: 50
                                        height: 50
                                        radius: 25
                                        color: Theme.Colors.white

                                        Text {
                                            anchors.centerIn: parent
                                            text: "🖴"
                                            font.pixelSize: 24
                                        }

                                    }

                                    // USB info
                                    ColumnLayout {
                                        Layout.fillWidth: true
                                        spacing: 5

                                        Text {
                                            text: "Kingston DataTraveler"
                                            font.pixelSize: 16
                                            font.weight: Font.Medium
                                            color: Theme.Colors.secondary
                                        }

                                        Text {
                                            text: "16 GB USB Drive"
                                            font.pixelSize: 14
                                            color: Theme.Colors.textSecondary
                                        }

                                        // Storage bar
                                        Rectangle {
                                            Layout.fillWidth: true
                                            height: 6
                                            radius: 3
                                            color: Theme.Colors.borderColor

                                            Rectangle {
                                                width: parent.width * 0.3 // 30% used
                                                height: parent.height
                                                radius: 3
                                                color: Theme.Colors.success
                                            }

                                        }

                                        Text {
                                            text: "4.8 GB used of 16 GB"
                                            font.pixelSize: 12
                                            color: Theme.Colors.textSecondary
                                        }

                                    }

                                    // Eject button
                                    Rectangle {
                                        width: 36
                                        height: 36
                                        radius: 18
                                        color: Theme.Colors.transparent

                                        Text {
                                            anchors.centerIn: parent
                                            text: "⏏"
                                            font.pixelSize: 16
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            cursorShape: Qt.PointingHandCursor
                                        }

                                    }

                                }

                            }

                            // Destination folder
                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 10

                                Text {
                                    text: "Destination Folder"
                                    font.pixelSize: 14
                                    font.weight: Font.Medium
                                    color: Theme.Colors.textSecondary
                                }

                                Rectangle {
                                    Layout.fillWidth: true
                                    height: 50
                                    border.width: 1
                                    border.color: Theme.Colors.borderColor
                                    radius: 4

                                    RowLayout {
                                        anchors.fill: parent
                                        anchors.margins: 10
                                        spacing: 10

                                        Text {
                                            text: "/Prospr/PrintJobs"
                                            font.pixelSize: 14
                                            color: Theme.Colors.secondary
                                            Layout.fillWidth: true
                                        }

                                        Rectangle {
                                            width: 30
                                            height: 30
                                            radius: 4
                                            color: Theme.Colors.transparent

                                            Text {
                                                anchors.centerIn: parent
                                                text: "📂"
                                                font.pixelSize: 16
                                            }

                                            MouseArea {
                                                anchors.fill: parent
                                                cursorShape: Qt.PointingHandCursor
                                            }

                                        }

                                    }

                                }

                            }

                            // File format
                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 10

                                Text {
                                    text: "File Format"
                                    font.pixelSize: 14
                                    font.weight: Font.Medium
                                    color: Theme.Colors.textSecondary
                                }

                                ComboBox {
                                    model: ["Standard (.lab)", "PDF", "JPEG", "PNG"]
                                    currentIndex: 0
                                    Layout.fillWidth: true
                                }

                            }

                            // Spacer
                            Item {
                                Layout.fillHeight: true
                            }

                        }

                    }

                    // Refresh button
                    ProsprButton {
                        text: "Refresh USB Devices"
                        buttonType: "secondary"
                        isOutlined: true
                        Layout.alignment: Qt.AlignCenter
                    }

                }

            }

            // Right column - Jobs to export
            Rectangle {
                Layout.fillWidth: true
                Layout.fillHeight: true
                color: Theme.Colors.white
                radius: 8

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 20
                    spacing: 20

                    // Section title with counter
                    RowLayout {
                        Layout.fillWidth: true

                        Text {
                            text: "Jobs to Export"
                            font.pixelSize: 18
                            font.weight: Font.Medium
                            color: Theme.Colors.secondary
                        }

                        Rectangle {
                            width: 30
                            height: 30
                            radius: 15
                            color: Theme.Colors.secondary

                            Text {
                                anchors.centerIn: parent
                                text: "3"
                                font.pixelSize: 14
                                color: Theme.Colors.white
                            }

                        }

                        // Spacer
                        Item {
                            Layout.fillWidth: true
                        }

                        // Add button
                        ProsprButton {
                            text: "Add Jobs"
                            buttonType: "secondary"
                            isOutlined: true
                        }

                    }

                    // Job list
                    ListView {
                        id: jobListView

                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        clip: true

                        model: ListModel {
                            ListElement {
                                name: "Product Label 001"
                                type: "Label"
                                copies: 1
                                size: "24 KB"
                                isSelected: true
                            }

                            ListElement {
                                name: "Shipping Label"
                                type: "Label"
                                copies: 1
                                size: "32 KB"
                                isSelected: true
                            }

                            ListElement {
                                name: "Batch Code Template"
                                type: "Template"
                                copies: 1
                                size: "15 KB"
                                isSelected: true
                            }

                        }

                        delegate: Rectangle {
                            width: ListView.view.width
                            height: 70
                            color: index % 2 === 0 ? "#f9f9f9" : "white"

                            RowLayout {
                                anchors.fill: parent
                                anchors.leftMargin: 15
                                anchors.rightMargin: 15
                                spacing: 15

                                // Selection checkbox
                                CheckBox {
                                    checked: model.isSelected
                                    onClicked: {
                                        model.isSelected = checked;
                                    }
                                }

                                // Job icon
                                Rectangle {
                                    width: 40
                                    height: 40
                                    radius: 4
                                    color: model.type === "Label" ? "#f04e2310" : "Theme.Colors.secondary10"

                                    Text {
                                        anchors.centerIn: parent
                                        text: model.type === "Label" ? "📄" : "📋"
                                        font.pixelSize: 16
                                    }

                                }

                                // Job details
                                ColumnLayout {
                                    Layout.fillWidth: true
                                    spacing: 3

                                    Text {
                                        text: model.name
                                        font.pixelSize: 14
                                        font.weight: Font.Medium
                                        color: Theme.Colors.secondary
                                    }

                                    Text {
                                        text: model.type + " • " + model.size
                                        font.pixelSize: 12
                                        color: Theme.Colors.textSecondary
                                    }

                                }

                                // Copies
                                RowLayout {
                                    spacing: 5

                                    Text {
                                        text: "Copies:"
                                        font.pixelSize: 14
                                        color: Theme.Colors.textSecondary
                                    }

                                    SpinBox {
                                        from: 1
                                        to: 100
                                        value: model.copies
                                        onValueChanged: {
                                            model.copies = value;
                                        }
                                    }

                                }

                                // Remove button
                                Rectangle {
                                    width: 36
                                    height: 36
                                    radius: 18
                                    color: Theme.Colors.transparent

                                    Text {
                                        anchors.centerIn: parent
                                        text: "✕"
                                        font.pixelSize: 16
                                        color: Theme.Colors.textSecondary
                                    }

                                    MouseArea {
                                        anchors.fill: parent
                                        cursorShape: Qt.PointingHandCursor
                                    }

                                }

                            }

                        }

                        ScrollBar.vertical: ScrollBar {
                        }

                    }

                    // Export actions
                    RowLayout {
                        Layout.fillWidth: true
                        spacing: 15

                        // Spacer
                        Item {
                            Layout.fillWidth: true
                        }

                        // Cancel button
                        ProsprButton {
                            text: "Cancel"
                            buttonType: "secondary"
                            isOutlined: true
                            onClicked: NavigationManager.go("Home")
                        }

                        // Export button
                        ProsprButton {
                            text: "Export to USB"
                            buttonType: "primary"
                            enabled: false // Enable when USB connected
                        }

                    }

                }

            }

        }

    }

}
