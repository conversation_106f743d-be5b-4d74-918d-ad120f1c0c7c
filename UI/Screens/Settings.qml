import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Theme" as Theme

Item {
    // Grid of settings options
    GridLayout {
        anchors.fill: parent
        anchors.margins: Theme.Spacing.screenMargin
        columns: 3
        rowSpacing: Theme.Spacing.cardPadding
        columnSpacing: Theme.Spacing.cardPadding

        // Date & Time
        CardButton {
            Layout.fillHeight: true
            Layout.fillWidth: true
            Layout.minimumHeight: Theme.Spacing.cardButtonMinHeight
            text: "Date & Time"
            iconSource: PathResolver.resolveAsset("Images/calendar.png")
            onClicked: NavigationManager.go("settings/DateTime")
        }

        // Users & Permissions
        CardButton {
            Layout.fillHeight: true
            Layout.fillWidth: true
            Layout.minimumHeight: Theme.Spacing.cardButtonMinHeight
            text: "Users & Permissions"
            iconSource: PathResolver.resolveAsset("Images/user-tick.png")
            onClicked: NavigationManager.go("settings/Users")
        }

        // Language
        CardButton {
            Layout.fillHeight: true
            Layout.fillWidth: true
            Layout.minimumHeight: Theme.Spacing.cardButtonMinHeight
            text: "Language"
            iconSource: PathResolver.resolveAsset("Images/language-circle.png")
            onClicked: NavigationManager.go("settings/Language")
        }

        // System Information
        CardButton {
            Layout.fillHeight: true
            Layout.fillWidth: true
            Layout.minimumHeight: Theme.Spacing.cardButtonMinHeight
            text: "System Information"
            iconSource: PathResolver.resolveAsset("Images/information.png")
            onClicked: NavigationManager.go("settings/SystemInfo")
        }

        // QR Data
        CardButton {
            Layout.fillHeight: true
            Layout.fillWidth: true
            Layout.minimumHeight: Theme.Spacing.cardButtonMinHeight
            text: "QR Data"
            iconSource: PathResolver.resolveAsset("Images/qr-code-02.png")
            onClicked: NavigationManager.go("settings/QRData")
        }

        // Other
        CardButton {
            Layout.fillHeight: true
            Layout.fillWidth: true
            Layout.minimumHeight: Theme.Spacing.cardButtonMinHeight
            text: "Other"
            iconSource: PathResolver.resolveAsset("Images/menu.png")
            onClicked: NavigationManager.go("settings/Other")
        }

    }

}
