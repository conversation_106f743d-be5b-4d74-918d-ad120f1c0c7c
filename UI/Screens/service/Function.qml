import QtQuick 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Theme" as Theme
import Backend.PrinterManager 1.0

Item {
    id: root

    signal operationRequested(string operationId, string operationTitle)

    anchors.fill: parent
    onOperationRequested: {
        // TODO temporary solution, the item shouldn't know anything about mainLayout
        mainLayout.popupVisible = true;
        operationPopupLoader.operationId = operationId
        operationPopupLoader.dialogTitle = `${operationTitle} in Progress`;
        operationPopupLoader.active = true;
    }

    Item {
        id: contentArea

        anchors.fill: parent

        ColumnLayout {
            anchors.fill: parent
            anchors.topMargin: Theme.Spacing.large
            spacing: Theme.Spacing.large

            Text {
                Layout.alignment: Qt.AlignHCenter
                text: "Function"
                font.family: Theme.Typography.primaryFontFamily
                font.pixelSize: Theme.Typography.formTitleText
                font.bold: true
            }

            Column {
                Layout.alignment: Qt.AlignHCenter
                Layout.fillHeight: true
                spacing: 50

                Repeater {
                    model: PrinterManager.functionModel

                    delegate: OperationControl {
                        title: model.title
                        checked: model.isActive
                        canStart: model.canStart
                        remainingTime: model.remainingTime

                        onOnClicked: {
                            if (PrinterManager.startFunction(model.functionId)) {
                                // TODO for the demo purposes
                                if (model.functionId === "cleanNozzle") {
                                    operationRequested(model.functionId, model.title);
                                }
                            }
                        }
                        onOffClicked: {
                            PrinterManager.stopFunction(model.functionId);
                        }
                    }
                }
            }
        }
    }

    BlurOverlay {
        anchors.fill: contentArea
        source: contentArea
        visible: operationPopupLoader.active
    }

    Loader {
        id: operationPopupLoader

        property string dialogTitle: ""
        property string operationId: ""

        anchors.centerIn: parent
        active: false
        source: PathResolver.resolveComponent("ProgressDialog.qml")
        onLoaded: {
            operationPopupLoader.item.title = operationPopupLoader.dialogTitle;
        }

        Connections {
            function onStop() {
                PrinterManager.stopFunction(operationPopupLoader.operationId)
                operationPopupLoader.active = false;
                mainLayout.popupVisible = false;
            }

            target: operationPopupLoader.item
        }

    }

    // Custom component that can be loaded in a ListView
    component OperationControl: Rectangle {
        id: control

        property string title: "Option"
        property bool checked: false
        property bool canStart: true
        property int remainingTime: 0

        signal onClicked()
        signal offClicked()

        implicitWidth: 1100
        implicitHeight: 150
        color: Theme.Colors.white
        radius: Theme.Radius.item

        RowLayout {
            anchors.fill: parent
            anchors.margins: 50
            spacing: 30

            // Radio button indicator (circle)
            Rectangle {
                id: radioIndicator

                width: 50
                height: 50
                radius: width / 2
                border.width: 12
                border.color: control.checked ? Theme.Colors.green : Theme.Colors.lightGrey
                color: "transparent"
            }

            // Title text
            Text {
                text: control.title
                Layout.fillWidth: true
                font.pixelSize: 42
                font.family: Theme.Typography.button.family
                font.weight: Theme.Typography.button.weight
            }

            // ETA text
            Text {
                text: `ETA: ${control.remainingTime} s`
                font.pixelSize: 42
                font.family: Theme.Typography.button.family
                font.weight: Theme.Typography.button.weight
                visible: control.checked && control.remainingTime > 0
            }

            // ON/OFF Buttons
            Row {
                spacing: 30

                DefaultButton {
                    text: "ON"
                    implicitWidth: 200
                    implicitHeight: 54
                    backgroundRectangle.color: Theme.Colors.darkBlue
                    enabled: control.canStart && !control.checked
                    onClicked: control.onClicked()
                }

                DefaultButton {
                    text: "OFF"
                    implicitWidth: 200
                    implicitHeight: 54
                    backgroundRectangle.color: Theme.Colors.red
                    enabled: control.checked
                    onClicked: control.offClicked()
                }
            }
        }
    }
}
