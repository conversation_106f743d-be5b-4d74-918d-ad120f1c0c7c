import QtQuick 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Theme" as Theme
import Backend.PrinterManager 1.0

Item {
    id: statusScreen

    Column {
        anchors.fill: parent
        anchors.topMargin: Theme.Spacing.large
        spacing: Theme.Spacing.large

        Text {
            anchors.horizontalCenter: parent.horizontalCenter
            text: "Status"
            font.family: Theme.Typography.primaryFontFamily
            font.pixelSize: Theme.Typography.formTitleText
            font.bold: true
        }

        GridLayout {
            anchors.horizontalCenter: parent.horizontalCenter
            columnSpacing: Theme.Spacing.large
            rowSpacing: Theme.Spacing.large
            columns: 3

            Repeater {
                model: PrinterManager.statusModel

                delegate: CardStatus {
                    caption: model.caption
                    value: {
                        // Special formatting for viscosity to show + for positive values
                        if (model.name === "viscosity") {
                            var val = model.value
                            return (val >= 0 ? "+" : "") + " " + val
                        }
                        return model.formattedValue
                    }

                    backgroundGradientColor: {
                        switch(model.level) {
                            case ServiceStatusLevel.Warning: return Theme.Colors.orange || "#FFA500"  // Warning level
                            case ServiceStatusLevel.Critical: return Theme.Colors.red || "#FF4500" // Critical level
                            default: return 'transparent'
                        }
                    }
                }
            }
        }
    }
}
