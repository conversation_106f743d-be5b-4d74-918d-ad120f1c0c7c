import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

Item {
    function action(name) {
        if (name === "save")
            toast.show();

    }

    ToastPopup {
        id: toast

        text: "Save Changes"

        anchors {
            right: parent.right
            top: parent.top
            margins: 20
        }

    }

    TitledFrame {
        anchors.centerIn: parent
        implicitWidth: 1100
        implicitHeight: 500
        title: "Phase"

        Column {
            spacing: 30

            anchors {
                fill: parent
                leftMargin: Theme.Spacing.large
                rightMargin: Theme.Spacing.large
            }

            Column {
                spacing: 10

                anchors {
                    left: parent.left
                    right: parent.right
                }

                Text {
                    text: "Phase (Volts)"
                    font.pixelSize: Theme.Typography.h2
                    font.family: Theme.Typography.secondaryFontFamily
                    color: Theme.Colors.black
                }

                LightInput {
                    id: usernameInput

                    anchors.left: parent.left
                    anchors.right: parent.right
                    font.family: Theme.Typography.secondaryFontFamily
                    font.pixelSize: Theme.Typography.h2
                    text: `${progressBar.progress * 10}`
                }

            }

            RowLayout {
                anchors.left: parent.left
                anchors.right: parent.right
                spacing: 30

                // Minus button
                DefaultButton {
                    Layout.preferredHeight: 160
                    Layout.preferredWidth: 160
                    backgroundRectangle.radius: height / 2
                    backgroundRectangle.color: Theme.Colors.addOpacity(Theme.Colors.primary, 0.2)
                    backgroundRectangle.border.color: Theme.Colors.primary
                    backgroundRectangle.border.width: 1
                    image.source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_MINUS)
                    image.sourceSize.width: width / 4
                    onClicked: {
                        if (progressBar.progress > 0)
                            --progressBar.progress;

                    }
                }

                SectionedProgressBar {
                    id: progressBar

                    sections: 9
                    progress: 4
                    Layout.fillWidth: true
                }

                DefaultButton {
                    Layout.preferredHeight: 160
                    Layout.preferredWidth: 160
                    backgroundRectangle.radius: height / 2
                    backgroundRectangle.color: Theme.Colors.addOpacity(Theme.Colors.primary, 0.2)
                    backgroundRectangle.border.color: Theme.Colors.primary
                    backgroundRectangle.border.width: 1
                    icon.source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_PLUS)
                    image.sourceSize.width: width / 4
                    onClicked: {
                        if (progressBar.progress < progressBar.sections)
                            ++progressBar.progress;

                    }
                }

            }

        }

    }

    // This component is used in Phase screen only, so it's inline.
    // But, inline components can't access shared JS libraries, so imported CONST can't be used in the component
    component SectionedProgressBar: RowLayout {
        id: control

        property int sections: 10
        property int progress: 4

        implicitWidth: 500
        implicitHeight: 100
        spacing: 10

        // Progress sections
        Repeater {
            model: control.sections

            Rectangle {
                property bool isFilled: index < control.progress

                Layout.fillWidth: true
                height: 20
                radius: height / 2
                color: isFilled ? Theme.Colors.primary : Theme.Colors.addOpacity(Theme.Colors.primary, 0.2)
                border.color: Theme.Colors.addOpacity(Theme.Colors.primary, 0.5)
                border.width: isFilled ? 0 : 1
            }

        }

    }

}
