import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

Item {
    function action(name) {
        if (name === "save")
            toast.show();

    }

    anchors.fill: parent

    ToastPopup {
        id: toast

        text: "Save Changes"

        anchors {
            right: parent.right
            top: parent.top
            margins: 20
        }

    }

    Column {
        anchors.fill: parent
        anchors.topMargin: Theme.Spacing.large
        spacing: Theme.Spacing.large

        Text {
            anchors.horizontalCenter: parent.horizontalCenter
            text: "Service Mode"
            font.family: Theme.Typography.primaryFontFamily
            font.pixelSize: Theme.Typography.formTitleText
            font.bold: true
        }

        Row {
            anchors.horizontalCenter: parent.horizontalCenter
            spacing: parent.spacing

            GridLayout {
                columnSpacing: Theme.Spacing.large
                rowSpacing: Theme.Spacing.large
                columns: 2

                CardStatus {
                    caption: "Pressure (Bars)"
                    value: "20"
                }

                CardStatus {
                    caption: "Pump Speed (RPM)"
                    value: "100.3"
                }

                CardStatus {
                    caption: "Viscosity Target (%)"
                    value: "95"
                }

                CardStatus {
                    caption: "High Voltage"
                    value: "34"
                }

            }

            Column {
                spacing: Theme.Spacing.large

                Row {
                    spacing: parent.spacing

                    CardSwitch {
                        text: "Light"
                        icon.source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_BRIGHTNESS)
                    }

                    CardSwitch {
                        text: "Dark"
                        icon.source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_NIGHT_MODE)
                    }

                }

                SwitchControl {
                    text: "Fill Machine"
                }

                SwitchControl {
                    text: "Empty Machine"
                }

                DefaultButton {
                    anchors.horizontalCenter: parent.horizontalCenter
                    text: "RESET MAINTENANCE"
                    onClicked: NavigationManager.go("service/ResetMaintenance")
                }

            }

        }

    }
}
