import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

Item {
    layer.enabled: true

    TitledFrame {
        anchors.centerIn: parent
        implicitWidth: 1100
        implicitHeight: 500
        title: "Reset Maintenance"

        Column {
            spacing: 50

            anchors {
                fill: parent
                leftMargin: Theme.Spacing.large
                rightMargin: Theme.Spacing.large
            }

            Column {
                spacing: 10

                anchors {
                    left: parent.left
                    right: parent.right
                }

                Text {
                    text: "Enter Code"
                    font.pixelSize: Theme.Typography.h2
                    font.family: Theme.Typography.secondaryFontFamily
                    color: Theme.Colors.black
                }

                LightInput {
                    id: usernameInput

                    anchors.left: parent.left
                    anchors.right: parent.right
                    font.family: Theme.Typography.secondaryFontFamily
                    font.pixelSize: Theme.Typography.h2
                }

            }

            DefaultButton {
                anchors.horizontalCenter: parent.horizontalCenter
                text: "RESET"
                font.weight: Theme.Typography.weightBold

                onClicked: resetPopup.open()
            }

        }
    }

    BlurOverlay {
        anchors.fill: parent
        source: parent
        visible: resetPopup.visible
    }

    QPopup {
        id: resetPopup

        confirmationText: "Successful reset"
        button1Text: "OK"
        numberOfButtons: 1
        onButton1Pressed: {
            close();
        }

        onOpened: {
            mainLayout.popupVisible = true
        }

        onClosed: {
            mainLayout.popupVisible = false;
        }
    }

}
