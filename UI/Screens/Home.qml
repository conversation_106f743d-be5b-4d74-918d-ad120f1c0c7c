import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Assets"
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: homeScreen

    property var shutdownDialog: shutdownDialogLoader.item

    anchors.fill: parent
    color: "transparent"

    Image {
        source: PathResolver.resolveAsset(CONST.ASSET_PATH.BG_HOME)
        anchors.fill: parent
        z: -1
    }

    Item {
        id: logoContainer

        width: Math.min(parent.width, 1600) // 826
        height: Math.min(parent.height, 800) // 411
        anchors.centerIn: parent

        Column {
            anchors.centerIn: parent
            spacing: 20

            Image {
                id: logoImage

                source: PathResolver.resolveAsset(CONST.ASSET_PATH.LOGO_PROSPR)
                width: Math.min(parent.width * 1.1, 1300) // 400
                height: Math.min(parent.height * 0.8, 600) // 220
                fillMode: Image.PreserveAspectFit
                anchors.horizontalCenter: parent.horizontalCenter
                layer.enabled: true

                layer.effect: DropShadow {
                    transparentBorder: true
                    horizontalOffset: 0
                    verticalOffset: 8
                    radius: 16
                    samples: 25
                    color: Theme.Colors.logoDropShadow
                }

            }

            Rectangle {
                width: Math.min(parent.width * 0.3, 400)
                height: 2
                color: Theme.Colors.white
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.topMargin: 5
                anchors.bottomMargin: 5
            }

            Text {
                text: "MARK AMBITIOUSLY."
                font.pixelSize: 16
                font.letterSpacing: 3
                font.weight: Font.Bold
                color: Theme.Colors.white
                anchors.horizontalCenter: parent.horizontalCenter
            }

        }

    }

    Image {
        source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_POWER_BUTTON)
        width: Math.min(176, parent.width / 10)
        height: width

        anchors {
            right: parent.right
            bottom: parent.bottom
            rightMargin: width / 2
            bottomMargin: width / 2
        }

        MouseArea {
            anchors.fill: parent
            anchors.margins: -Theme.Spacing.small
            cursorShape: Qt.PointingHandCursor
            onClicked: {
                if (shutdownDialog)
                    shutdownDialog.open();
                else
                    console.error("Shutdown dialog is not loaded yet.");
            }
        }

    }

    Loader {
        id: shutdownDialogLoader

        source: PathResolver.resolveComponent("ShutdownDialog.qml")
        active: true
        anchors.fill: parent
        onLoaded: {
            item.countdownComplete.connect(function() {
                Qt.quit();
            });
        }
    }

}
