import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: insertDateTimeJulian

    color: "white"
    radius: Theme.Radius.xxxlarge

    GridLayout {
        columns: 3
        rows: 3
        rowSpacing: 20
        columnSpacing: 20
        anchors.fill: parent
        anchors.margins: 20

        ColumnLayout {
            Layout.columnSpan: 3
            spacing: Theme.Spacing.xssmall

            Text {
                text: "Date/Time"
                font.pixelSize: Theme.Typography.h2
                font.weight: Theme.Typography.weightMedium
                font.family: Theme.Typography.secondaryFontFamily
                color: Theme.Colors.black
            }

            LightInput {
                Layout.fillWidth: true
                placeholderText: "11"
                font.pixelSize: Theme.Typography.h2
            }

        }

        Repeater {
            model: 6

            ColumnLayout {
                spacing: Theme.Spacing.xssmall

                Text {
                    text: "Time 0" + (index + 1)
                    font.pixelSize: Theme.Typography.h2
                    font.weight: Theme.Typography.weightMedium
                    font.family: Theme.Typography.secondaryFontFamily
                    color: Theme.Colors.black
                }

                LightInput {
                    Layout.fillWidth: true
                    placeholderText: "11"
                    font.pixelSize: Theme.Typography.h2
                }

            }

        }

    }

}
