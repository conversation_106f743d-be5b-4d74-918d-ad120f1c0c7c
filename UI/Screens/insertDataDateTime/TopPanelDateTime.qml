import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Theme" as Theme

// Print Preview Panel
Rectangle {
    id: insertDataTopPanel

    // Defined property for the preview content
    property string currentPreviewContent: "Select a file to view print preview"

    // Signal when preview is updated
    signal previewUpdated()

    // Function to update preview when a file is selected
    function updatePreview(fileName, lotNum, bestByDateValue) {
        previewContent.text = "Previewing file: " + fileName;
        lotNumber.text = lotNum;
        bestByDate.text = bestByDateValue;
        lotNumber.visible = true;
        bestByDate.visible = true;
        previewUpdated();
    }

    color: Theme.Colors.white
    radius: Theme.Radius.large

    ColumnLayout {
        // Placeholder for the actual preview content
        anchors.left: parent.left
        anchors.bottom: parent.bottom
        anchors.margins: 30
        Layout.alignment: Qt.AlignLeft | Qt.AlignBottom

        Text {
            id: previewContent

            text: "Select a file to view print preview"
            font.pixelSize: 14
            color: Theme.Colors.secondary
            Layout.fillWidth: true
            visible: false
        }

        Text {
            id: lotNumber

            text: "LOT #12345"
            font.pixelSize: 30
            font.weight: Font.Bold
            color: Theme.Colors.black
            visible: true // Will show when a file is selected
        }

        Text {
            id: bestByDate

            text: "BEST BUY 02/17/26"
            font.pixelSize: 30
            font.weight: Font.Bold
            color: Theme.Colors.black
            visible: true // Will show when a file is selected
        }

    }

}
