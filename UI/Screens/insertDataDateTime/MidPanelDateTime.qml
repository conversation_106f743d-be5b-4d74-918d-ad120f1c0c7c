import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Theme" as Theme

// Middle Panel Julian-Override
Rectangle {
    id: middlePanelSwitch

    property bool julianClicked: true

    width: 993
    height: 98
    radius: 10
    clip: true
    color: "transparent"

    Rectangle {
        width: parent.width / 2
        height: parent.height
        anchors.left: parent.left
        anchors.leftMargin: 8
        border.width: 2
        border.color: Theme.Colors.borderColor
        color: julianClicked ? Theme.Colors.darkBlue : Theme.Colors.lightGrey

        Text {
            anchors.centerIn: parent
            color: julianClicked ? Theme.Colors.white : Theme.Colors.black
            text: "JULIAN"
            font.pixelSize: Theme.Typography.h2
            font.weight: Theme.Typography.weightMedium
            font.family: Theme.Typography.primaryFontFamily
        }

        MouseArea {
            anchors.fill: parent
            onClicked: {
                julianClicked = true;
            }
        }

    }

    Rectangle {
        width: parent.width / 2
        height: parent.height
        anchors.right: parent.right
        anchors.rightMargin: 8
        border.width: 2
        border.color: Theme.Colors.borderColor
        color: julianClicked ? Theme.Colors.lightGrey : Theme.Colors.darkBlue

        Text {
            anchors.centerIn: parent
            color: julian<PERSON>licked ? Theme.Colors.black : Theme.Colors.white
            text: "OVERRIDE"
            font.pixelSize: Theme.Typography.h1
            font.weight: Theme.Typography.weightMedium
            font.family: Theme.Typography.primaryFontFamily
        }

        MouseArea {
            anchors.fill: parent
            onClicked: {
                julianClicked = false;
            }
        }

    }

}
