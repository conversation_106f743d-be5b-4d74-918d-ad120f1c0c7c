import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Theme" as Theme

// Main print layout component that organizes the three panels
Item {
    id: counterLayout

    signal navigateToRequested(string to, string from)

    // Main content area with shadow and rounded corners
    Rectangle {
        id: mainContent

        anchors.fill: parent
        anchors.margins: 20
        color: "transparent" // Use transparent since parent already has gradient
        radius: Theme.Radius.large

        // Main layout with three panels
        Item {
            anchors.fill: parent

            // Top Panel - Print Preview
            TopPanelDateTime {
                id: topPanel

                height: parent.height * 0.3

                anchors {
                    top: parent.top
                    left: parent.left
                    right: parent.right
                }

            }

            MidPanelDateTime {
                id: midPanel

                height: parent.height * 0.1

                anchors {
                    top: topPanel.bottom
                    left: parent.left
                    topMargin: 20
                    leftMargin: 20
                }

            }

            BottomPanelJulian {
                id: julianPanel

                visible: midPanel.julianClicked
                height: parent.height * 0.55

                anchors {
                    top: midPanel.bottom
                    topMargin: 20
                    left: parent.left
                    right: parent.right
                }

            }

            BottomPanelOverride {
                id: overridePanel

                visible: !julianPanel.visible
                height: parent.height * 0.55

                anchors {
                    top: midPanel.bottom
                    topMargin: 20
                    left: parent.left
                    right: parent.right
                }

            }

        }

    }

}
