import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: insertDateTimeOverride

    color: "white"
    radius: Theme.Radius.xxxlarge

    GridLayout {
        columns: 4
        rows: 3
        rowSpacing: 20
        columnSpacing: 20
        anchors.fill: parent
        anchors.margins: 20

        Repeater {
            model: 12

            ColumnLayout {
                Layout.fillWidth: true
                spacing: Theme.Spacing.xssmall

                Text {
                    text: {
                        switch (index) {
                        case 0:
                            return "Monday";
                        case 1:
                            return "Tuesday";
                        case 2:
                            return "Wednesday";
                        case 3:
                            return "Thursday";
                        case 4:
                            return "Friday";
                        case 5:
                            return "Saturday";
                        case 6:
                            return "Sunday";
                        case 7:
                            return "Month";
                        case 8:
                            return "Year";
                        case 9:
                            return "Hour";
                        case 10:
                            return "Minute";
                        case 11:
                            return "End of Week";
                        default:
                            return "";
                        }
                    }
                    font.pixelSize: Theme.Typography.h2
                    font.weight: Theme.Typography.weightMedium
                    font.family: Theme.Typography.secondaryFontFamily
                    color: Theme.Colors.black
                    horizontalAlignment: Text.AlignLeft
                    Layout.maximumWidth: 120
                }

                LightInput {
                    Layout.fillWidth: true
                    placeholderText: "11"
                    font.pixelSize: Theme.Typography.h2
                }

            }

        }

    }

}
