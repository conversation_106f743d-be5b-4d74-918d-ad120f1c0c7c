import Backend.UserManager 1.0
import QtQuick 2.15
pragma Singleton

QtObject {
    // Simple navigation history stack
    property var history: []
    property string current: "LogIn"

    signal navigateToRequested(string to, string from, var params)

    function go(to, from, params = {}) {
        console.log('[NavigationManager] go() called. Navigating from:', from, 'to:', to, 'Current before:', current, 'params: ', JSON.stringify(params));
        if (!to || typeof to !== "string" || to.trim() === "") {
            console.log('[NavigationManager] go() blocked due to empty to value:', to);
            console.warn("[NavigationManager] go() called with invalid or empty 'to' value:", to, "from:", from);
            return ;
        }
        console.log("[NavigationManager] go() called. Navigating from:", from, "to:", to, "Current before:", current);
        // Permission checks for restricted screens
        var guestRestrictedScreens = ["Service", "SettingsUsers", "SettingsOther", "SettingsQRData"];
        if (UserManager && UserManager.currentUser.role === "Guest" && guestRestrictedScreens.indexOf(to) !== -1) {
            console.warn("Navigation blocked: " + UserManager.currentUser.role + " does not have permission to access " + to);
            return ;
        }
        // Push current screen to history if not initial
        if (current && current !== to)
            history.push(current);

        current = to;
        console.log('[NavigationManager] go() after update. Current:', current, 'History:', JSON.stringify(history));
        console.log("[NavigationManager] go() after update. Current:", current, "History:", JSON.stringify(history));
        console.log("[NavigationManager] Emitting navigateToRequested signal with to:", to, "from:", from);
        navigateToRequested(to, from, params);
    }

    function goHome(from) {
        console.log('[NavigationManager] goHome() called. Resetting history and navigating to Home.');
        history = [];
        current = 'Home';
        navigateToRequested('Home', from, {});
    }

    function goBack() {
        console.log('[NavigationManager] goBack() called. Current:', current, 'History:', JSON.stringify(history));
        console.log("[NavigationManager] goBack() called. Current:", current, "History:", JSON.stringify(history));
        if (history.length > 0) {
            var previous = history.pop();
            var from = current;
            current = previous;
            console.log("[NavigationManager] goBack() navigating back to:", previous, "from:", from, "History after pop:", JSON.stringify(history));
            console.log("[NavigationManager] Emitting navigateToRequested signal with to:", previous, "from:", from);
            navigateToRequested(previous, from, {});
        } else {
            console.warn("NavigationManager: No previous screen in history stack");
        }
    }

    function showErrorLog() {
        console.log('[NavigationManager] showErrorLog() called. Navigating to ErrorLog.');
        go('ErrorLog', '', {});
    }

    // TODO Showing wroing screen
    function showUserProfile() {
        console.log('[NavigationManager] showUserProfile() called. Navigating to UserProfile.');
        go('UserProfile', '', {});
    }

}
