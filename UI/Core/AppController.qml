import QtQuick 2.15

// Central application controller to manage app state
QtObject {
    id: appController

    // Current application state
    property bool isInitialized: false

    // Signal for app initialization complete
    signal initialized()

    // Initialize the application
    function initialize() {
        if (!isInitialized) {
            console.debug("Initializing application...");
            isInitialized = true;
            initialized();
            console.debug("Application initialized");
        }
    }

    // Log the application state
    function logAppState() {
        console.debug("=== Application State ===");
        console.debug("Initialized: " + isInitialized);
    }

}
