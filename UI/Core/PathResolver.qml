/**
 * PathResolver.qml
 *
 * A singleton for resolving resource paths consistently across different platforms.
 * Handles the differences between desktop development environment (qrc:/) and
 * embedded T507 target environment (file:///).
 */

import QtQuick 2.15
pragma Singleton

QtObject {
    // For future implementation:
    // We could add fallback paths here if needed
    // Example: try a different directory if the primary fails
    // Additional Linux-specific detection can be implemented here
    // This could check for specific files or hardware that exists only on the T507
    // Example: Check for a specific T507 device file (theoretical)
    // In practice, you'd need to determine what files or properties are unique to the T507
    // try {
    //     var xhr = new XMLHttpRequest();
    //     xhr.open("GET", "file:///proc/device-tree/model", false);
    //     xhr.send(null);
    //     if (xhr.responseText && xhr.responseText.indexOf("Allwinner T507") >= 0) {
    //         return true;
    //     }
    // } catch (e) {
    //     // File doesn't exist or can't be read - not on T507
    // }

    id: pathResolver

    readonly property bool isEmbedded: detectEmbeddedPlatform()
    readonly property string desktopPrefix: "qrc:/"
    // For embedded targets, use /opt/prospr-light as the root for all QML and assets
    readonly property string embeddedPrefix: "file:///opt/prospr-light/"
    property bool debugMode: true
    property var pathCache: ({
    })
    // Stores the last missing file path for error display
    property string lastMissingFile: ""

    function getCurrentPrefix() {
        return isEmbedded ? embeddedPrefix : desktopPrefix;
    }

    /**
     * Resolves a path based on the current platform.
     *
     * @param {string} relativePath - The relative path to resolve.
     * @return {string} The resolved absolute path appropriate for the current platform.
     */
    function resolvePath(relativePath) {
        if (!relativePath) {
            console.warn("PathResolver: Attempted to resolve null or undefined path");
            return "";
        }
        // Check cache first for performance
        if (pathCache[relativePath] !== undefined)
            return pathCache[relativePath];

        // Skip resolution for already fully resolved paths for the current platform
        if (relativePath.startsWith(desktopPrefix) && !isEmbedded) {
            pathCache[relativePath] = relativePath;
            return relativePath;
        }
        if (relativePath.startsWith(embeddedPrefix) && isEmbedded) {
            pathCache[relativePath] = relativePath;
            return relativePath;
        }
        // Strip any existing prefixes first to normalize the path
        var cleanPath = relativePath.replace(/^(qrc:|file:\/\/\/root\/|file:\/\/\/opt\/prospr-light\/|\.\/|\/root\/|\/opt\/prospr-light\/)/i, "");
        // Ensure we don't have double slashes
        while (cleanPath.startsWith("/"))cleanPath = cleanPath.substring(1)
        var resolvedPath = (isEmbedded ? embeddedPrefix : desktopPrefix) + cleanPath;
        // On embedded, check if file exists and log error if not
        if (isEmbedded) {
            try {
                var xhr = new XMLHttpRequest();
                xhr.open("HEAD", resolvedPath, false);
                xhr.send(null);
                if (xhr.status !== 200) {
                    lastMissingFile = resolvedPath;
                    console.error("PathResolver: Missing file on target: " + resolvedPath);
                } else {
                    lastMissingFile = "";
                }
            } catch (e) {
                lastMissingFile = resolvedPath;
                console.error("PathResolver: Error checking file existence: " + resolvedPath + " (" + e + ")");
            }
        }
        if (debugMode)
            console.debug("PathResolver: Resolving '" + relativePath + "' to '" + resolvedPath + "'");

        pathCache[relativePath] = resolvedPath;
        return resolvedPath;
    }

    /**
     * Try multiple resolution methods if needed.
     * This is a more robust version that tries different path variations if necessary.
     *
     * @param {string} relativePath - The relative path to resolve.
     * @return {string} The resolved absolute path.
     */
    function tryMultipleResolutionMethods(relativePath) {
        // First try standard resolution
        var primaryPath = resolvePath(relativePath);
        // If on embedded, we could try additional fallback paths
        if (isEmbedded) {
        }
        return primaryPath;
    }

    /**
     * Resolves an asset path (images, icons, etc.)
     *
     * @param {string} assetPath - The asset path relative to the Assets directory.
     * @return {string} The fully resolved asset path.
     */
    function resolveAsset(assetPath) {
        return resolvePath("UI/Assets/" + assetPath);
    }

    /**
     * Resolves a component path.
     *
     * @param {string} componentPath - The component path relative to the Components directory.
     * @return {string} The fully resolved component path.
     */
    function resolveComponent(componentPath) {
        return resolvePath("UI/Components/" + componentPath);
    }

    /**
     * Resolves a screen path.
     *
     * @param {string} screenPath - The screen path relative to the Screens directory.
     * @return {string} The fully resolved screen path.
     */
    function resolveScreen(screenPath) {
        return resolvePath("UI/Screens/" + screenPath);
    }

    /**
     * Clears the path resolution cache.
     * Call this when resources change or to free memory.
     */
    function clearCache() {
        pathCache = {
        };
        if (debugMode)
            console.debug("PathResolver: Cache cleared");

    }

    /**
     * Detect if running on embedded platform.
     * Uses multiple detection methods for reliability.
     *
     * @return {bool} True if running on embedded platform, false otherwise.
     */
    function detectEmbeddedPlatform() {
        // Method 1: Check for command line arguments
        for (var i = 0; i < Qt.application.arguments.length; i++) {
            if (Qt.application.arguments[i] === "--embedded" || Qt.application.arguments[i] === "--aarch64" || Qt.application.arguments[i] === "--target") {
                if (debugMode)
                    console.debug("PathResolver: Detected embedded platform via command line args");

                return true;
            }
        }
        // Method 2: Check for environment variables
        try {
            // Note: Qt.env may not be available in all Qt versions
            // This is a theoretical check - you'll need to test if it works
            if (Qt.application.environment) {
                var envVars = Qt.application.environment;
                if (envVars.indexOf("QT_EMBEDDED=1") >= 0 || envVars.indexOf("QT_AARCH64_TARGET=1") >= 0 || envVars.indexOf("QT_FORCE_DIRECT_ASSET_LOADING=1") >= 0) {
                    if (debugMode)
                        console.debug("PathResolver: Detected embedded platform via environment variables");

                    return true;
                }
            }
        } catch (e) {
            if (debugMode)
                console.debug("PathResolver: Error checking environment variables: " + e);

        }
        // Method 3: Check for specific system properties
        if (Qt.platform.os === "linux") {
        }
        // Default to desktop mode
        return false;
    }

    Component.onCompleted: {
        if (debugMode) {
            console.debug("PathResolver: Platform detection - isEmbedded: " + isEmbedded);
            console.debug("PathResolver: Using prefix: " + getCurrentPrefix());
        }
    }
}
