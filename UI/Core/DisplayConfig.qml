import Backend.ConfigManager 1.0
import QtQuick 2.15
import QtQuick.Window 2.15

// Display configuration helper for properly scaling the UI
// Specially optimized for embedded targets like the T507
QtObject {
    //getVisibilityMode()

    id: displayConfig

    // Reference to platform helpers
    property var platform
    // Target resolution - what the app is designed for
    readonly property int targetWidth: ConfigManager.windowWidth
    readonly property int targetHeight: ConfigManager.windowHeight
    // Actual screen dimensions (what's available)
    readonly property int actualWidth: ConfigManager.windowWidth
    readonly property int actualHeight: ConfigManager.windowHeight
    // Device pixel ratio - critical for proper scaling
    readonly property real devicePixelRatio: 1
    //getDevicePixelRatio()
    // Scale factor to apply to the UI
    readonly property real scaleFactor: 1
    //getScaleFactor()
    // Window visibility mode
    readonly property int visibilityMode: Window.Windowed

    // Get target width with priority order for different scenarios
    function getTargetWidth() {
        return 1920;
        // Priority 0: Use ConfigManager if available
        if (typeof ConfigManager !== 'undefined' && ConfigManager.windowWidth > 0) {
            console.debug("Using ConfigManager.windowWidth: " + ConfigManager.windowWidth);
            return ConfigManager.windowWidth;
        }
        // Priority 0: Use ConfigManager if available
        if (typeof ConfigManager !== 'undefined' && ConfigManager.windowWidth > 0) {
            console.debug("Using ConfigManager.windowWidth: " + ConfigManager.windowWidth);
            return ConfigManager.windowWidth;
        }
        // Existing logic follows...
        if (!platform.isEmbedded && !platform.isAarch64Target) {
            console.debug("Forcing width=1920 for desktop development");
            return 1920;
        }
        if (platform.isEmbedded || platform.isAarch64Target) {
            console.debug("Forcing width=1920 for aarch64 target");
            return 1920;
        }
        var envWidth = platform.getNumericEnvVar("DISPLAY_WIDTH", 0);
        if (envWidth > 0) {
            console.debug("Using environment width: " + envWidth);
            return envWidth;
        }
        for (var i = 0; i < Qt.application.arguments.length; i++) {
            if (Qt.application.arguments[i] === "--width" && i + 1 < Qt.application.arguments.length) {
                var cmdWidth = parseInt(Qt.application.arguments[i + 1]);
                if (!isNaN(cmdWidth) && cmdWidth > 0) {
                    console.debug("Using command line width: " + cmdWidth);
                    return cmdWidth;
                }
            }
        }
        if (Screen.width > 0) {
            console.debug("Using screen width: " + Screen.width);
            return Screen.width;
        }
        console.debug("Using fallback width: 1920");
        return 1920;
    }

    // Get target height with similar priority order
    function getTargetHeight() {
        return 1080;
        // Priority 0: Use ConfigManager if available
        if (typeof ConfigManager !== 'undefined' && ConfigManager.windowHeight > 0) {
            console.debug("Using ConfigManager.windowHeight: " + ConfigManager.windowHeight);
            return ConfigManager.windowHeight;
        }
        // Priority 0: Use ConfigManager if available
        if (typeof ConfigManager !== 'undefined' && ConfigManager.windowHeight > 0) {
            console.debug("Using ConfigManager.windowHeight: " + ConfigManager.windowHeight);
            return ConfigManager.windowHeight;
        }
        // Existing logic follows...
        if (!platform.isEmbedded && !platform.isAarch64Target) {
            console.debug("Forcing height=1080 for desktop development");
            return 1080;
        }
        if (platform.isEmbedded || platform.isAarch64Target) {
            console.debug("Forcing height=1080 for aarch64 target");
            return 1080;
        }
        var envHeight = platform.getNumericEnvVar("DISPLAY_HEIGHT", 0);
        if (envHeight > 0) {
            console.debug("Using environment height: " + envHeight);
            return envHeight;
        }
        for (var i = 0; i < Qt.application.arguments.length; i++) {
            if (Qt.application.arguments[i] === "--height" && i + 1 < Qt.application.arguments.length) {
                var cmdHeight = parseInt(Qt.application.arguments[i + 1]);
                if (!isNaN(cmdHeight) && cmdHeight > 0) {
                    console.debug("Using command line height: " + cmdHeight);
                    return cmdHeight;
                }
            }
        }
        if (Screen.height > 0) {
            console.debug("Using screen height: " + Screen.height);
            return Screen.height;
        }
        console.debug("Using fallback height: 1080");
        return 1080;
    }

    // Get device pixel ratio - critical for scaling
    function getDevicePixelRatio() {
        // For aarch64/embedded target, always force 1.0 to avoid scaling issues
        if (platform.isEmbedded || platform.isAarch64Target) {
            console.debug("FORCING device pixel ratio to 1.0 for embedded/aarch64");
            return 1;
        }
        // Try to get from environment (useful for testing)
        var envDpr = platform.getNumericEnvVar("QT_SCALE_FACTOR", 0);
        if (envDpr > 0) {
            console.debug("Using environment device pixel ratio: " + envDpr);
            return envDpr;
        }
        // Get from screen if available
        if (Screen.devicePixelRatio > 0) {
            console.debug("Using screen device pixel ratio: " + Screen.devicePixelRatio);
            return Screen.devicePixelRatio;
        }
        // Default fallback
        return 1;
    }

    // Get UI scale factor
    function getScaleFactor() {
        // Priority 0: Use ConfigManager if available and > 0
        if (typeof ConfigManager !== 'undefined' && ConfigManager.windowWidth > 0 && ConfigManager.windowHeight > 0) {
            // Calculate scale based on config window size vs. design size
            var scaleW = ConfigManager.windowWidth / 1920;
            var scaleH = ConfigManager.windowHeight / 1080;
            var scale = Math.min(scaleW, scaleH);
            // Clamp between 0.5 and 1.2 for usability
            var clamped = Math.min(Math.max(scale, 0.5), 1.2);
            console.debug("Using ConfigManager-based scale factor: " + clamped);
            return clamped;
        }
        // Priority 0: Use ConfigManager if available and > 0
        if (typeof ConfigManager !== 'undefined' && ConfigManager.windowWidth > 0 && ConfigManager.windowHeight > 0) {
            // Calculate scale based on config window size vs. design size
            var scaleW = ConfigManager.windowWidth / 1920;
            var scaleH = ConfigManager.windowHeight / 1080;
            var scale = Math.min(scaleW, scaleH);
            // Clamp between 0.5 and 1.2 for usability
            var clamped = Math.min(Math.max(scale, 0.5), 1.2);
            console.debug("Using ConfigManager-based scale factor: " + clamped);
            return clamped;
        }
        // Existing logic follows...
        if (platform.isEmbedded || platform.isAarch64Target) {
            console.debug("HARDCODED scale factor 1.0 for embedded/aarch64");
            return 1;
        }
        var envScale = platform.getNumericEnvVar("QT_SCALE_FACTOR", 0);
        if (envScale > 0) {
            console.debug("Using environment scale factor: " + envScale);
            return envScale;
        }
        var screenRatio = Math.min(Screen.width / 1920, Screen.height / 1080);
        if (screenRatio > 0) {
            var calcScale = Math.min(Math.max(screenRatio, 0.5), 1.2);
            console.debug("Using calculated scale factor: " + calcScale);
            return calcScale;
        }
        return 1;
    }

    // Determine window visibility mode
    function getVisibilityMode() {
        // For embedded targets, use fullscreen
        if (platform.isEmbedded || platform.isAarch64Target)
            return Window.FullScreen;

        // For development, use windowed mode
        return Window.Windowed;
    }

    // Log display configuration
    function logDisplayConfig() {
        console.debug("=== Display Configuration ===");
        console.debug("Target resolution: " + targetWidth + "x" + targetHeight);
        console.debug("Actual screen: " + actualWidth + "x" + actualHeight);
        console.debug("Device pixel ratio: " + devicePixelRatio);
        console.debug("Scale factor: " + scaleFactor);
        console.debug("Visibility mode: " + visibilityMode);
    }

}
