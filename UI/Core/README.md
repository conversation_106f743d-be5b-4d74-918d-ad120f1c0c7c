# Core Components

This directory contains core application infrastructure components that handle platform-specific adaptations, resource loading, and other fundamental functions.

## Components

### PathResolver.qml

The `PathResolver` singleton provides a unified way to handle resource paths across different platforms (desktop development environment and T507 embedded target).

#### Features

- **Platform Detection**: Automatically detects whether the application is running on desktop or embedded target
- **Path Resolution**: Converts relative paths to absolute paths with appropriate prefixes
- **Specialized Resolution Methods**: Dedicated methods for assets, components, and screens
- **Caching**: Improves performance by caching resolved paths
- **Error Handling**: Robust error handling for missing or invalid paths
- **Debugging**: Detailed logging for path resolution (when debug mode is enabled)

#### Usage

```qml
import UI_new.Core 1.0

// Basic path resolution
Image {
    source: PathResolver.resolvePath("UI_new/Assets/icons/home-icon.svg")
}

// Specialized asset resolution
Image {
    source: PathResolver.resolveAsset("icons/home-icon.svg")
}

// Component loading
Loader {
    source: PathResolver.resolveComponent("ProsprButton.qml")
}

// Screen loading
Loader {
    source: PathResolver.resolveScreen("Home.qml")
}
```

For detailed usage guidelines, see `/docs/path_resolver_guidelines.md`.

### ContentLoader.qml

The `ContentLoader` component handles dynamic loading of application screens with error handling and fallbacks.

#### Features

- **Dynamic Screen Loading**: Loads screens based on requested screen name
- **Error Handling**: Provides graceful error handling when screens fail to load
- **Fallback Mechanism**: Shows fallback screens when loading fails
- **Platform Compatibility**: Uses PathResolver for cross-platform path resolution

#### Usage

```qml
ContentLoader {
    id: contentLoader
    appController: appController
}
```

### PlatformHelpers.qml

Provides platform-specific utility functions and properties.

### DisplayConfig.qml

Handles display configuration, scaling, and adaptation for different devices.

### AppController.qml

Manages application state, navigation, and screen transitions.

### FallbackScreen.qml

Provides a fallback UI when the main screen fails to load.

### KeyboardStyle.qml

Defines styling for the virtual keyboard on embedded devices.

## Development Guidelines

1. **Core components should be platform-agnostic**: Use PathResolver for resource references
2. **Maintain backwards compatibility**: Include fallback options for critical functionality
3. **Document public APIs**: All public functions and properties should be documented
4. **Add unit tests**: Create tests for new functionality
5. **Use proper error handling**: Anticipate and handle failure cases

For more information, see the project's main documentation.
