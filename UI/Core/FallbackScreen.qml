import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

// Emergency fallback screen when normal content loading fails
Rectangle {
    id: fallbackScreen

    // Properties
    property string errorMessage: "Error loading content"
    property string screenName: ""

    // Helper function to find the app controller
    function findAppController() {
        // Try to find it through parent relationships
        var current = parent;
        while (current) {
            if (current.appController)
                return current.appController;

            current = current.parent;
        }
        // Try to access via root object
        var root = findRootObject();
        if (root && root.appController)
            return root.appController;

        return null;
    }

    // Helper function to find the root object
    function findRootObject() {
        if (typeof rootWindow !== 'undefined')
            return rootWindow;

        var root = parent;
        while (root && root.parent)root = root.parent
        return root;
    }

    color: "#333333"

    ColumnLayout {
        anchors.centerIn: parent
        width: parent.width * 0.8
        spacing: 20

        Text {
            Layout.fillWidth: true
            horizontalAlignment: Text.AlignHCenter
            color: "#FFFFFF"
            font.pixelSize: 24
            font.bold: true
            text: "Application Error"
        }

        Text {
            Layout.fillWidth: true
            horizontalAlignment: Text.AlignHCenter
            wrapMode: Text.WordWrap
            color: "#FFFFFF"
            font.pixelSize: 18
            text: errorMessage
        }

        Text {
            Layout.fillWidth: true
            horizontalAlignment: Text.AlignHCenter
            wrapMode: Text.WordWrap
            color: "#CCCCCC"
            font.pixelSize: 16
            text: "Failed screen: " + screenName
            visible: screenName !== ""
        }

        Button {
            Layout.alignment: Qt.AlignHCenter
            text: "Return to Login"
            onClicked: {
                // Try to navigate back to login
                var appController = findAppController();
                if (appController) {
                    NavigationManager.go("LogIn", "");
                } else {
                    // Direct attempt if appController not found
                    var rootObject = findRootObject();
                    if (rootObject && rootObject.contentLoader)
                        rootObject.contentLoader.tryLoadScreen("LogIn");

                }
            }
        }

    }

}
