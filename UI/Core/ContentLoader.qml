/**
 * ContentLoader.qml
 *
 * Dynamic content loader with error handling and debug information.
 * Uses PathResolver to ensure consistent path resolution across platforms.
 */

import QtQuick 2.15
import QtQuick.Controls 2.15

// Dynamic content loader with error handling and debug information
Item {
    // Continue to fallback methods
    // Resource path (desktop)
    // File path with triple slash (embedded)
    // Relative path
    // Absolute path on target
    // Continue to the next attempt
    // Monitor app controller for screen changes

    id: contentLoader

    // Properties
    property string currentScreen: ""
    property bool debugMode: true
    property var appController
    // Internal state
    property bool isLoading: false
    property string lastError: ""

    // Signal when screen loading completes successfully
    signal loadingCompleted(string screenName)
    // Signal when screen loading fails
    signal loadingFailed(string screenName, string errorMessage)

    // Load a specific screen
    function tryLoadScreen(screenName) {
        console.debug("Attempting to load screen: " + screenName);
        // If screen doesn't end with .qml, add it
        if (!screenName.endsWith(".qml"))
            screenName += ".qml";

        // First try: Use PathResolver (preferred method)
        try {
            var resolvedPath = PathResolver.resolveScreen(screenName);
            console.debug("PathResolver resolved path: " + resolvedPath);
            screenLoader.source = resolvedPath;
            // If no error is thrown and the loader status is Ready or Loading,
            // then the load was successful
            if (screenLoader.status === Loader.Ready || screenLoader.status === Loader.Loading) {
                console.debug("Successfully loaded screen from resolved path: " + resolvedPath);
                return ;
            }
        } catch (e) {
            console.debug("PathResolver failed to load screen, trying fallback methods: " + e);
        }
        // Fallback Method: Try multiple paths in case PathResolver fails
        // This ensures compatibility with both host and embedded platforms
        var attempts = ["qrc:/UI/Screens/" + screenName, "qrc:/UI/Screens/" + screenName, "./UI/Screens/" + screenName, "/root/UI/Screens/" + screenName];
        // Try to load the screen from each path
        for (var i = 0; i < attempts.length; i++) {
            try {
                var fallbackUrl = Qt.resolvedUrl(attempts[i]);
                console.debug("Trying fallback path: " + fallbackUrl);
                screenLoader.source = fallbackUrl;
                // If the load succeeded (no error thrown),
                // and the loader status isn't Error, break the loop
                if (screenLoader.status !== Loader.Error && screenLoader.status !== Loader.Null) {
                    console.debug("Successfully loaded screen from fallback path: " + fallbackUrl);
                    return ;
                }
            } catch (e) {
                console.error("Error loading screen from " + attempts[i] + ": " + e);
            }
        }
        // If we get here, all attempts failed
        lastError = "Failed to load screen: " + screenName;
        console.error(lastError);
        loadingFailed(screenName, lastError);
        showFallbackScreen();
    }

    // Show a fallback screen when loading fails
    function showFallbackScreen() {
        // Use PathResolver for fallback screen
        try {
            var fallbackPath = PathResolver.resolvePath("UI/Core/FallbackScreen.qml");
            var fallbackComponent = Qt.createComponent(fallbackPath);
            if (fallbackComponent.status === Component.Ready) {
                screenLoader.sourceComponent = fallbackComponent.createObject(contentLoader);
                return ;
            }
        } catch (e) {
            console.debug("Failed to load fallback screen using PathResolver: " + e);
        }
        // First fallback: Try direct paths
        var fallbackPaths = ["qrc:/UI/Core/FallbackScreen.qml", "qrc:/UI/Core/FallbackScreen.qml", "./UI/Core/FallbackScreen.qml"];
        for (var i = 0; i < fallbackPaths.length; i++) {
            try {
                var component = Qt.createComponent(fallbackPaths[i]);
                if (component.status === Component.Ready) {
                    screenLoader.sourceComponent = component.createObject(contentLoader);
                    return ;
                }
            } catch (e) {
                console.debug("Failed to load fallback screen from " + fallbackPaths[i] + ": " + e);
            }
        }
        // Ultimate fallback - create a basic error screen inline
        try {
            var inlineComponent = Qt.createQmlObject('import QtQuick 2.15; import QtQuick.Controls 2.15; ' + 'Rectangle { color: "#333333"; Text { anchors.centerIn: parent; ' + 'color: "white"; text: "Failed to load screen: ' + currentScreen + '"; ' + 'font.pixelSize: 20; } }', contentLoader, "inlineFallback");
            screenLoader.sourceComponent = inlineComponent;
        } catch (e) {
            console.error("Failed to create inline fallback: " + e);
        }
    }

    // Create the actual loader as a child component
    Loader {
        id: screenLoader

        anchors.fill: parent
        asynchronous: false
        // Handle loader status changes
        onStatusChanged: {
            switch (status) {
            case Loader.Loading:
                isLoading = true;
                if (debugMode)
                    console.debug("Loading screen: " + source);

                break;
            case Loader.Ready:
                isLoading = false;
                if (debugMode)
                    console.debug("Screen loaded successfully: " + source);

                loadingCompleted(currentScreen);
                break;
            case Loader.Error:
                isLoading = false;
                lastError = "Failed to load screen: " + source;
                console.error(lastError);
                loadingFailed(currentScreen, lastError);
                showFallbackScreen();
                break;
            case Loader.Null:
                if (source !== "") {
                    isLoading = false;
                    lastError = "Screen became null: " + source;
                    console.error(lastError);
                    loadingFailed(currentScreen, lastError);
                }
                break;
            }
        }
    }

    // Loading indicator when changing screens
    BusyIndicator {
        id: loadingIndicator

        anchors.centerIn: parent
        running: isLoading
        visible: isLoading
    }

    // Error message display
    Rectangle {
        id: errorDisplay

        anchors.fill: parent
        color: "#88000000"
        visible: lastError !== "" && !isLoading

        Text {
            anchors.centerIn: parent
            color: "white"
            text: lastError
            font.pixelSize: 18
            width: parent.width * 0.8
            wrapMode: Text.WordWrap
            horizontalAlignment: Text.AlignHCenter
        }

        MouseArea {
            anchors.fill: parent
            onClicked: {
                lastError = "";
                tryLoadScreen("LogIn");
            }
        }

    }

}
