import QtQuick 2.15
import QtQuick.Window 2.15 // Required for Screen

// Platform detection and environment helpers
QtObject {
    id: platformHelpers

    // Platform detection
    readonly property bool isLinux: Qt.platform.os === "linux"
    readonly property bool isMacOS: Qt.platform.os === "osx"
    readonly property bool isWindows: Qt.platform.os === "windows"
    // Embedded mode detection - multiple methods for reliability
    readonly property bool isEmbedded: {
        // Method 1: Command line argument
        for (var i = 0; i < Qt.application.arguments.length; i++) {
            if (Qt.application.arguments[i] === "--embedded") {
                console.debug("Detected embedded mode via command line arg");
                return true;
            }
        }
        // Method 2: Environment variable
        try {
            if (parseInt(Qt.env.QT_EMBEDDED) === 1) {
                console.debug("Detected embedded mode via QT_EMBEDDED env var");
                return true;
            }
        } catch (e) {
        }
        // Method 3: Screen size heuristic (typically embedded screens are smaller)
        var totalPixels = Screen.width * Screen.height;
        if (totalPixels < 800000 && !isMacOS && !isWindows) {
            console.debug("Detected embedded mode via screen size heuristic");
            return true;
        }
        return false;
    }
    // Aarch64 target detection - multiple methods for reliability
    readonly property bool isAarch64Target: {
        // Method 1: Command line argument
        for (var i = 0; i < Qt.application.arguments.length; i++) {
            if (Qt.application.arguments[i] === "--aarch64") {
                console.debug("Detected aarch64 via command line args");
                return true;
            }
        }
        // Method 2: Environment variable
        try {
            if (parseInt(Qt.env.QT_AARCH64_TARGET) === 1) {
                console.debug("Detected aarch64 via QT_AARCH64_TARGET env var");
                return true;
            }
        } catch (e) {
        }
        // Method 3: Check TARGET_ARCH env var
        try {
            if (Qt.env.TARGET_ARCH === "aarch64") {
                console.debug("Detected aarch64 via TARGET_ARCH env var");
                return true;
            }
        } catch (e) {
        }
        return false;
    }

    // Safe environment variable access
    function getEnvVar(name, defaultValue) {
        try {
            var value = Qt.env[name];
            return value !== undefined ? value : defaultValue;
        } catch (e) {
            console.debug("Error reading env var " + name + ": " + e);
            return defaultValue;
        }
    }

    // Get numeric environment variable with fallback
    function getNumericEnvVar(name, defaultValue) {
        try {
            if (typeof Qt.env === "object" && Qt.env !== null && Qt.env[name] !== undefined) {
                var value = parseFloat(Qt.env[name]);
                return !isNaN(value) ? value : defaultValue;
            }
            // If env var is not set, just return default without error
            return defaultValue;
        } catch (e) {
            console.debug("Error reading numeric env var " + name + ": " + e);
            return defaultValue;
        }
    }

    // Log platform information for debugging
    function logPlatformInfo() {
        console.debug("=== Platform Information ===");
        console.debug("OS: " + Qt.platform.os);
        console.debug("Embedded mode: " + isEmbedded);
        console.debug("Aarch64 target: " + isAarch64Target);
        console.debug("Screen dimensions: " + Screen.width + "x" + Screen.height);
        console.debug("Screen DPI: " + Screen.pixelDensity * 25.4);
        console.debug("Device pixel ratio: " + Screen.devicePixelRatio);
    }

}
