import "Components/bottombar/BottomBarConfigs.js" as BottomBarConfigs
import "qrc:/UI/Core/constants.js" as CONST
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Theme" as Theme

Item {
    // signal navigateToRequested(string to, string from)

    id: mainLayout

    // Properties for layout configuration
    property bool isEmbeddedMode: false
    property real scaleFactor: isEmbeddedMode ? 1 : 1.2
    property string currentScreen: NavigationManager.current
    property bool popupVisible: false
    // navButtons property: always up-to-date with currentScreen
    property var navButtons: {
        return BottomBarConfigs.getNavButtonsForScreen(currentScreen);
    }

    // Helper function to determine if back button should be shown
    function isBackButtonScreen(screen) {
        var screens = ["ErrorLog", "PrinterSettings", "FileManager", "CreateEditMessage", "InsertText"];
        screens = screens.concat(["settings/DateTime", "settings/Users", "settings/AddUser", "settings/UserPermissions", "settings/Language", "settings/SystemInfo", "settings/QRData", "settings/Other"]);
        return screens.indexOf(screen) >= 0;
    }

    // Helper function to determine if main buttons should be shown
    function isMainButtonsScreen(screen) {
        return screen === "Home" || screen === "Service" || screen === "Print" || screen === "Settings";
    }

    // Signal for navigation requests
    // Use NavigationManager for navigation and listen for theme changes
    Component.onCompleted: {
        NavigationManager.navigateToRequested.connect(function(screen, fromScreen, params) {
            console.log("[MainLayout] NavigationManager.navigateToRequested fired. Navigating to:", screen, "from:", fromScreen, "with params: ", JSON.stringify(params));
            mainLayout.currentScreen = screen;
            contentLoader.parameters = params;
            contentLoader.source = PathResolver.resolveScreen(screen + ".qml");
        });

        Theme.Theme.themeChanged.connect(function() {
            if (currentScreen !== "Home")
                contentArea.color = Theme.Colors.backgroundSecondary;

        });
    }
    onCurrentScreenChanged: {
        console.log("[MainLayout] currentScreen changed to:", currentScreen);
    }
    // NOTE: For this to work, ensure the window that loads this Item does NOT resize it to the physical screen size.
    width: isEmbeddedMode ? Screen.width : 1920
    height: isEmbeddedMode ? Screen.height : 1080
    implicitWidth: 1920
    implicitHeight: 1080

    Connections {
        function onNavigateToRequested(to, from) {
            // mainLayout.navigateToRequested(to, from); // emit signal
            NavigationManager.go(to, from);
        }

        target: contentLoader.item
        ignoreUnknownSignals: true
    }
    // Force fixed size for desktop development to match target touchscreen (1920x1080)

    // Main content area
    Rectangle {
        id: contentArea

        anchors.fill: parent
        anchors.bottomMargin: bottomBar.visible ? bottomBar.height : 0
        color: currentScreen === "Home" ? "transparent" : Theme.Colors.secondary

        Image {
            id: background

            anchors.fill: parent
            source: PathResolver.resolveAsset(CONST.ASSET_PATH.BG_SYSTEM)
        }

        // Content will be loaded dynamically
        Loader {
            id: contentLoader

            property var parameters: ({}) // parameters to pass to loaded item

            anchors.fill: parent
            source: PathResolver.resolveScreen(mainLayout.currentScreen + ".qml")

            onLoaded: {
                if (parameters) {
                    for (var key in parameters) {
                        if (item.hasOwnProperty(key)) {
                            item[key] = parameters[key];
                        }
                    }
                }

                console.log("[MainLayout] Loader loaded new screen:", mainLayout.currentScreen);
            }
        }

    }

    // Bottom bar (configurable based on current screen)
    BottomBar {
        id: bottomBar

        anchors.bottom: parent.bottom
        width: parent.width
        enabled: !mainLayout.popupVisible
        currentScreen: mainLayout.currentScreen
        navButtons: mainLayout.navButtons
        // Navigation: emit navigateToRequested, handled by NavigationManager
        onNavigateToRequested: function(to, from) {
            NavigationManager.go(to, from);
        }
        onAction: {
            if (contentLoader.item && typeof contentLoader.item.action === "function")
                contentLoader.item.action(actionName);

        }
    }

    BlurOverlay {
        anchors.fill: bottomBar
        source: bottomBar
        visible: popupVisible
    }

}
