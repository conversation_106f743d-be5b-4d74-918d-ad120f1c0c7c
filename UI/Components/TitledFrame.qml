import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: root

    property alias title: titleText.text
    property alias titleHorizontalAlignment: titleText.horizontalAlignment
    default property alias content: contentContainer.data
    property alias titleFontFamily: titleText.font.family
    property alias titleFontPixelSize: titleText.font.pixelSize
    property alias titleFontBold: titleText.font.bold
    property alias titleColor: titleText.color

    implicitHeight: 400
    implicitWidth: 400
    radius: Theme.Radius.item

    Text {
        id: titleText

        text: "CHANGE ME!"
        font.family: Theme.Typography.primaryFontFamily
        font.pixelSize: Theme.Typography.h1
        font.bold: true
        color: Theme.Colors.secondary
        horizontalAlignment: titleHorizontalAlignment
        height: 100

        anchors {
            top: parent.top
            left: parent.left
            right: parent.right
            margins: 20
        }

    }

    Item {
        id: contentContainer

        anchors {
            top: titleText.bottom
            left: root.left
            right: root.right
            bottom: root.bottom
        }

    }

}
