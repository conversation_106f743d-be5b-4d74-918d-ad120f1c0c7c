import QtGraphicalEffects 1.14
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Components/bottombar/BottomBarConfigs.js" as BottomBarConfigs
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme
import Backend.PrinterManager 1.0
import Backend.Error 1.0


Rectangle {
    id: bottomBar

    // Configurable API
    property var navButtons: []
    property bool notificationsAvailable: false // Controls warning icon color
    property string currentScreen: ""

    // --- API: Signals for navigation/actions ---
    signal navigateToRequested(string to, string from)
    signal action(string actionName)
    signal backPopupRequested()
    signal saveChangesPopupRequested()

    width: parent.width
    height: 110
    color: "#FFFFFF"
    border.color: "#C8C8C8"
    border.width: 1

    // Left and Middle Groups (Status Indicators and Nav Buttons)
    RowLayout {
        width: parent.width * 0.57 // 7% for Status Indicators + 50% for Nav Buttons
        height: parent.height
        spacing: 0

        // Status Indicators Group (7% width, left-aligned)
        Item {
            width: parent.width * (0.07 / 0.57) // Proportional to 7% of total width
            height: parent.height

            RowLayout {
                anchors.left: parent.left
                anchors.leftMargin: 15
                anchors.verticalCenter: parent.verticalCenter
                spacing: 5

                Rectangle {
                    width: 28
                    height: 28
                    radius: 30
                    color: PrinterManager.statusIndicators[0] ? Theme.Colors.statusGreen : Theme.Colors.statusMidGrey
                }

                Rectangle {
                    width: 28
                    height: 28
                    radius: 30
                    color: PrinterManager.statusIndicators[1] ? Theme.Colors.statusYellow : Theme.Colors.statusMidGrey
                }

                Rectangle {
                    width: 28
                    height: 28
                    radius: 30
                    color: PrinterManager.statusIndicators[2] ? Theme.Colors.statusRed : Theme.Colors.statusMidGrey
                }

            }

        }

        // Nav Buttons Group (50% width, left-aligned)
        Item {
            width: parent.width * (0.6 / 0.57) // Proportional to 50% of total width
            height: parent.height

            RowLayout {
                anchors.fill: parent
                spacing: 15

                // Actual nav buttons
                Repeater {
                    id: navButtonsRepeater

                    model: bottomBar.navButtons

                    delegate: Rectangle {
                        id: navBtn

                        Layout.fillWidth: true
                        Layout.maximumWidth: 500
                        height: bottomBar.height * 0.65
                        color: {
                            if (modelData.label === "BACK")
                                return Theme.Colors.white;

                            return bottomBar.currentScreen === modelData.screen ? Theme.Colors.primary : Theme.Colors.secondary;
                        }
                        border.width: color === Theme.Colors.white ? 1 : 0
                        border.color: Theme.Colors.borderColor
                        radius: 21

                        RowLayout {
                            anchors.fill: parent
                            anchors.leftMargin: 24
                            anchors.rightMargin: 24
                            spacing: Theme.Spacing.none
                            Layout.alignment: Qt.AlignVCenter | Qt.AlignHCenter

                            Item {
                                Layout.alignment: Qt.AlignVCenter | Qt.AlignHCenter
                                Layout.fillWidth: true
                                height: parent.height

                                Row {
                                    anchors.centerIn: parent

                                    Image {
                                        visible: modelData.icon !== ""
                                        source: visible ? PathResolver.resolveAsset(CONST.ASSET_PATH.ICONS_BASE + modelData.icon) : ""
                                        anchors.verticalCenter: parent.verticalCenter
                                    }

                                    Text {
                                        text: modelData.label
                                        color: navBtn.color === Theme.Colors.white ? Theme.Colors.black : Theme.Colors.white
                                        font.pixelSize: 32
                                        font.family: Theme.Typography.button.family
                                        font.weight: Theme.Typography.button.weight
                                        verticalAlignment: Text.AlignVCenter
                                        leftPadding: 12
                                        anchors.verticalCenter: parent.verticalCenter
                                    }

                                }

                            }

                        }

                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                if (typeof modelData.action !== "undefined") {
                                    bottomBar.action(modelData.action);
                                } else if (modelData.label === "BACK" && currentScreen === CONST.SCREEN.PRINT_CREATE) {
                                    //open backPopup
                                    backPopupRequested();
                                } else if (modelData.label === "SAVE" && currentScreen === CONST.SCREEN.PRINT_SETTINGS) {
                                    //open saveChangesPopup
                                    saveChangesPopupRequested();
                                } else {
                                    console.log('[BottomBar] Button clicked: emitting navigateToRequested to:', modelData.screen, 'from:', bottomBar.currentScreen);
                                    bottomBar.navigateToRequested(modelData.screen, bottomBar.currentScreen);
                                }
                            }
                            hoverEnabled: true
                            cursorShape: Qt.PointingHandCursor
                        }

                    }

                }

            }

        }

    }

    // Right Icons and Date/Time Group (40% width, fixed right-aligned)
    Item {
        width: parent.width * 0.4
        height: parent.height
        anchors.right: parent.right
        anchors.rightMargin: 15

        RowLayout {
            anchors.right: parent.right
            anchors.verticalCenter: parent.verticalCenter
            spacing: Theme.Spacing.xxsmall
            Layout.alignment: Qt.AlignRight | Qt.AlignVCenter

            // Warning Icon
            Item {
                width: 70
                height: 70
                Layout.alignment: Qt.AlignVCenter

                Image {
                    anchors.fill: parent
                    anchors.topMargin: 8.75
                    anchors.bottomMargin: 8.75
                    anchors.leftMargin: 5.833
                    anchors.rightMargin: 5.833
                    source: PathResolver.resolveAsset(
                        (ErrorManager.hasErrors || ErrorManager.hasPendingErrors)
                            ? CONST.ASSET_PATH.ICON_WARNING_YELLOW
                            : CONST.ASSET_PATH.ICON_WARNING_GREY
                    )
                    fillMode: Image.PreserveAspectFit
                }

                MouseArea {
                    anchors.fill: parent
                    cursorShape: Qt.PointingHandCursor
                    onClicked: NavigationManager.showErrorLog()
                }

            }

            // User Icon
            Item {
                width: 70
                height: 70
                Layout.alignment: Qt.AlignVCenter

                Image {
                    anchors.fill: parent
                    anchors.topMargin: 8.75
                    anchors.bottomMargin: 8.75
                    anchors.leftMargin: 5.833
                    anchors.rightMargin: 5.833
                    source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_USER)
                    fillMode: Image.PreserveAspectFit
                }

                MouseArea {
                    anchors.fill: parent
                    cursorShape: Qt.PointingHandCursor
                    onClicked: NavigationManager.go("LogIn", "")
                }

            }

            // Home Icon
            Item {
                width: 70
                height: 70
                Layout.alignment: Qt.AlignVCenter

                Image {
                    anchors.fill: parent
                    anchors.topMargin: 8.75
                    anchors.bottomMargin: 8.75
                    anchors.leftMargin: 5.833
                    anchors.rightMargin: 5.833
                    source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_HOME)
                    fillMode: Image.PreserveAspectFit
                }

                MouseArea {
                    anchors.fill: parent
                    cursorShape: Qt.PointingHandCursor
                    onClicked: {
                        console.log('[BottomBar] Home icon clicked: navigating to Home (reset history)');
                        NavigationManager.goHome(bottomBar.currentScreen);
                    }
                }

            }

            // Date/Time
            ColumnLayout {
                spacing: Theme.Spacing.none
                Layout.alignment: Qt.AlignVCenter

                Text {
                    text: "2025/06/07"
                    color: Theme.Colors.darkGrey
                    font.pixelSize: 24
                    font.family: Theme.Typography.button.family
                    font.weight: Font.Medium
                    horizontalAlignment: Text.AlignRight
                }

                Text {
                    text: "14:32" + "PM"
                    color: Theme.Colors.darkGrey
                    font.pixelSize: 24
                    font.family: Theme.Typography.button.family
                    font.weight: Font.Medium
                    horizontalAlignment: Text.AlignRight
                }

            }

        }

    }

}
