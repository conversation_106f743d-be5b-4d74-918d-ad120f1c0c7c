import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

// Shutdown dialog with countdown
Popup {
    id: shutdownDialog

    // Properties
    property int countdownTime: 10
    property int currentCount: countdownTime

    // Signal emitted when countdown completes
    signal countdownComplete()

    // Dialog properties
    modal: true
    width: 800
    height: 600
    closePolicy: Popup.NoAutoClose
    padding: 20
    // Explicit centering
    x: (parent.width - width) / 2
    y: (parent.height - height) / 2
    // Start countdown when dialog is opened
    onOpened: {
        shutdownDialog.currentCount = shutdownDialog.countdownTime; // Reset counter
        countdownTimer.start();
    }

    // Timer for countdown
    Timer {
        id: countdownTimer

        interval: 1000
        repeat: true
        onTriggered: {
            shutdownDialog.currentCount -= 1;
            if (shutdownDialog.currentCount <= 0) {
                stop();
                shutdownDialog.countdownComplete();
            }
        }
    }

    background: Item {
    }

    contentItem: TitledFrame {
        title: "Shutting Down..."

        Column {
            anchors.fill: parent
            spacing: 20

            // Countdown message
            Text {
                anchors.horizontalCenter: parent.horizontalCenter
                text: "Machine will shut down in"
                font.pixelSize: 28
                font.family: Theme.Typography.secondaryFontFamily
            }

            // Countdown number
            Text {
                anchors.horizontalCenter: parent.horizontalCenter
                height: 240
                text: shutdownDialog.currentCount
                font.pixelSize: 120
                font.family: Theme.Typography.primaryFontFamily
                font.bold: true
                color: Theme.Colors.primary
                verticalAlignment: Text.AlignVCenter
            }

            // Cancel button
            Control {
                anchors.horizontalCenter: parent.horizontalCenter
                implicitWidth: 300
                implicitHeight: 100

                MouseArea {
                    anchors.fill: parent
                    cursorShape: Qt.PointingHandCursor
                    onClicked: {
                        countdownTimer.stop();
                        shutdownDialog.close();
                        shutdownDialog.currentCount = shutdownDialog.countdownTime; // Reset counter
                    }
                }

                background: Rectangle {
                    color: Theme.Colors.red
                    radius: 20
                }

                contentItem: Item {
                    Row {
                        anchors.centerIn: parent
                        spacing: 4

                        Image {
                            anchors.verticalCenter: parent.verticalCenter
                            source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_FORBIDDEN)
                        }

                        Text {
                            text: "CANCEL"
                            font.pixelSize: 30
                            font.family: Theme.Typography.secondaryFontFamily
                            color: Theme.Colors.white
                            anchors.verticalCenter: parent.verticalCenter
                        }

                    }

                }

            }

        }

    }

}
