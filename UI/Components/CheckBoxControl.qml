import QtQuick 2.15
import QtQuick.Controls 2.15
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

CheckBox {
    id: control

    property real indicatorRadius: Theme.Radius.large

    implicitWidth: 50
    implicitHeight: 50

    indicator: Rectangle {
        anchors.fill: parent
        radius: control.indicatorRadius
        border.width: 2
        border.color: control.checked ? Theme.Colors.primary : Theme.Colors.borderColor
        color: control.checked ? Theme.Colors.primary : Theme.Colors.white

        Image {
            source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_CHECKMARK)
            anchors.centerIn: parent
        }

    }

}
