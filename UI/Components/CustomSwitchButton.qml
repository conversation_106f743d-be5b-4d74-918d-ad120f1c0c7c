import QtGraphicalEffects 1.15
import QtQuick 2.15
import QtQuick.Controls 2.15
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

Rectangle {
    property alias imageSource: halfImg.source
    property alias buttonText: textField.text

    radius: Theme.Radius.xxmlarge
    color: invertSwitch.checked ? Theme.Colors.orange : "transparent"
    border.color: Theme.Colors.orange
    border.width: 1

    // Colored circle with gradient
    Rectangle {
        width: parent.width / 2.5
        height: parent.width / 2.5
        radius: parent.width / 2.5
        border.color: Theme.Colors.orange
        border.width: 1

        anchors {
            top: parent.top
            horizontalCenter: parent.horizontalCenter
            margins: 30
        }

        Image {
            id: halfImg

            source: PathResolver.resolveAsset(CONST.ASSET_PATH.IMAGE_HALF)
            anchors.centerIn: parent

            ColorOverlay {
                anchors.fill: parent
                source: halfImg
                color: invertSwitch.checked ? Theme.Colors.white : Theme.Colors.black
            }

        }

        gradient: Gradient {
            GradientStop {
                position: 0
                color: invertSwitch.checked ? "#f74b1b" : "#ffe1d9"
            }

            GradientStop {
                position: 1
                color: invertSwitch.checked ? "#ff5c1a" : "#ffffff"
            }

        }

    }

    Text {
        id: textField

        text: "Invert"
        anchors.bottom: parent.bottom
        anchors.horizontalCenter: parent.horizontalCenter
        color: invertSwitch.checked ? Theme.Colors.white : Theme.Colors.black
        font.pixelSize: 46
        bottomPadding: 22
    }

    Switch {
        id: invertSwitch

        checked: false

        anchors {
            top: parent.top
            right: parent.right
            margins: 15
        }

        indicator: Rectangle {
            implicitWidth: 48
            implicitHeight: 26
            radius: 13
            color: Theme.Colors.white
            border.color: Theme.Colors.orange
            border.width: 1

            Rectangle {
                width: 20
                height: 20
                radius: 11
                color: Theme.Colors.orange
                anchors.verticalCenter: parent.verticalCenter
                x: invertSwitch.checked ? parent.width - width - 2 : 2
            }

        }

    }

}
