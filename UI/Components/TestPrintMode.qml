import QtGraphicalEffects 1.15
import QtQuick 2.15
import "qrc:/UI/Theme" as Theme

Rectangle {
    signal closePopup()

    anchors.fill: parent
    color: "transparent"

    Rectangle {
        width: 1000
        height: 478
        radius: 40
        color: Theme.Colors.white
        anchors.centerIn: parent

        Text {
            id: textMode

            text: "TEST PRINT MODE"
            font.pixelSize: 46
            font.family: Theme.Fonts.youngSerif

            anchors {
                top: parent.top
                left: parent.left
                margins: 30
            }

        }

        Rectangle {
            id: separatorHeader

            width: parent.width
            height: 2
            color: Theme.Colors.black

            anchors {
                top: textMode.bottom
                left: parent.left
                topMargin: 10
            }

        }

        Text {
            id: descriptionText

            text: "Test printing dialog...\n-\n-\n-"
            font.pixelSize: 28
            font.weight: Font.DemiBold
            color: Theme.Colors.black

            anchors {
                top: separatorHeader.bottom
                left: parent.left
                margins: 30
            }

        }

        Rectangle {
            id: separatorDescription

            width: parent.width
            height: 2
            color: Theme.Colors.black

            anchors {
                top: descriptionText.bottom
                left: parent.left
                topMargin: 30
            }

        }

        Rectangle {
            id: contentPlaceholder

            width: 610
            height: 98
            color: Theme.Colors.primary
            opacity: 0.15
            radius: 20

            anchors {
                top: separatorDescription.bottom
                left: parent.left
                margins: 30
            }

        }

        Rectangle {
            id: dynamicRectangle

            width: (parseFloat(contentText.text) / 100) * (contentPlaceholder.width - 20) // Calculate width based on percentage
            height: contentPlaceholder.height - 20
            color: Theme.Colors.primary
            radius: contentPlaceholder.radius

            anchors {
                verticalCenter: contentPlaceholder.verticalCenter
                left: contentPlaceholder.left
                margins: 10
            }

        }

        Text {
            id: contentText

            anchors.centerIn: contentPlaceholder
            font.pixelSize: 20
            text: "40%"
            font.weight: Font.DemiBold
            color: Theme.Colors.black
        }

        LeftPanelButton {
            id: actionButton

            width: 300
            color: Theme.Colors.red
            text: "STOP"

            anchors {
                top: separatorDescription.bottom
                right: parent.right
                margins: 30
            }

            MouseArea {
                anchors.fill: parent
                onClicked: closePopup()
            }

        }

    }

}
