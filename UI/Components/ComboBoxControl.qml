import QtQuick 2.15
import QtQuick.Controls 2.15
import QtGraphicalEffects 1.15
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

ComboBox {
    id: control

    property color backgroundColor: "#FCFCFC"
    property color textColor: Theme.Colors.black
    property color borderColor: Theme.Colors.lightGrey
    property int borderWidth: 1
    property int radius: 20

    implicitWidth: 300
    implicitHeight: 78
    font.pixelSize: Theme.Typography.h2
    editable: false

    contentItem: Text {
        leftPadding: 20
        text: control.displayText
        font: control.font
        color: control.textColor
        verticalAlignment: Text.AlignVCenter
    }

    background: Rectangle {
        color: control.backgroundColor
        radius: control.radius
        border.width: control.borderWidth
        border.color: control.borderColor
    }

    indicator: Image {
        source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_CHEVRON_DOWN)

        anchors {
            right: control.background.right
            rightMargin: 20
            verticalCenter: parent.verticalCenter
        }

        // Fallback if image is missing
        Text {
            anchors.centerIn: parent
            visible: parent.status !== Image.Ready
            text: "▼"
            font.pixelSize: Theme.Typography.h4
            color: Theme.Colors.borderColor
        }

    }

    popup: Popup {
        id: popup

        y: control.height + 10
        width: control.width
        height: contentItem.implicitHeight
        padding: 0

        contentItem: ListView {
            clip: true
            layer.enabled: true
            layer.effect: OpacityMask {
                maskSource: Rectangle {
                    width: popup.width
                    height: popup.height
                    radius: control.radius
                }
            }

            implicitHeight: contentHeight
            model: control.popup.visible ? control.delegateModel : null
            currentIndex: control.highlightedIndex

            ScrollIndicator.vertical: ScrollIndicator { }
        }

        background: Rectangle {
            color: control.backgroundColor
            border.width: control.borderWidth
            border.color: control.borderColor
            radius: control.radius
        }
    }

    delegate: ItemDelegate {
        id: delegate

        required property var model
        required property int index

        padding: 20
        width: control.width
        contentItem: Text {
            text: delegate.model.modelData[control.textRole]
            color: control.textColor
            font: control.font
            elide: Text.ElideRight
            verticalAlignment: Text.AlignVCenter
        }
        highlighted: control.highlightedIndex === index
    }
}
