import QtQuick 2.15
import QtQuick.Controls 2.15
import "qrc:/UI/Theme" as Theme

TextField {
    id: control

    // Custom properties for styling
    property color backgroundColor: "#FCFCFC"
    property color borderColor: Theme.Colors.lightGrey
    property int borderWidth: 1
    property int radius: 20
    property bool isValid: control.acceptableInput
    property string errorMessage: ""
    property bool _visited: false

    // Text input properties
    placeholderTextColor: Theme.Colors.textGrey
    color: Theme.Colors.black
    font.pixelSize: 18
    font.family: "Roboto"
    verticalAlignment: TextInput.AlignVCenter
    // Size and padding
    implicitHeight: 78
    leftPadding: 20
    rightPadding: 20

    // Error message
    Text {
        id: errorText

        visible: control._visited && !control.isValid && control.errorMessage !== ""
        text: control.errorMessage
        color: Theme.Colors.red
        font.pixelSize: Theme.Typography.helperText
        font.family: Theme.Typography.secondaryFontFamily
        anchors.top: control.bottom
        anchors.topMargin: 4
        anchors.left: control.left
    }

    // Background styling
    background: Rectangle {
        color: control.backgroundColor
        radius: control.radius
        border.width: control.borderWidth
        border.color: !control._visited || control.isValid ? control.borderColor : Theme.Colors.red
    }

    // Animations for focus state
    states: State {
        name: "focused"
        when: control.activeFocus && !control.readOnly && (!control._visited || control.isValid)

        PropertyChanges {
            target: control.background
            border.width: 2
            border.color: Theme.Colors.primary
        }

    }

    transitions: Transition {
        from: ""
        to: "focused"
        reversible: true

        PropertyAnimation {
            properties: "border.width, border.color"
            duration: 200
        }

    }

    onActiveFocusChanged: {
        if (!activeFocus) {
            control._visited = true
        }
    }

}
