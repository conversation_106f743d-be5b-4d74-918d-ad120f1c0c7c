import QtQuick 2.15
import QtQuick.Controls 2.15
import "qrc:/UI/Theme" as Theme

// A scrollable container that ensures text fields remain visible when virtual keyboard appears
Flickable {
    // Already visible, no scrolling needed
    // Child is above viewport, scroll up
    // Child is below viewport, scroll down

    id: scrollableContainer

    // Scroll properties
    property int scrollMargin: 20
    property bool scrollToSelectedChild: true

    // Function to ensure a child (like a text field) is visible when keyboard appears
    function ensureChildVisible(child) {
        if (!scrollToSelectedChild || !child)
            return ;

        // Calculate position of child relative to container
        var childPos = child.mapToItem(contentItem, 0, 0);
        var childHeight = child.height;
        // Get current scroll position
        var viewportTop = contentY;
        var viewportBottom = contentY + height;
        var childTop = childPos.y;
        var childBottom = childPos.y + childHeight;
        // Check if child is already fully visible
        if (childTop >= viewportTop && childBottom <= viewportBottom)
            return ;

        // Calculate new scroll position
        if (childTop < viewportTop)
            contentY = Math.max(0, childTop - scrollMargin);
        else
            contentY = Math.min(contentHeight - height, childBottom - height + scrollMargin);
    }

    // Default properties
    contentWidth: width
    clip: true
    boundsBehavior: Flickable.StopAtBounds
    flickableDirection: Flickable.VerticalFlick

    // Scrollbar for touch interaction
    ScrollBar.vertical: ScrollBar {
        id: verticalScrollBar

        policy: ScrollBar.AsNeeded
        width: 10
        active: true
        interactive: true
    }

}
