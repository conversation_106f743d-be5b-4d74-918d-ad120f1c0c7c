import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: toastPopup

    property alias text: toastText.text
    property int duration: 2000 // milliseconds

    function show() {
        visible = true;
        timer.restart();
    }

    visible: false
    width: Math.max(330, toastText.implicitWidth + 40)
    height: Math.max(toastText.implicitHeight + 24, 64)
    color: Theme.Colors.addOpacity(Theme.Colors.green, 0.2)
    radius: Theme.Radius.xmlarge
    border.color: Theme.Colors.green
    opacity: 0.95

    Row {
        anchors.centerIn: parent
        spacing: Theme.Spacing.xssmall

        Rectangle {
            width: 34
            height: 34
            radius: Theme.Radius.round
            color: Theme.Colors.green

            Image {
                anchors.centerIn: parent
                width: parent.width / 2
                fillMode: Image.PreserveAspectFit
                source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_CHECKMARK)
            }

        }

        Text {
            id: toastText

            text: "Save Changes"
            color: Theme.Colors.green
            font.pixelSize: Theme.Typography.h2
            font.family: Theme.Typography.secondaryFontFamily
            wrapMode: Text.Wrap
        }

    }

    Timer {
        id: timer

        interval: toastPopup.duration
        running: false
        repeat: false
        onTriggered: toastPopup.visible = false
    }

}
