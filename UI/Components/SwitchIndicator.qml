import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Theme" as Theme

Rectangle {
    property bool checked: false

    implicitWidth: 100
    implicitHeight: 40
    radius: height / 2
    color: checked ? 'green' : 'white'
    border.color: 'gray'
    border.width: 1
    
    Rectangle {
        anchors.verticalCenter: parent.verticalCenter
        
        readonly property int baseSize: parent.height / 1.6
        readonly property int margin: (parent.height - baseSize) / 2
        
        height: baseSize
        width: baseSize
        radius: baseSize / 2
        color: checked ? "white" : "gray"
        x: checked ? parent.width - width - margin : margin
    }
    
}
