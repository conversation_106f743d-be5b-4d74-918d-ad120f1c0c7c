import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Theme" as Theme

TitledFrame {
    id: root

    property alias fromValue: spinBox.from
    property alias toValue: spinBox.to
    property alias value: spinBox.value
    property alias validator: spinBox.validator

    height: 500
    width: 260

    Column {
        spacing: Theme.Spacing.medium
        height: 400
        width: 300

        anchors {
            top: parent.top
            topMargin: 20
            horizontalCenter: parent.horizontalCenter
        }

        RoundButton {
            anchors.horizontalCenter: parent.horizontalCenter
            text: "+"
            onClicked: spinBox.increase()
        }

        // SpinBox without indicators
        SpinBox {
            id: spinBox

            anchors.horizontalCenter: parent.horizontalCenter
            height: 90
            width: 200
            from: 1
            to: 23
            value: 20
            editable: true

            // Hide the default indicators
            up.indicator: Item {
            }

            down.indicator: Item {
            }

            // Custom content item (text field)
            contentItem: TextField {
                anchors.fill: parent
                text: spinBox.textFromValue(spinBox.value)
                font.pointSize: Theme.Typography.h2
                font.family: Theme.Fonts.clashGrotesk
                readOnly: !spinBox.editable
                validator: spinBox.validator
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                inputMethodHints: Qt.ImhDigitsOnly

                background: Rectangle {
                    color: Theme.Colors.inputBackground
                    border.color: Theme.Colors.addOpacity(Theme.Colors.darkGrey, 0.2)
                    border.width: 1
                    radius: 20
                }

            }

            // Override the default background to be transparent
            background: Item {
            }

        }

        RoundButton {
            anchors.horizontalCenter: parent.horizontalCenter
            text: "−"
            onClicked: spinBox.decrease()
        }

    }

    // Custom button component
    component RoundButton: Rectangle {
        id: btn

        property alias text: caption.text

        signal clicked()

        height: 90
        width: 90
        radius: Theme.Radius.round
        color: Theme.Colors.addOpacity(Theme.Colors.primary, 0.2)
        border.color: Theme.Colors.primary
        border.width: 1

        Text {
            id: caption

            anchors.centerIn: parent
            font.pointSize: Theme.Typography.h2
        }

        MouseArea {
            anchors.fill: parent
            onClicked: btn.clicked()
        }

    }

}
