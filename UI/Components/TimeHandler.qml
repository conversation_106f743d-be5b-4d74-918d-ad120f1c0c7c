import QtQuick 2.15

// Stub TimeHandler component to replace the C++ timehandler module
Item {
    id: timeHandler

    property string currentTime: Qt.formatTime(new Date(), "HH:mm:ss")
    property string currentDate: Qt.formatDate(new Date(), "yyyy-MM-dd")

    // Update time every second
    Timer {
        interval: 1000
        running: true
        repeat: true
        onTriggered: {
            timeHandler.currentTime = Qt.formatTime(new Date(), "HH:mm:ss");
            timeHandler.currentDate = Qt.formatDate(new Date(), "yyyy-MM-dd");
        }
    }

}
