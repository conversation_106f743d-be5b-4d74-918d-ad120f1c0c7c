import QtGraphicalEffects 1.15
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

Switch {
    id: control

    property alias backgroundRectangle: bck

    implicitWidth: 285
    implicitHeight: 260
    font.pixelSize: 46
    font.family: Theme.Typography.primaryFontFamily
    text: "Caption"
    icon.source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_RELOAD)

    background: Rectangle {
        id: bck

        radius: 48 // Increased for softer corners
        color: control.checked ? Qt.lighter(Theme.Colors.primary, 1.4) : Qt.lighter(Theme.Colors.primary, 1.9)
        border.color: Qt.lighter(Theme.Colors.primary, 1.6)
        border.width: 1
        gradient: bckGradient
        layer.enabled: true
        layer.effect: DropShadow {
            color: "#33000000"
            radius: 24
            samples: 32
            x: 0
            y: 6
        }

        Gradient {
            id: bckGradient

            orientation: Gradient.Horizontal

            GradientStop {
                position: 0
                color: "#fff3e0" // soft warm white
            }

            GradientStop {
                position: 1
                color: Qt.lighter(Theme.Colors.primary, 1.6)
            }

        }

    }

    contentItem: Item {
        Rectangle {
            width: parent.width / 2.4
            height: width
            radius: width / 2
            border.color: Qt.lighter(Theme.Colors.primary, control.checked ? 1.4 : 1.7)
            border.width: 1

            anchors {
                top: parent.top
                horizontalCenter: parent.horizontalCenter
                margins: 30
            }

            Image {
                id: icon

                source: control.icon.source
                anchors.centerIn: parent
                visible: false
            }

            ColorOverlay {
                anchors.fill: icon
                source: icon
                color: control.checked ? Theme.Colors.white : Theme.Colors.black
            }

            gradient: Gradient {
                GradientStop {
                    position: 0
                    color: control.checked ? Qt.lighter(Theme.Colors.primary, 1.1) : Qt.lighter(Theme.Colors.primary, 1.8)
                }
                GradientStop {
                    position: 1
                    color: control.checked ? Theme.Colors.primary : Theme.Colors.white
                }
            }
        }

        Text {
            anchors.bottom: parent.bottom
            anchors.bottomMargin: 22
            anchors.horizontalCenter: parent.horizontalCenter
            text: control.text
            color: Theme.Colors.black
            font: control.font
        }

    }

    indicator: Rectangle {
        implicitWidth: 60
        implicitHeight: 30
        radius: height / 2
        color: control.checked ? "black" : "white" // soft, semi-transparent green when on, white when off
        border.color: Theme.Colors.primary
        border.width: 1

        anchors {
            top: parent.top
            right: parent.right
            topMargin: 15
            rightMargin: 15
        }

        Rectangle {
            anchors.verticalCenter: parent.verticalCenter
            width: 22
            height: 22
            radius: width / 2
            color: Theme.Colors.primary
            x: control.checked ? parent.width - width - 4 : 4
        }

    }

}
