import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Theme" as Theme

Button {
    id: root

    property alias iconSource: icon.source

    implicitWidth: Theme.Spacing.cardButtonImplicitWidth
    implicitHeight: Theme.Spacing.cardButtonImplicitHeight
    font.family: Theme.Typography.secondaryFontFamily
    font.pointSize: Theme.Typography.h1
    text: "Caption"

    contentItem: Rectangle {
        border.color: Theme.Colors.white
        radius: Theme.Radius.item

        Column {
            anchors.centerIn: parent
            spacing: Theme.Spacing.medium

            Rectangle {
                anchors.horizontalCenter: parent.horizontalCenter
                width: 268
                height: 268
                radius: Theme.Radius.round
                border.color: Theme.Colors.primaryMedium
                border.width: Theme.Spacing.xxxsmall
                color: 'transparent'

                Rectangle {
                    anchors.fill: parent
                    anchors.margins: Theme.Spacing.medium
                    border.color: Theme.Colors.primary
                    border.width: root.down ? Theme.Spacing.xxsmall : Theme.Spacing.xxxsmall
                    radius: Theme.Radius.round

                    Image {
                        id: icon

                        anchors.centerIn: parent
                    }

                }

            }

            Text {
                anchors.horizontalCenter: parent.horizontalCenter
                font: root.font
                text: root.text
            }

        }

        gradient: Gradient {
            orientation: Gradient.Vertical

            GradientStop {
                position: 0
                color: Theme.Colors.primaryLight
            }

            GradientStop {
                position: 1
                color: Theme.Colors.white
            }

        }

    }

    background: Rectangle {
        color: Theme.Colors.white
        radius: Theme.Radius.item
    }

}
