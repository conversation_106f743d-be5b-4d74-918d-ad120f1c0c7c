import QtQuick 2.15
import QtQuick.Controls 2.15
import QtGraphicalEffects 1.15
import Backend.PrintfileManager 1.0
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: topPanel

    // Defined property for the preview content
    property string currentPreviewContent: "Select a file to view print preview"
    property bool showGridLines: false
    property int gridLinesSize: 36 // size in pixels
    property color gridLinesColor: Theme.Colors.secondary

    // Edit mode property to enable/disable edit capabilities
    property bool editMode: false

    // Property to track the currently selected item
    property Item selectedItem: null
    property string _selectedPrintfileItemId: ""

    signal itemSelected(Item item)

    function editSelectedItem() {
        if (!editMode) {
            console.log("Edit mode is disabled");
            return;
        }
        if (_selectedPrintfileItemId === "") {
            console.log("No item selected to edit");
            return;
        }
        // In a real app, this might open an editor UI. Here, just log for now.
        console.log("Edit item:", _selectedPrintfileItemId);
    }

    function copySelectedItem() {
        if (!editMode) {
            console.log("Edit mode is disabled");
            return;
        }

        if (_selectedPrintfileItemId === "") {
            console.log("No item selected to copy");
            return;
        }
        var newItem = PrintfileManager.duplicateEditPrintfileItem(_selectedPrintfileItemId);
        if (newItem) {
            _selectedPrintfileItemId = newItem.itemId;
            console.log("Copied item. New item id:", newItem.itemId);
        } else {
            console.log("Failed to copy item");
        }
    }

    function deleteSelectedItem() {
        if (!editMode) {
            console.log("Edit mode is disabled");
            return;
        }

        if (_selectedPrintfileItemId === "") {
            console.log("No item selected to delete");
            return;
        }
        var success = PrintfileManager.deleteEditPrintfileItem(_selectedPrintfileItemId);
        if (success) {
            console.log("Deleted item:", _selectedPrintfileItemId);
            _selectedPrintfileItemId = "";
            selectedItem = null;
        } else {
            console.log("Failed to delete item");
        }
    }

    function moveSelectedItemUp() { _moveSelectedItem(0, -10); }
    function moveSelectedItemDown() { _moveSelectedItem(0, 10); }
    function moveSelectedItemLeft() { _moveSelectedItem(-10, 0); }
    function moveSelectedItemRight() { _moveSelectedItem(10, 0); }

    // Functions to move the selected item
    function _moveSelectedItem(deltaX, deltaY) {
        if (!editMode) {
            console.log("Edit mode is disabled");
            return;
        }

        if (_selectedPrintfileItemId === "") {
            console.log("No item selected to move");
            return;
        }

        // Move the printfile item using PrintfileManager
        var currentItem = PrintfileManager.getEditPrintfileItem(_selectedPrintfileItemId);
        console.log('moving item:', _selectedPrintfileItemId)
        if (currentItem) {
            var newX = currentItem.x + deltaX;
            var newY = currentItem.y + deltaY;

            // Ensure the item stays within canvas bounds
            newX = Math.max(0, Math.min(newX, printfileCanvas.width - currentItem.width));
            newY = Math.max(0, Math.min(newY, printfileCanvas.height - currentItem.height));

            PrintfileManager.movePrintfileItem(_selectedPrintfileItemId, newX, newY);
        }
    }

    border.color: Theme.Colors.darkGrey
    border.width: 1
    color: Theme.Colors.white
    radius: Theme.Radius.large

    layer.enabled: true
    layer.effect: OpacityMask {
        maskSource: Rectangle {
            width: topPanel.width
            height: topPanel.height
            radius: topPanel.radius
        }
    }

    // Grid lines
    Item {
        anchors.fill: parent
        visible: topPanel.showGridLines

        // Vertical grid lines
        Repeater {
            model: parent.width / topPanel.gridLinesSize + 1

            Rectangle {
                x: index * topPanel.gridLinesSize
                width: 1
                height: topPanel.height
                color: topPanel.gridLinesColor
                opacity: 0.3
            }
        }

        // Horizontal grid lines
        Repeater {
            model: parent.height / topPanel.gridLinesSize + 1

            Rectangle {
                y: index * topPanel.gridLinesSize
                width: topPanel.width
                height: 1
                color: topPanel.gridLinesColor
                opacity: 0.3
            }
        }
    }

    // Mark at bottom left corner using two rectangles
    Item {
        anchors.left: parent.left
        anchors.bottom: parent.bottom
        width: 20
        height: 20
        visible: false // TODO Do we need it if the area has a border?

        // Horizontal line
        Rectangle {
            anchors.left: parent.left
            anchors.bottom: parent.bottom
            width: parent.width
            height: 3
            color: Theme.Colors.red
        }

        // Vertical line
        Rectangle {
            anchors.left: parent.left
            anchors.bottom: parent.bottom
            width: 3
            height: parent.height
            color: Theme.Colors.red
        }
    }

    // Printfile-based preview area
    Item {
        anchors.fill: parent

        // Show message when no printfile is selected
        Text {
            anchors.centerIn: parent
            text: PrintfileManager.currentPrintfile ?
                  ("File: " + PrintfileManager.currentPrintfile.name) :
                  "No file selected"
            font.pixelSize: 16
            color: Theme.Colors.secondary
            visible: !PrintfileManager.currentPrintfile
        }

        // Printfile canvas
        Item {
            id: printfileCanvas
            anchors.fill: parent
            visible: PrintfileManager.currentPrintfile !== null

            // Printfile items
            Repeater {
                model: topPanel.editMode ? PrintfileManager.editPrintfileItemModel : PrintfileManager.currentPrintfileItemModel

                delegate: Rectangle {
                    id: printfileItemDelegate
                    x: (model.x || 0)
                    y: (model.y || 0)
                    width: (model.width || 100)
                    height: (model.height || 100)
                    color: "transparent"
                    border.color: (topPanel.editMode && topPanel._selectedPrintfileItemId === model.itemId) ? Theme.Colors.primary : Theme.Colors.transparent
                    border.width: (topPanel.editMode && topPanel._selectedPrintfileItemId === model.itemId) ? 2 : 0
                    radius: 6
                    rotation: model.rotation || 0
                    scale: model.scale || 1.0
                    visible: model.visible !== false

                    // Simple text rendering for preview (in real implementation, would load actual QML components)
                    Text {
                        anchors.fill: parent
                        text: {
                            var props = model.properties || {};
                            return props.text || "Text Item";
                        }
                        font.pixelSize: {
                            var props = model.properties || {};
                            return (props.fontSize || 16)
                        }
                        color: {
                            var props = model.properties || {};
                            return props.color || "#000000";
                        }
                        font.weight: {
                            var props = model.properties || {};
                            return props.fontWeight === "bold" ? Font.Bold : Font.Normal;
                        }
                        horizontalAlignment: {
                            var props = model.properties || {};
                            switch(props.textAlign) {
                                case "center": return Text.AlignHCenter;
                                case "right": return Text.AlignRight;
                                default: return Text.AlignLeft;
                            }
                        }
                        verticalAlignment: Text.AlignVCenter
                        wrapMode: Text.WordWrap
                        elide: Text.ElideRight
                    }

                    MouseArea {
                        anchors.fill: parent
                        enabled: topPanel.editMode
                        onClicked: {
                            if (topPanel.editMode) {
                                topPanel.selectedItem = printfileItemDelegate;
                                topPanel._selectedPrintfileItemId = model.itemId || "";
                                topPanel.itemSelected(printfileItemDelegate);
                            }
                        }
                    }
                }
            }
        }
    }

}
