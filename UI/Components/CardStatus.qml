import QtGraphicalEffects 1.15
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

// Main card background
Rectangle {
    id: root

    property alias caption: captionText.text
    property string value: "3200"
    property string units: ""
    property color borderColor: Theme.Colors.primary // Gradient border primary color
    property alias backgroundColor: root.color
    property color backgroundGradientColor: 'transparent'
    property color captionTextColor: captionText.color
    property color valueTextColor: valueText.color

    implicitWidth: 600
    implicitHeight: 300
    color: Theme.Colors.white
    radius: Theme.Radius.item

    Rectangle {
        id: backgroundGradientRectangle
        anchors.fill: parent
        radius: parent.radius
    }

    RadialGradient {
        anchors.fill: backgroundGradientRectangle
        source: backgroundGradientRectangle

        verticalOffset: backgroundGradientRectangle.height / 2
        horizontalOffset: -backgroundGradientRectangle.width / 3

        gradient: Gradient {
            GradientStop { position: 0.0; color: root.backgroundGradientColor }
            GradientStop { position: 1.0; color: 'transparent' }
        }
    }

    // Gradient border effect
    Rectangle {
        id: borderMask

        anchors.fill: parent
        radius: parent.radius
        color: "transparent"
        border.width: 1
        border.color: "white"
        visible: false
    }

    LinearGradient {
        anchors.fill: borderMask
        source: borderMask

        gradient: Gradient {
            orientation: Qt.Vertical

            GradientStop {
                position: 0
                color: root.borderColor
            }

            GradientStop {
                position: 1
                color: 'transparent'
            }

        }

    }

    // Caption text
    Text {
        id: captionText

        text: "Name"
        color: Theme.Colors.black
        font.family: Theme.Typography.secondaryFontFamily
        font.weight: Theme.Typography.weightSemiBold
        font.pixelSize: 46
        anchors.left: parent.left
        anchors.top: parent.top
        anchors.leftMargin: Theme.Spacing.large
        anchors.topMargin: Theme.Spacing.large
        wrapMode: Text.NoWrap
    }

    // Value text
    Text {
        id: valueText

        text: root.value + (root.units.length > 0 ? " " + root.units : "")
        color: Theme.Colors.black
        font.family: Theme.Typography.secondaryFontFamily
        font.weight: Theme.Typography.weightBold
        font.pixelSize: 68
        anchors.left: parent.left
        anchors.bottom: parent.bottom
        anchors.leftMargin: Theme.Spacing.large
        anchors.bottomMargin: Theme.Spacing.large
        wrapMode: Text.NoWrap
    }

    Image {
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_CARD_STATUS_CIRCLES)
    }

    Behavior on backgroundGradientColor {
        ColorAnimation { duration: 200 }
    }
}
