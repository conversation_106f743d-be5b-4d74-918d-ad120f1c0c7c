import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Theme" as Theme

Switch {
    id: control

    property alias textAlignment: caption.horizontalAlignment
    property alias textColor: caption.color
    property alias backgroundRectangle: bck

    implicitWidth: 600
    implicitHeight: 158
    padding: 50
    font.pixelSize: 46
    font.weight: Theme.Typography.weightSemiBold

    background: Rectangle {
        id: bck

        radius: Theme.Radius.item
        border.color: Theme.Colors.primary
        border.width: 1
    }

    contentItem: Text {
        id: caption

        verticalAlignment: Text.AlignVCenter
        rightPadding: indicator.width
        text: control.text
        font: control.font

    }

    indicator: SwitchIndicator {
        checked: control.checked

        width: parent.width / 6
        height: parent.height / 3

        anchors {
            verticalCenter: parent.verticalCenter
            right: parent.right
            rightMargin: control.padding
        }
    }
}
