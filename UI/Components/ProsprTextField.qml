import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Window 2.14
import "qrc:/UI/Theme" as Theme

TextField {
    id: control

    // Properties
    property bool isValid: true
    property string errorMessage: ""
    property bool showLabel: true
    property string labelText: ""
    property bool enableVirtualKeyboard: true // Controls whether virtual keyboard is shown
    // Colors from theme
    property color borderColor: activeFocus ? Theme.Colors.primary : (isValid ? Theme.Colors.borderColor : Theme.Colors.red)
    property color backgroundColor: enabled ? Theme.Colors.backgroundPrimary : Theme.Colors.backgroundSecondary
    property color textColor: enabled ? Theme.Colors.textPrimary : Theme.Colors.midGrey

    // Function to ensure the text field is visible when keyboard appears
    function ensureVisible() {
        if (!Window.window)
            return ;

        // Get keyboard height when it's visible
        var keyboardHeight = Qt.inputMethod.keyboardRectangle.height;
        if (keyboardHeight <= 0)
            return ;

        // Keyboard not shown yet
        // Calculate global position of this text field
        var globalPos = control.mapToGlobal(0, 0);
        var screenHeight = Window.window.height;
        // Check if the text field is obscured by keyboard
        if (globalPos.y + control.height > screenHeight - keyboardHeight) {
            // Create a signal to inform parent container to scroll if needed
            if (control.parent && typeof control.parent.ensureChildVisible === "function")
                control.parent.ensureChildVisible(control);

        }
    }

    // Sizing
    implicitWidth: 280
    implicitHeight: showLabel ? 70 : 40
    // Text
    color: textColor
    font.pixelSize: Theme.Typography.inputText
    font.family: Theme.Typography.primaryFontFamily
    leftPadding: Theme.Spacing.medium
    rightPadding: Theme.Spacing.medium
    verticalAlignment: TextInput.AlignVCenter
    // Handle keyboard visibility
    onActiveFocusChanged: {
        if (!activeFocus && enableVirtualKeyboard)
            Qt.inputMethod.hide();

    }
    // Connect to theme changes
    Component.onCompleted: {
        Theme.Theme.themeChanged.connect(function() {
            // Update colors when theme changes
            backgroundColor = enabled ? Theme.Colors.backgroundPrimary : Theme.Colors.backgroundSecondary;
            textColor = enabled ? Theme.Colors.textPrimary : Theme.Colors.midGrey;
            borderColor = activeFocus ? Theme.Colors.primary : (isValid ? Theme.Colors.borderColor : Theme.Colors.red);
            background.color = backgroundColor;
        });
    }

    // Label
    Label {
        id: inputLabel

        visible: showLabel
        text: labelText
        font.pixelSize: Theme.Typography.helperText
        font.family: Theme.Typography.primaryFontFamily
        font.weight: Theme.Typography.weightMedium
        color: Theme.Colors.textSecondary
        anchors.bottom: control.top
        anchors.bottomMargin: Theme.Spacing.xsmall
        anchors.left: control.left
    }

    // Error message
    Label {
        id: errorLabel

        visible: !isValid && errorMessage !== ""
        text: errorMessage
        font.pixelSize: Theme.Typography.helperText
        font.family: Theme.Typography.primaryFontFamily
        color: Theme.Colors.red
        anchors.top: control.bottom
        anchors.topMargin: Theme.Spacing.xsmall
        anchors.left: control.left
    }

    // Background
    background: Rectangle {
        implicitWidth: control.implicitWidth
        implicitHeight: 40
        color: control.backgroundColor
        radius: 4
        border.width: 1
        border.color: control.borderColor
    }

    // Animation for focus state
    states: State {
        name: "focused"
        when: control.activeFocus

        PropertyChanges {
            target: control.background
            border.width: 2
        }

        StateChangeScript {
            name: "showKeyboard"
            script: {
                if (control.enableVirtualKeyboard) {
                    Qt.inputMethod.show();
                    // Ensure field is visible when keyboard appears
                    control.ensureVisible();
                }
            }
        }

    }

    transitions: Transition {
        from: ""
        to: "focused"
        reversible: true

        PropertyAnimation {
            properties: "border.width"
            duration: 200
        }

    }

}
