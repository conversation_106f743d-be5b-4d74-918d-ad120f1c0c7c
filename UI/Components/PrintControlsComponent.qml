import QtQuick 2.15
import QtQuick.Controls 2.15
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: printControlsRect

    property string startRangeValue: "500"
    property string endRangeValue: "1200"

    signal printRequested()
    signal stopRequested()

    width: parent.width
    height: parent.height
    color: "transparent"

    Column {
        width: parent.width
        height: parent.height
        spacing: 15

        // Print range input
        Row {
            width: parent.width
            height: childrenRect.height
            spacing: 15

            // Start At field
            Column {
                width: (parent.width - 15) / 2
                spacing: 5

                Text {
                    text: "Start At"
                    font.pixelSize: 14
                    color: Theme.Colors.black
                }

                Rectangle {
                    width: parent.width
                    height: 40
                    color: Theme.Colors.white
                    border.color: Theme.Colors.borderColor
                    border.width: 1
                    radius: 4

                    TextInput {
                        anchors.fill: parent
                        anchors.margins: 10
                        text: startRangeValue
                        font.pixelSize: 16
                        color: Theme.Colors.secondary
                        verticalAlignment: TextInput.AlignVCenter
                        onTextChanged: {
                            printControlsRect.startRangeValue = text;
                        }
                    }

                }

            }

            // End At field
            Column {
                width: (parent.width - 15) / 2
                spacing: 5

                Text {
                    text: "End At"
                    font.pixelSize: 14
                    color: Theme.Colors.black
                }

                Rectangle {
                    width: parent.width
                    height: 40
                    color: Theme.Colors.white
                    border.color: Theme.Colors.borderColor
                    border.width: 1
                    radius: 4

                    TextInput {
                        anchors.fill: parent
                        anchors.margins: 10
                        text: endRangeValue
                        font.pixelSize: 16
                        color: Theme.Colors.secondary
                        verticalAlignment: TextInput.AlignVCenter
                        onTextChanged: {
                            printControlsRect.endRangeValue = text;
                        }
                    }

                }

            }

        }

        // Print and Stop buttons
        Item {
            width: parent.width
            height: 70

            Row {
                width: parent.width
                height: parent.height
                spacing: 15

                // Print button
                Rectangle {
                    width: (parent.width - 15) / 2
                    height: parent.height
                    color: Theme.Colors.success
                    radius: height / 2

                    Text {
                        anchors.centerIn: parent
                        text: "PRINT"
                        color: Theme.Colors.white
                        font.pixelSize: 20
                        font.weight: Font.Bold
                    }

                    MouseArea {
                        anchors.fill: parent
                        cursorShape: Qt.PointingHandCursor
                        onClicked: {
                            printControlsRect.printRequested();
                        }
                    }

                }

                // Stop button
                Rectangle {
                    width: (parent.width - 15) / 2
                    height: parent.height
                    color: Theme.Colors.red
                    radius: height / 2

                    Text {
                        anchors.centerIn: parent
                        text: "STOP"
                        color: Theme.Colors.white
                        font.pixelSize: 20
                        font.weight: Font.Bold
                    }

                    MouseArea {
                        anchors.fill: parent
                        cursorShape: Qt.PointingHandCursor
                        onClicked: {
                            printControlsRect.stopRequested();
                        }
                    }

                }

            }

        }

    }

}
