import QtGraphicalEffects 1.15
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: root

    property alias progress: progressControl.value
    property alias title: titleText.text
    property int _itemSpacing: 30

    signal stop()

    implicitWidth: 1000
    implicitHeight: 600
    radius: Theme.Radius.item

    ColumnLayout {
        spacing: root._itemSpacing

        anchors {
            fill: parent
            leftMargin: root._itemSpacing
            rightMargin: root._itemSpacing
            bottomMargin: root._itemSpacing
        }

        Text {
            id: titleText

            Layout.fillWidth: true
            Layout.preferredHeight: 100
            text: "Progress Dialog"
            font.pixelSize: 46
            font.family: Theme.Typography.primaryFontFamily
            verticalAlignment: Text.AlignVCenter

            Rectangle {
                id: separatorHeader

                height: 2
                color: Theme.Colors.black

                anchors {
                    bottom: parent.bottom
                    left: parent.left
                    leftMargin: -root._itemSpacing
                    right: parent.right
                    rightMargin: -root._itemSpacing
                }

            }

        }

        Flickable {
            Layout.fillHeight: true
            Layout.fillWidth: true
            contentWidth: width
            contentHeight: descriptionText.implicitHeight
            interactive: contentHeight > height
            clip: true

            Text {
                id: descriptionText

                anchors.fill: parent
                text: "Progress:\n1...\n2...\n3..."
                font.pixelSize: 28
                font.weight: Font.DemiBold
                wrapMode: Text.Wrap
                color: Theme.Colors.black
            }

            ScrollBar.vertical: ScrollBar {
            }

        }

        Column {
            Layout.fillWidth: true
            spacing: root._itemSpacing

            Rectangle {
                height: 2
                color: Theme.Colors.black

                anchors {
                    left: parent.left
                    leftMargin: -root._itemSpacing
                    right: parent.right
                    rightMargin: -root._itemSpacing
                }

            }

            RowLayout {
                width: parent.width
                spacing: root._itemSpacing

                ProgressBar {
                    id: progressControl

                    Layout.fillWidth: true
                    padding: 10
                    implicitHeight: 100
                    value: 0.4

                    background: Rectangle {
                        radius: Theme.Radius.xmlarge
                        color: Theme.Colors.addOpacity(Theme.Colors.primary, 0.1)
                    }

                    contentItem: Item {
                        Rectangle {
                            width: parent.width * progressControl.visualPosition
                            height: parent.height
                            color: Theme.Colors.primary
                            radius: 20
                        }

                        Text {
                            anchors.centerIn: parent
                            font.family: Theme.Typography.secondaryFontFamily
                            font.pixelSize: Theme.Typography.h4
                            font.weight: Theme.Typography.weightSemiBold
                            text: `${progressControl.value * 100}%`
                        }

                    }

                }

                DefaultButton {
                    text: "STOP"
                    backgroundRectangle.color: Theme.Colors.red
                    icon.source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_STOP)
                    font.weight: Theme.Typography.weightSemiBold
                    onClicked: {
                        root.stop();
                    }
                }

            }

        }

    }

}
