import QtGraphicalEffects 1.14
import QtQuick 2.15
import QtQuick.Controls 2.15
import "qrc:/UI/Theme" as Theme

Button {
    id: control

    // Properties
    property string buttonType: "primary"
    // primary, secondary, danger
    property bool isOutlined: false
    // Colors from theme
    property color primaryColor: Theme.Colors.primary
    property color secondaryColor: Theme.Colors.secondary
    property color dangerColor: Theme.Colors.red
    // Determine background and text colors based on properties
    property color backgroundColor: {
        if (isOutlined)
            return "transparent";

        if (buttonType === "primary")
            return primaryColor;

        if (buttonType === "secondary")
            return secondaryColor;

        if (buttonType === "danger")
            return dangerColor;

        return primaryColor;
    }
    property color textColor: {
        if (isOutlined) {
            if (buttonType === "primary")
                return primaryColor;

            if (buttonType === "secondary")
                return secondaryColor;

            if (buttonType === "danger")
                return dangerColor;

            return primaryColor;
        }
        return "white";
    }
    property color borderColor: {
        if (buttonType === "primary")
            return primaryColor;

        if (buttonType === "secondary")
            return secondaryColor;

        if (buttonType === "danger")
            return dangerColor;

        return primaryColor;
    }

    // Sizing
    implicitWidth: 120
    implicitHeight: 40
    // Connect to theme changes
    Component.onCompleted: {
        Theme.Theme.themeChanged.connect(function() {
            // Force visual update when theme changes
            background.color = control.enabled ? (control.pressed ? Qt.darker(control.backgroundColor, 1.2) : control.backgroundColor) : Theme.Colors.midGrey;
        });
    }

    // Mouse cursor
    MouseArea {
        anchors.fill: parent
        anchors.margins: -Theme.Spacing.xxsmall // Increase touch area
        cursorShape: Qt.PointingHandCursor
        acceptedButtons: Qt.NoButton
    }

    // Text
    contentItem: Text {
        text: control.text
        font.pixelSize: Theme.Typography.buttonText
        font.family: Theme.Typography.primaryFontFamily
        font.weight: Theme.Typography.weightBold
        font.letterSpacing: Theme.Typography.letterSpacingWide
        font.capitalization: Font.AllUppercase
        color: control.enabled ? control.textColor : Theme.Colors.midGrey
        horizontalAlignment: Text.AlignHCenter
        verticalAlignment: Text.AlignVCenter
        elide: Text.ElideRight
    }

    // Background
    background: Rectangle {
        id: buttonBackground

        implicitWidth: control.implicitWidth
        implicitHeight: control.implicitHeight
        color: control.enabled ? (control.pressed ? Qt.darker(control.backgroundColor, 1.2) : control.backgroundColor) : Theme.Colors.midGrey
        radius: 4
        border.width: control.isOutlined ? 2 : 0
        border.color: control.borderColor
        // Add elevation shadow
        Component.onCompleted: {
            if (!control.isOutlined && control.enabled)
                Theme.Shadows.applyElevation(buttonBackground, 1);

        }

        // Hover effect
        Rectangle {
            anchors.fill: parent
            radius: 4
            color: Theme.Theme.isDarkMode ? "white" : "white"
            opacity: control.hovered && !control.pressed ? 0.2 : 0
            visible: control.enabled
        }

    }

}
