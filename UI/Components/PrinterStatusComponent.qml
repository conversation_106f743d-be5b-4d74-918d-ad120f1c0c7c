import QtQuick 2.15
import QtQuick.Controls 2.15
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: printerStatusRect

    property string status: "Printing"
    property string printedCount: "520000"
    property string historyCount: "410000"

    signal resetCounterRequested()

    width: parent.width
    height: parent.height
    color: "transparent"

    Column {
        width: parent.width
        height: parent.height
        spacing: 15

        // Status section
        Text {
            text: "Printer Status"
            font.pixelSize: 18
            font.weight: Font.Bold
            color: Theme.Colors.black
        }

        // Status counters row
        Row {
            width: parent.width
            height: 70
            spacing: 0

            // Status indicator
            Rectangle {
                width: parent.width / 3
                height: parent.height
                color: Theme.Colors.transparent
                border.width: 1
                border.color: Theme.Colors.borderColor

                Column {
                    anchors.centerIn: parent
                    spacing: 2

                    Text {
                        anchors.horizontalCenter: parent.horizontalCenter
                        text: "Status"
                        font.pixelSize: 12
                        color: Theme.Colors.primary
                    }

                    Text {
                        anchors.horizontalCenter: parent.horizontalCenter
                        text: printerStatusRect.status
                        font.pixelSize: 14
                        color: Theme.Colors.primary
                        font.weight: Font.Medium
                    }

                }

            }

            // Printed counter
            Rectangle {
                width: parent.width / 3
                height: parent.height
                color: Theme.Colors.transparent
                border.width: 1
                border.color: Theme.Colors.borderColor

                Column {
                    anchors.centerIn: parent
                    spacing: 2

                    Text {
                        anchors.horizontalCenter: parent.horizontalCenter
                        text: "Printed"
                        font.pixelSize: 12
                        color: Theme.Colors.primary
                    }

                    Text {
                        anchors.horizontalCenter: parent.horizontalCenter
                        text: printerStatusRect.printedCount
                        font.pixelSize: 14
                        color: Theme.Colors.primary
                        font.weight: Font.Medium
                    }

                }

            }

            // History counter
            Rectangle {
                width: parent.width / 3
                height: parent.height
                color: Theme.Colors.transparent
                border.width: 1
                border.color: Theme.Colors.borderColor

                Column {
                    anchors.centerIn: parent
                    spacing: 2

                    Text {
                        anchors.horizontalCenter: parent.horizontalCenter
                        text: "History"
                        font.pixelSize: 12
                        color: Theme.Colors.primary
                    }

                    Text {
                        anchors.horizontalCenter: parent.horizontalCenter
                        text: printerStatusRect.historyCount
                        font.pixelSize: 14
                        color: Theme.Colors.primary
                        font.weight: Font.Medium
                    }

                }

            }

        }

        // Reset counter button
        Rectangle {
            width: parent.width
            height: 50
            color: Theme.Colors.secondary
            radius: height / 2

            Text {
                anchors.centerIn: parent
                text: "RESET COUNTER"
                color: Theme.Colors.white
                font.pixelSize: 18
                font.weight: Font.Bold
            }

            MouseArea {
                anchors.fill: parent
                cursorShape: Qt.PointingHandCursor
                onClicked: {
                    printerStatusRect.resetCounterRequested();
                }
            }

        }

    }

}
