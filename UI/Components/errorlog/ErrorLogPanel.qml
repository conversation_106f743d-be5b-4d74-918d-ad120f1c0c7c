import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import Backend.Error 1.0
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

/*
  ErrorLogPanel Component
  Displays a table of error logs with sortable columns.
*/
Rectangle {
    id: root

    color: Theme.Colors.white

    // Constants for layout
    QtObject {
        id: constants

        readonly property int headerHeight: 94
        readonly property int rowHeight: 94
        readonly property int margin: Theme.Spacing.medium
        readonly property int fontSize: Theme.Typography.h2
        readonly property int titleSize: Theme.Typography.h2
        readonly property int columnSpacing: Theme.Spacing.medium
        readonly property color headerTextColor: Theme.Colors.secondary
        readonly property color rowTextColor: Theme.Colors.black
        readonly property color borderColor: Theme.Colors.borderColor
    }

    ColumnLayout {
        anchors.fill: parent

        Rectangle {
            id: header
            Layout.fillWidth: true
            implicitHeight: constants.headerHeight
            color: Theme.Colors.headerBackground
            // Header row
            Row {
                anchors.fill: parent
                anchors.leftMargin: constants.margin
                anchors.rightMargin: constants.margin
                spacing: constants.columnSpacing

                // Error column header
                Text {
                    width: parent.width * 0.6
                    height: parent.height
                    text: qsTr("Error")
                    color: constants.headerTextColor
                    verticalAlignment: Text.AlignVCenter
                    font {
                        family: Theme.Typography.secondaryFontFamily
                        pixelSize: constants.fontSize
                        weight: Font.Medium
                    }
                }
                // Code column header
                Text {
                    width: parent.width * 0.2
                    height: parent.height
                    text: qsTr("Code")
                    color: constants.headerTextColor
                    verticalAlignment: Text.AlignVCenter
                    font {
                        family: Theme.Typography.secondaryFontFamily
                        pixelSize: constants.fontSize
                        weight: Font.Medium
                    }
                }
                // Timestamp column header
                Row {
                    width: parent.width * 0.2
                    height: parent.height
                    spacing: Theme.Spacing.small
                    Text {
                        text: qsTr("Timestamp")
                        color: constants.headerTextColor
                        anchors.verticalCenter: parent.verticalCenter
                        font {
                            family: Theme.Typography.secondaryFontFamily
                            pixelSize: constants.fontSize
                            weight: Font.Medium
                        }
                    }
                }

            }

        }

        // Error log list
        ListView {
            id: errorList

            Layout.fillHeight: true
            Layout.fillWidth: true
            clip: true

            // Actual error log model from C++ backend
            model: ErrorModel {}


            // List item delegate
            delegate: Rectangle {
                width: ListView.view ? ListView.view.width : 0
                height: constants.rowHeight
                color: Theme.Colors.white

                // Bottom border
                Rectangle {
                    height: 1
                    color: constants.borderColor

                    anchors {
                        left: parent.left
                        right: parent.right
                        bottom: parent.bottom
                    }

                }

                Row {
                    anchors.fill: parent
                    anchors.leftMargin: constants.margin
                    anchors.rightMargin: constants.margin
                    spacing: constants.columnSpacing

                    Text {
                        width: parent.width * 0.6
                        height: parent.height
                        text: error
                        color: constants.rowTextColor
                        elide: Text.ElideRight
                        verticalAlignment: Text.AlignVCenter
                        font {
                            family: Theme.Typography.secondaryFontFamily
                            pixelSize: constants.fontSize
                        }
                    }
                    Text {
                        width: parent.width * 0.2
                        height: parent.height
                        text: code
                        color: constants.rowTextColor
                        elide: Text.ElideRight
                        verticalAlignment: Text.AlignVCenter
                        font {
                            family: Theme.Typography.secondaryFontFamily
                            pixelSize: constants.fontSize
                        }
                    }
                    Text {
                        width: parent.width * 0.2
                        height: parent.height
                        text: timestamp
                        color: constants.rowTextColor
                        elide: Text.ElideRight
                        verticalAlignment: Text.AlignVCenter
                        font {
                            family: Theme.Typography.secondaryFontFamily
                            pixelSize: constants.fontSize
                        }
                    }

                }

            }

            ScrollBar.vertical: ScrollBar {
            }

        }

    }

}
