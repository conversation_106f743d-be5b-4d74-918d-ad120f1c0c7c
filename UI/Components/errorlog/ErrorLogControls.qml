import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Theme" as Theme

/*
  ErrorLogControls Component
  Navigation control panel for the error log view with buttons for
  scrolling, refreshing, and page navigation.
*/
Rectangle {
    id: navButtonsContainer

    // Navigation signals
    signal upClicked()
    signal downClicked()
    signal refreshClicked()
    signal nextClicked()
    signal previousClicked()

    width: 80
    color: "transparent"

    // Constants
    QtObject {
        id: constants

        readonly property int buttonSize: 64
        readonly property int buttonRadius: Theme.Radius.round
        readonly property int spacing: Theme.Spacing.medium
        readonly property int margin: Theme.Spacing.medium
        readonly property int iconSize: 24
        readonly property color buttonColor: Theme.Colors.secondary
        readonly property color buttonHoverColor: Qt.darker(Theme.Colors.secondary, 1.1)
        readonly property color buttonPressedColor: Qt.darker(Theme.Colors.secondary, 1.2)
    }

    // Navigation and action buttons layout
    Column {
        anchors.fill: parent
        spacing: constants.spacing
        topPadding: constants.margin
        bottomPadding: constants.margin

        // Up button
        Rectangle {
            id: upButton
            width: constants.buttonSize
            height: constants.buttonSize
            radius: constants.buttonRadius
            color: upMouseArea.pressed ? constants.buttonPressedColor : upMouseArea.containsMouse ? constants.buttonHoverColor : constants.buttonColor
            anchors.horizontalCenter: parent.horizontalCenter
            Text {
                anchors.centerIn: parent
                text: "\u25b2"
                color: Theme.Colors.white
                font.pixelSize: constants.iconSize
            }
            MouseArea {
                id: upMouseArea
                anchors.fill: parent
                onClicked: navButtonsContainer.upClicked()
                cursorShape: Qt.PointingHandCursor
                onClicked: upClicked()
            }

        }

        // Down button
        Rectangle {
            id: downButton

            width: constants.buttonSize
            height: constants.buttonSize
            radius: constants.buttonRadius
            color: downMouseArea.pressed ? constants.buttonPressedColor : downMouseArea.containsMouse ? constants.buttonHoverColor : constants.buttonColor
            anchors.horizontalCenter: parent.horizontalCenter

            Text {
                anchors.centerIn: parent
                text: "▼"
                color: Theme.Colors.white
                font.pixelSize: constants.iconSize
            }

            MouseArea {
                id: downMouseArea

                anchors.fill: parent
                hoverEnabled: true
                cursorShape: Qt.PointingHandCursor
                onClicked: downClicked()
            }

        }

        // Refresh button
        Rectangle {
            id: refreshButton

            width: constants.buttonSize
            height: constants.buttonSize
            radius: constants.buttonRadius
            color: refreshMouseArea.pressed ? constants.buttonPressedColor : refreshMouseArea.containsMouse ? constants.buttonHoverColor : constants.buttonColor
            anchors.horizontalCenter: parent.horizontalCenter

            Text {
                anchors.centerIn: parent
                text: "↻"
                color: Theme.Colors.white
                font.pixelSize: constants.iconSize
            }

            MouseArea {
                id: refreshMouseArea

                anchors.fill: parent
                hoverEnabled: true
                cursorShape: Qt.PointingHandCursor
                onClicked: refreshClicked()
            }

        }

        // Spacer
        Item {
            width: constants.buttonSize
            height: parent.height - (5 * constants.buttonSize + 4 * constants.spacing + 2 * constants.margin)
        }

        // Next button
        Rectangle {
            id: nextButton

            width: constants.buttonSize
            height: constants.buttonSize
            radius: constants.buttonRadius
            color: nextMouseArea.pressed ? constants.buttonPressedColor : nextMouseArea.containsMouse ? constants.buttonHoverColor : constants.buttonColor
            anchors.horizontalCenter: parent.horizontalCenter

            Text {
                anchors.centerIn: parent
                text: "→"
                color: Theme.Colors.white
                font.pixelSize: constants.iconSize
            }

            MouseArea {
                id: nextMouseArea

                anchors.fill: parent
                hoverEnabled: true
                cursorShape: Qt.PointingHandCursor
                onClicked: nextClicked()
            }

        }

        // Previous button
        Rectangle {
            id: prevButton

            width: constants.buttonSize
            height: constants.buttonSize
            radius: constants.buttonRadius
            color: prevMouseArea.pressed ? constants.buttonPressedColor : prevMouseArea.containsMouse ? constants.buttonHoverColor : constants.buttonColor
            anchors.horizontalCenter: parent.horizontalCenter

            Text {
                anchors.centerIn: parent
                text: "←"
                color: Theme.Colors.white
                font.pixelSize: constants.iconSize
            }

            MouseArea {
                id: prevMouseArea

                anchors.fill: parent
                hoverEnabled: true
                cursorShape: Qt.PointingHandCursor
                onClicked: previousClicked()
            }

        }

    }

}
