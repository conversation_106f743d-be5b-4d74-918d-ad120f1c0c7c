import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: component

    property alias text: text.text
    property alias source: image.source
    property alias color: component.color
    property alias textColor: text.color

    signal clicked()

    width: parent.width
    height: 98
    Layout.fillHeight: true
    radius: Theme.Radius.xmlarge
    color: Theme.Colors.secondary

    Row {
        spacing: Theme.Spacing.xsmall
        anchors.centerIn: parent

        Image {
            id: image

            fillMode: Image.PreserveAspectFit
            anchors.verticalCenter: parent.verticalCenter
        }

        Text {
            id: text

            color: Theme.Colors.white
            font.pixelSize: 30
            font.weight: Font.Bold
            anchors.verticalCenter: parent.verticalCenter
        }

    }

    MouseArea {
        anchors.fill: parent
        cursorShape: Qt.PointingHandCursor
        onClicked: component.clicked()
    }

}
