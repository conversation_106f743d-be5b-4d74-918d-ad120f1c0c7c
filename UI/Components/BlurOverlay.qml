import QtGraphicalEffects 1.15
import QtQuick 2.15
import "qrc:/UI/Theme" as Theme

Item {
    id: blurOverlay

    // Property to specify the source item to blur
    property alias source: blurEffect.source
    property Item fallbackSource: parent
    property Item sourceItem: null

    anchors.fill: parent
    // Debug: Log visibility changes
    onVisibleChanged: console.log("BlurOverlay visibility:", visible)

    // Blur effect layer
    FastBlur {
        id: blurEffect

        anchors.fill: parent
        source: blurOverlay.sourceItem ? blurOverlay.sourceItem : blurOverlay.fallbackSource
        radius: 32 // Hardcoded for testing; revert to 15 * Theme.Radius once verified
        z: 1
        // Debug: Log when blur is rendered
        Component.onCompleted: console.log("FastBlur initialized with source:", source, "radius:", radius)
    }

    // Dark semi-transparent overlay
    Rectangle {
        anchors.fill: parent
        color: "#33000000" // Lowered opacity (~20%) to make blur visible
        z: 2
    }

    // Block user input (with touch support)
    Mouse<PERSON>rea {
        anchors.fill: parent
        onClicked: console.log("Mouse input blocked by BlurOverlay")
    }

    MultiPointTouchArea {
        anchors.fill: parent
        onTouchUpdated: console.log("Touch input blocked by BlurOver<PERSON>")
    }

}
