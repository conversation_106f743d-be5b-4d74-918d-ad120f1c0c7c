import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Theme" as Theme

Button {
    id: root

    property alias textColor: txt.color
    property bool autoTextColor: true
    property alias image: img
    property alias backgroundRectangle: bck

    implicitWidth: Math.max(300, txt.implicitWidth + 240)
    implicitHeight: 100
    opacity: enabled ? 1 : 0.3
    font.pixelSize: 30
    font.family: Theme.Typography.button.family
    font.weight: Theme.Typography.button.weight

    contentItem: Item {
        Row {
            anchors.centerIn: parent
            spacing: 10

            Image {
                id: img

                anchors.verticalCenter: parent.verticalCenter
                source: root.icon.source
                visible: source !== ""
            }

            Text {
                id: txt

                anchors.verticalCenter: parent.verticalCenter
                font: root.font
                text: root.text
                color: Theme.Colors.darkBlue
                visible: root.text !== ""
            }

        }

    }

    background: Rectangle {
        id: bck

        color: Theme.Colors.darkBlue
        radius: 20
        border.color: Theme.Colors.darkBlue
        border.width: color === Theme.Colors.white ? 1 : 0
        onColorChanged: {
            if (!autoTextColor)
                return ;

            if (color === Theme.Colors.white || color === "white")
                txt.color = Theme.Colors.darkBlue;
            else
                txt.color = Theme.Colors.white;
        }
    }

}
