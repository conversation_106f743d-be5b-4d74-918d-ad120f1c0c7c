import QtQuick 2.15

Item {
    id: root

    property color iconColor: Theme.Colors.primary // Default yellow
    property color borderColor: Theme.Colors.black
    property bool isActive: true

    width: 48
    height: 48

    // Triangle with exclamation mark
    Canvas {
        id: canvas

        anchors.fill: parent
        onPaint: {
            var ctx = getContext("2d");
            // Draw warning triangle
            ctx.beginPath();
            ctx.moveTo(width / 2, 0); // Top point
            ctx.lineTo(0, height); // Bottom left
            ctx.lineTo(width, height); // Bottom right
            ctx.closePath();
            // Fill with warning color
            ctx.fillStyle = root.isActive ? root.iconColor : "#e0e0e0";
            ctx.fill();
            // Draw border
            ctx.lineWidth = 2;
            ctx.strokeStyle = root.borderColor;
            ctx.stroke();
            // Draw exclamation mark
            ctx.fillStyle = "black";
            // Exclamation point dot
            ctx.beginPath();
            ctx.arc(width / 2, height - height / 4, width / 12, 0, 2 * Math.PI);
            ctx.fill();
            // Exclamation point line
            ctx.beginPath();
            ctx.rect(width / 2 - width / 12, height / 3, width / 6, height / 2.5);
            ctx.fill();
        }
    }

}
