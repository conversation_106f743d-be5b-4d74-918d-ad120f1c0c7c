import QtQuick 2.15

Image {
    id: svgIcon

    // Properties
    property string iconSource: ""
    property string iconColor: Theme.Colors.white // Default white for dark backgrounds

    source: iconSource
    sourceSize.width: width
    sourceSize.height: height
    smooth: true
    antialiasing: true
    // Apply color overlay to the SVG (works for single-color SVGs)
    layer.enabled: true

    layer.effect: ShaderEffect {
        property color color: svgIcon.iconColor

        fragmentShader: "
            varying highp vec2 qt_TexCoord0;
            uniform sampler2D source;
            uniform lowp float qt_Opacity;
            uniform highp vec4 color;
            void main() {
                highp vec4 pixelColor = texture2D(source, qt_TexCoord0);
                gl_FragColor = vec4(color.r, color.g, color.b, pixelColor.a) * qt_Opacity;
            }
        "
    }

}
