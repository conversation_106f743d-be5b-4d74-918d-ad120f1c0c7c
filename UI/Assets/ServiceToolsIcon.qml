import QtQuick 2.15

Item {
    id: root

    property color iconColor: Theme.Colors.white // Default white
    property bool isActive: true

    width: 48
    height: 48

    // Service/Tools icon
    Canvas {
        id: canvas

        anchors.fill: parent
        onPaint: {
            var ctx = getContext("2d");
            var centerX = width / 2;
            var centerY = height / 2;
            // Draw wrench
            ctx.beginPath();
            // Drawing a simplified wrench shape
            var handleWidth = width * 0.25;
            var handleHeight = height * 0.65;
            var headWidth = width * 0.45;
            var headHeight = height * 0.25;
            // Handle
            ctx.rect(centerX - handleWidth / 2, centerY - handleHeight / 2 + headHeight * 0.2, handleWidth, handleHeight);
            // Head
            ctx.rect(centerX - headWidth / 2, centerY - handleHeight / 2 - headHeight / 2, headWidth, headHeight);
            // Fill
            ctx.fillStyle = root.isActive ? root.iconColor : "#A0A0A0";
            ctx.fill();
            ctx.lineWidth = 2;
            ctx.strokeStyle = root.isActive ? "#000000" : "#707070";
            ctx.stroke();
            // Draw screwdriver
            ctx.beginPath();
            // Simplify by drawing a rotated screwdriver
            ctx.save();
            ctx.translate(centerX, centerY);
            ctx.rotate(Math.PI / 4); // 45 degrees
            // Handle
            var sdHandleWidth = width * 0.12;
            var sdHandleHeight = height * 0.4;
            var sdTipWidth = width * 0.06;
            var sdTipHeight = height * 0.25;
            ctx.rect(-sdHandleWidth / 2, -sdHandleHeight / 2 - sdTipHeight / 2, sdHandleWidth, sdHandleHeight);
            // Tip
            ctx.rect(-sdTipWidth / 2, sdHandleHeight / 2 - sdTipHeight / 2, sdTipWidth, sdTipHeight);
            ctx.restore();
            // Fill
            ctx.fillStyle = root.isActive ? root.iconColor : "#A0A0A0";
            ctx.fill();
            ctx.lineWidth = 2;
            ctx.strokeStyle = root.isActive ? "#000000" : "#707070";
            ctx.stroke();
        }
    }

}
