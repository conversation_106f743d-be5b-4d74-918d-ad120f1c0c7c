import QtQuick 2.15
import QtQuick.Controls 2.15

// Simple barcode placeholder
Rectangle {
    id: barcodePlaceholder

    width: 120
    height: 80
    color: Theme.Colors.white

    // Vertical lines for barcode effect
    Canvas {
        anchors.fill: parent
        anchors.margins: 5
        onPaint: {
            var ctx = getContext("2d");
            ctx.fillStyle = Theme.Colors.black;
            // Draw random vertical bars
            var x = 0;
            while (x < width) {
                var barWidth = Math.random() * 4 + 1;
                if (Math.random() > 0.5)
                    ctx.fillRect(x, 0, barWidth, height * 0.8);

                x += barWidth + Math.random() * 3 + 1;
            }
            // Draw the bottom text area
            ctx.fillStyle = Theme.Colors.white;
            ctx.fillRect(0, height * 0.8, width, height * 0.2);
            // Draw text
            ctx.fillStyle = Theme.Colors.black;
            ctx.font = "8px sans-serif";
            ctx.fillText("123456789012", 10, height * 0.95);
        }
    }

}
