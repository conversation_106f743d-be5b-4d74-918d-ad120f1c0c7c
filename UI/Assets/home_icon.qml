import QtQuick 2.15

Rectangle {
    width: 24
    height: 24
    color: Theme.Colors.transparent

    Canvas {
        anchors.fill: parent
        onPaint: {
            var ctx = getContext("2d");
            ctx.reset();
            // Roof
            ctx.beginPath();
            ctx.moveTo(12, 3);
            ctx.lineTo(22, 12);
            ctx.lineTo(19, 12);
            ctx.lineTo(19, 21);
            ctx.lineTo(5, 21);
            ctx.lineTo(5, 12);
            ctx.lineTo(2, 12);
            ctx.closePath();
            ctx.fillStyle = "black";
            ctx.fill();
            // Door
            ctx.beginPath();
            ctx.rect(10, 14, 4, 7);
            ctx.fillStyle = "white";
            ctx.fill();
        }
    }

}
