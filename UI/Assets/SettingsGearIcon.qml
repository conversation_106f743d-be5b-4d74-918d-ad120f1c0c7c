import QtQuick 2.15

Item {
    id: root

    property color iconColor: Theme.Colors.white // Default white
    property bool isActive: true

    width: 48
    height: 48

    // Gear icon
    Canvas {
        id: canvas

        anchors.fill: parent
        onPaint: {
            var ctx = getContext("2d");
            var centerX = width / 2;
            var centerY = height / 2;
            var outerRadius = Math.min(width, height) * 0.4;
            var innerRadius = outerRadius * 0.6;
            var teethCount = 8;
            ctx.beginPath();
            // Draw gear teeth
            for (var i = 0; i < teethCount; i++) {
                var angle1 = (i * 2 * Math.PI / teethCount);
                var angle2 = angle1 + Math.PI / teethCount * 0.5;
                var angle3 = angle1 + Math.PI / teethCount;
                var angle4 = angle1 + Math.PI / teethCount * 1.5;
                // Outer points
                var x1 = centerX + outerRadius * Math.cos(angle1);
                var y1 = centerY + outerRadius * Math.sin(angle1);
                var x3 = centerX + outerRadius * Math.cos(angle3);
                var y3 = centerY + outerRadius * Math.sin(angle3);
                // Tooth peaks
                var toothRadius = outerRadius * 1.3;
                var x2 = centerX + toothRadius * Math.cos(angle2);
                var y2 = centerY + toothRadius * Math.sin(angle2);
                var x4 = centerX + toothRadius * Math.cos(angle4);
                var y4 = centerY + toothRadius * Math.sin(angle4);
                if (i === 0)
                    ctx.moveTo(x1, y1);

                ctx.lineTo(x2, y2);
                ctx.lineTo(x3, y3);
                ctx.lineTo(x4, y4);
            }
            ctx.closePath();
            // Fill gear
            ctx.fillStyle = root.isActive ? root.iconColor : "#A0A0A0";
            ctx.fill();
            // Draw inner circle
            ctx.beginPath();
            ctx.arc(centerX, centerY, innerRadius, 0, 2 * Math.PI);
            ctx.fillStyle = root.isActive ? root.iconColor : "#A0A0A0";
            ctx.fill();
            // Draw center hole
            ctx.beginPath();
            ctx.arc(centerX, centerY, innerRadius * 0.3, 0, 2 * Math.PI);
            ctx.fillStyle = '#0C3348'; // background color
            ctx.fill();
        }
    }

}
