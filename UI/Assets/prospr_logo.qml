import QtQuick 2.15
import QtQuick.Controls 2.15

Item {
    id: logoContainer

    property int logoWidth: 200
    property int logoHeight: 75
    property string logoColor: Theme.Colors.secondary // Dark blue color from the logo
    property string accentColor: Theme.Colors.primary // Orange accent color (CALI · USA · EST. 1991)

    width: logoWidth
    height: logoHeight

    // Main Prospr logo
    Text {
        id: mainLogo

        text: "Prospr"
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.top: parent.top
        anchors.topMargin: parent.height * 0.15
        font.family: "Script" // You may need to import a custom font
        font.pixelSize: parent.height * 0.5
        font.weight: Font.Bold
        color: logoColor
    }

    // Tagline
    Text {
        id: tagline

        text: "MARK AMBITIOUSLY."
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        anchors.bottomMargin: parent.height * 0.15
        font.family: "Sans"
        font.pixelSize: parent.height * 0.15
        font.weight: Font.Bold
        color: logoColor
    }

    // Line under Prospr
    Rectangle {
        width: mainLogo.width * 0.6
        height: 2
        color: logoColor
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.top: mainLogo.bottom
        anchors.topMargin: 5
    }

    // California text
    Text {
        text: "CALI · USA · EST. 1991"
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.top: parent.top
        font.family: "Sans"
        font.pixelSize: parent.height * 0.1
        font.weight: Font.Bold
        color: accentColor
    }

}
