import QtQuick 2.15

Rectangle {
    width: 24
    height: 24
    color: Theme.Colors.transparent

    Canvas {
        anchors.fill: parent
        onPaint: {
            var ctx = getContext("2d");
            ctx.reset();
            // Head circle
            ctx.beginPath();
            ctx.arc(12, 8, 5, 0, Math.PI * 2);
            ctx.fillStyle = "black";
            ctx.fill();
            // Body shape
            ctx.beginPath();
            ctx.moveTo(6, 23);
            ctx.quadraticCurveTo(12, 15, 18, 23);
            ctx.closePath();
            ctx.fillStyle = "black";
            ctx.fill();
        }
    }

}
