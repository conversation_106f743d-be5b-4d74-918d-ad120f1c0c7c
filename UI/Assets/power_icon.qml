import QtQuick 2.15
import "qrc:/UI/Theme" as Theme

Item {
    id: root

    // Properties for customization
    property color backgroundColor: Theme.Colors.primary
    // Orange background
    property color iconColor: Theme.Colors.white
    // White icon
    property real backgroundOpacity: 1

    width: 40
    height: 40

    // Background circle
    Rectangle {
        id: backgroundCircle

        anchors.fill: parent
        radius: width / 2
        color: root.backgroundColor
        opacity: root.backgroundOpacity
    }

    // Power symbol - outer circle
    Rectangle {
        id: outerCircle

        width: parent.width * 0.6
        height: width
        radius: width / 2
        anchors.centerIn: parent
        color: Theme.Colors.transparent
        border.width: parent.width * 0.06
        border.color: root.iconColor

        // Create a gap at the top of the circle
        Rectangle {
            width: parent.border.width * 1.5
            height: parent.width * 0.3
            color: root.backgroundColor
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.bottom: parent.verticalCenter
            anchors.bottomMargin: parent.height * 0.5 - parent.border.width / 2
        }

    }

    // Power symbol - vertical line
    Rectangle {
        id: powerLine

        width: parent.width * 0.06
        height: parent.height * 0.3
        color: root.iconColor
        anchors.top: parent.top
        anchors.topMargin: parent.height * 0.2
        anchors.horizontalCenter: parent.horizontalCenter
    }

}
