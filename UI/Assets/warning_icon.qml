import QtQuick 2.15

Rectangle {
    width: 24
    height: 24
    color: Theme.Colors.transparent

    Canvas {
        anchors.fill: parent
        onPaint: {
            var ctx = getContext("2d");
            ctx.reset();
            // Warning triangle
            ctx.beginPath();
            ctx.moveTo(12, 2);
            ctx.lineTo(22, 22);
            ctx.lineTo(2, 22);
            ctx.closePath();
            ctx.fillStyle = "#FFA000"; // Amber color
            ctx.fill();
            // Exclamation mark
            ctx.beginPath();
            ctx.moveTo(12, 9);
            ctx.lineTo(12, 16);
            ctx.lineWidth = 2;
            ctx.strokeStyle = "white";
            ctx.stroke();
            // Dot
            ctx.beginPath();
            ctx.arc(12, 19, 1, 0, Math.PI * 2);
            ctx.fillStyle = "white";
            ctx.fill();
        }
    }

}
