// Colors.qml - Central color palette for the Light application
// Usage: import "qrc:/UI/Theme" as Theme
//        Then use as: Theme.Colors.primary, Theme.Colors.secondary, etc.

import QtQuick 2.15
pragma Singleton

QtObject {
    // Dark navy blue (RGB 0, 42, 64)
    // Warning state (used in StatusIndicators)
    // Light grey (RGB 200, 200, 200)
    // Alias for secondary color
    // Dark grey for secondary text

    // Main brand colors - Updated from color palette image (2025-04-04)
    // --- Brand Colors (from Figma, May 2025) ---
    readonly property color primary: "#F15B26"
    readonly property color primaryMedium: "#FEEFEA"
    readonly property color primaryLight: "#FFF2EE"
    // Prospr orange (RGB 241, 91, 38)
    readonly property color secondary: "#002A40"
    // --- Semantic Colors ---
    readonly property color red: "#CF1F25"
    // Red (RGB 207, 31, 37)
    readonly property color yellow: "#FFB818"
    // Yellow (RGB 255, 184, 24)
    readonly property color green: "#009E0F"
    // Green (RGB 0, 158, 15)
    // --- Status Indicator Colors ---
    readonly property color statusGreen: "#009E0F"
    readonly property color statusRed: "#CF1F25"
    readonly property color statusYellow: "#FFB818"
    readonly property color statusMidGrey: "#898888"
    readonly property color statusLightGrey: "#C8C8C8"
    //orange
    readonly property color orange: "#F26119"
    readonly property color selected: "#E0E8F0"
    readonly property color noSelected: "#F8F8F8"
    readonly property color borderCol: "#E0E0E0"
    readonly property color darkRed: "#BB0000"
    // Aliases for semantic colors used in components
    readonly property color success: green
    // Success state (used in StatusIndicators)
    readonly property color warning: yellow
    // --- Neutrals ---
    readonly property color black: "#1C1C1C"
    // Primary text (RGB 28, 28, 28)
    readonly property color white: "#FFFFFF"
    // White (RGB 255, 255, 255)
    readonly property color darkGrey: "#626262"
    // Dark grey (RGB 98, 98, 98)
    readonly property color midGrey: "#898888"
    // Mid grey (RGB 137, 136, 136)
    readonly property color lightGrey: "#C8C8C8"
    // Header background (RGB 230, 234, 236)
    readonly property color headerBackground: "#E6EAEC"
    // For captions
    readonly property color textGrey: "#6D6D6D"
    // --- UI/Utility Colors ---
    readonly property color inputBorder: lightGrey
    // Use Figma light grey for input borders
    readonly property color borderColor: lightGrey
    // Use Figma light grey for borders
    readonly property color shadow: "#22000000"
    // Semi-transparent black for shadows
    readonly property color transparent: "#00000000"
    // Transparent
    readonly property color darkBlue: secondary
    // UI color aliases for use throughout the app
    readonly property color backgroundPrimary: secondary
    // Main background
    readonly property color backgroundSecondary: "#00242F"
    // Test Input Background
    readonly property color inputBackground: "#FCFCFC"
    // Logo DropShadow (semi-transparent white)
    readonly property color logoDropShadow: "#33FFFFFF"
    readonly property color textGray: "#6D6D6D"
    // Container backgrounds
    readonly property color textPrimary: white
    // White text on dark backgrounds
    readonly property color textSecondary: darkGrey
    // Background gradient (legacy, optional)
    readonly property Gradient
    backgroundGradient: Gradient {
        GradientStop {
            position: 0
            color: white
        }

        GradientStop {
            position: 1
            color: "#F8E9E2"
        }

    }

    // Button states
    readonly property color primaryButtonPressed: Qt.darker(primary, 1.2)
    readonly property color secondaryButtonPressed: Qt.darker(secondary, 1.2)
    readonly property color tertiaryButtonColor: white
    // Status indicators (aliases to palette)
    readonly property color statusActive: green
    readonly property color statusInactive: midGrey
    readonly property color statusError: red
    readonly property color statusWarning: yellow

    function addOpacity(color, opacity) {
        return Qt.rgba(color.r, color.g, color.b, opacity);
    }

}
