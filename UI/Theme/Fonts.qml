// Fonts.qml - Central font definitions for the application
// Usage: import "qrc:/UI/Theme" as Theme
//        Then use as: Theme.Fonts.youngSerif, Theme.Fonts.clashGrotesk, etc.

import QtQuick 2.15
pragma Singleton

QtObject {
    // Load Young Serif font
    readonly property FontLoader
    youngSerifLoader: FontLoader {
        source: "qrc:/UI/fonts/YoungSerif-Regular.ttf"
    }

    readonly property string youngSerif: youngSerifLoader.name
    // Load Clash Grotesk font (Medium weight for consistency with request)
    readonly property FontLoader
    clashGroteskLoader: FontLoader {
        source: "qrc:/UI/fonts/ClashGrotesk-Medium.ttf"
    }

    readonly property string clashGrotesk: clashGroteskLoader.name
}
