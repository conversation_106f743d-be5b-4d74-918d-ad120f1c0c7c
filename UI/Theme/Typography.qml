// Typography.qml - Centralized typography definitions for the Light application
// Usage: import "qrc:/UI/Theme" as Theme
//        Then use as: Theme.Typography.title, Theme.Typography.body, etc.
// Updated April 2025 with new brand fonts

import QtQuick 2.15
pragma Singleton

QtObject {
    // 700 (Bold)
    // No Light in Figma system; not exposed
    // Young Serif: Regular only
    // Secondary font styles (Clash Grotesk) - used for body text, UI elements
    // Larger for better touch targets
    // Clash Grotesk: Body, buttons, labels, UI elements (Regular, Medium, Semibold, Bold)

    // --- Font Families (from Figma, May 2025) ---
    // Young Serif: Headings, highlights, emphasis (Regular only)
    readonly property string primaryFontFamily: "Young Serif, serif"
    // TODO: // Qt issue with font Clash Grotesk rendering, temporarily remove
    readonly property string secondaryFontFamily: "sans-serif"
    //"Clash Grotesk, sans-serif"
    // Monospace (for code/diagnostics only)
    readonly property string monospaceFontFamily: "UbuntuMono, DejaVuSansMono, LiberationMono, monospace"
    // --- Font Weights (Qt standard values) ---
    readonly property int weightNormal: Font.Normal
    // 400 (Regular)
    readonly property int weightMedium: Font.Medium
    // 500 (Medium)
    readonly property int weightSemiBold: 63
    // 600 (Semibold, Qt convention)
    readonly property int weightBold: Font.Bold
    // Font sizes for different components (in pixels)
    // Headers
    readonly property int h1: 32
    readonly property int h2: 28
    readonly property int h3: 24
    readonly property int h4: 20
    readonly property int h5: 18
    readonly property int h6: 16
    // Body text
    readonly property int bodyLarge: 16
    readonly property int bodyMedium: 14
    readonly property int bodySmall: 12
    // UI elements
    readonly property int formTitleText: 46
    readonly property int buttonText: 16
    readonly property int inputText: 16
    readonly property int labelText: 14
    readonly property int helperText: 12
    // Spacing and letter spacing
    readonly property real letterSpacingTight: -0.5
    readonly property real letterSpacingNormal: 0
    readonly property real letterSpacingWide: 0.5
    readonly property real letterSpacingExtraWide: 1
    // --- Pre-configured Text Styles (use with Text.font) ---
    // Headings: Young Serif, Regular only
    readonly property var heading: {
        "pixelSize": h1,
        "family": primaryFontFamily,
        "weight": weightNormal,
        "letterSpacing": letterSpacingTight
    }
    // Subtitle: Young Serif, Regular
    readonly property var subtitle: {
        "pixelSize": h3,
        "family": primaryFontFamily,
        "weight": weightNormal,
        "letterSpacing": letterSpacingNormal
    }
    // Body: Clash Grotesk, Regular
    readonly property var body: {
        "pixelSize": bodyLarge,
        "family": secondaryFontFamily,
        "weight": weightNormal,
        "letterSpacing": letterSpacingNormal
    }
    // Button: Clash Grotesk, Medium
    readonly property var button: {
        "pixelSize": buttonText,
        "family": secondaryFontFamily,
        "weight": weightMedium,
        "letterSpacing": letterSpacingWide
    }
    // Label: Clash Grotesk, Semibold
    readonly property var label: {
        "pixelSize": labelText,
        "family": secondaryFontFamily,
        "weight": weightSemiBold,
        "letterSpacing": letterSpacingNormal
    }
    // Helper: Clash Grotesk, Regular
    readonly property var helper: {
        "pixelSize": helperText,
        "family": secondaryFontFamily,
        "weight": weightNormal,
        "letterSpacing": letterSpacingExtraWide
    }
    readonly property var highlight: {
        "pixelSize": h4,
        "family": primaryFontFamily,
        "weight": weightNormal,
        "letterSpacing": letterSpacingNormal
    }
    // For touch-optimized industrial displays
    readonly property var industrial: {
        "pixelSize": 20,
        "family": secondaryFontFamily,
        "weight": weightBold,
        "letterSpacing": letterSpacingWide
    }
}
