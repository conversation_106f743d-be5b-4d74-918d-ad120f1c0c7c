// Shadows.qml - Centralized shadow definitions for the Light application
// This is a simplified version with basic border shadows instead of drop shadows
// to avoid compatibility issues

import QtQuick 2.15
pragma Singleton

QtObject {
    // Shadow properties
    readonly property color shadowColor: "#d0d0d0"
    // Light grey (matching borderColor)
    readonly property int shadowWidth: 1

    // Dummy function that simply sets a border on the element instead of a shadow
    // This avoids the complexity of dynamic QML object creation
    function applyElevation(targetItem, elevationLevel) {
        // Just ignore if not a valid target
        if (!targetItem)
            return ;

        // If the targetItem has a border property, apply a simple border instead
        if (targetItem && targetItem.hasOwnProperty("border")) {
            targetItem.border.width = shadowWidth;
            targetItem.border.color = shadowColor;
        }
    }

}
