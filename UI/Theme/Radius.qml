// Radius.qml - Centralized radius definitions for the Light application
// Usage: import "qrc:/UI/Theme" as Theme
//        Then use as: Theme.Radius.small, Theme.Radius.medium, etc.

import QtQuick 2.15
pragma Singleton

QtObject {
    // 4px

    // Base radius unit (in pixels)
    readonly property int unit: 4
    // Named radius values
    readonly property int none: 0
    readonly property int small: unit // 4px
    readonly property int medium: unit * 2 // 8px
    readonly property int large: unit * 3 // 12px
    readonly property int xlarge: unit * 4 // 16px
    readonly property int xmlarge: unit * 5 //20px
    readonly property int xxlarge: unit * 6 // 24px
    readonly property int xxmlarge: unit * 8 // 32px
    readonly property int xxxlarge: unit * 10 // 40px
    readonly property int xxxmlarge: unit * 12 // 48px
    readonly property int round: 9999 // Fully rounded (for circles)
    // Component specific radius
    readonly property int button: medium
    // 8px
    readonly property int card: large
    // 12px
    readonly property int container: large
    // 12px
    readonly property int dialog: xlarge
    // 16px
    readonly property int input: medium
    // 8px
    readonly property int panel: large
    // 12px
    readonly property int tooltip: small
    readonly property int item: unit * 10 // 40px
}
