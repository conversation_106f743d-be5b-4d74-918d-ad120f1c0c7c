// Spacing.qml - Centralized spacing definitions for the Light application
// Usage: import "qrc:/UI/Theme" as Theme
//        Then use as: Theme.Spacing.small, Theme.Spacing.medium, etc.

import QtQuick 2.15
pragma Singleton

QtObject {
    // Minimum spacing between touchable elements
    // 16px
    // 24px

    // Base spacing unit (in pixels)
    readonly property int unit: 8
    // Named spacing values
    readonly property int none: 0
    readonly property int xxxsmall: unit / 4 // 2px
    readonly property int xxsmall: unit / 2 // 4px
    readonly property int xsmall: unit // 8px
    readonly property int xssmall: unit + 2 // 10px
    readonly property int small: unit * 2 // 16px
    readonly property int usmall: (unit * 2) + 4 // 20px
    readonly property int medium: unit * 3 // 24px
    readonly property int large: unit * 4 // 32px
    readonly property int xlarge: unit * 6 // 48px
    readonly property int xxlarge: unit * 8 // 64px
    readonly property int xxxlarge: unit * 12 // 96px
    // Industrial UI specific (touch-optimized)
    readonly property int touchableMinHeight: 50
    // Minimum height for touchable elements
    readonly property int touchableMinWidth: 50
    // Minimum width for touchable elements
    readonly property int touchableSpacing: unit * 2
    // Component specific spacing
    readonly property int buttonPadding: unit * 2
    // 16px
    readonly property int cardPadding: unit * 2
    // 16px
    readonly property int containerMargin: unit * 3
    // 24px
    readonly property int inputFieldHeight: unit * 6
    // 48px
    readonly property int inputFieldPadding: unit * 2
    // 16px
    readonly property int listItemHeight: unit * 6
    // 48px
    readonly property int listItemPadding: unit * 2
    // Screen layout guidance
    readonly property int screenMargin: unit * 3
    // 24px
    readonly property int sectionSpacing: unit * 4
    // 32px
    readonly property int bottomBarHeight: unit * 10
    // 80px
    readonly property int dialogPadding: unit * 3
    readonly property int cardButtonMinHeight: 150
    readonly property int cardButtonImplicitWidth: 600
    readonly property int cardButtonImplicitHeight: 426
}
