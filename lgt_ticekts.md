# Light Project Tickets

---

## LGT-0002: Default to <PERSON> <PERSON>gin on Startup

**Description:**  
On application startup, the user should be automatically logged in as "Guest" by default, unless another user session is active. This ensures the application is always in a usable state and avoids requiring manual login for basic access.

**Reference Files/Context:**  
- `UI/LoginPage.qml`
- `UI/HomePage.qml`
- Backend user/session management (e.g., `UserManager.cpp/.h` if applicable)

**Acceptance Criteria:**  
- On app launch, "Guest" user is logged in by default.
- If a user logs out, revert to "Guest" session.
- No login dialog is shown unless explicitly logging in as another user.
- The UI reflects "Guest" status (e.g., limited permissions, user indicator).

**Helpful Notes:**  
- Ensure logic does not interfere with persistent user sessions if implemented.
- Test both embedded/device and development modes.

---

## LGT-0003: Print Page – Baseline Parameters and Sizing Ratio

**Description:**  
Implement logic to obtain baseline parameters for print preview and calculate sizing ratios. Additionally, evaluate and (if feasible) implement touch interaction for moving text elements around the preview area.

**Reference Files/Context:**  
- `UI/PrintPage.qml`
- `UI/PrintPreview.qml`
- Any backend logic for print parameters

**Acceptance Criteria:**  
- Print preview uses correct baseline parameters and sizing ratios.
- Users can move text objects with touch (if not cost-prohibitive).
- Touch-move feature is documented and tested for usability.
- If not feasible, provide a brief cost/complexity estimate.

**Helpful Notes:**  
- Coordinate with UX/UI for gesture standards.
- Consider performance on embedded hardware.

---

## LGT-0004: Data Insert Page – Add Text Feature

**Description:**  
Enable users to add new text fields to the Data Insert page, supporting label customization or data entry.

**Reference Files/Context:**  
- `UI/InsertData.qml`

**Acceptance Criteria:**  
- "Add Text" button or action is present and functional.
- New text fields appear in the insert area and are editable.
- All new text fields are included in data submission/printing.

**Helpful Notes:**  
- Follow existing field styling and validation.
- Check for maximum allowed text fields.

---

## LGT-0005: Error Page Improvements

**Description:**  
Refactor the Error Page with the following improvements:
- "Reset" only resets the status indicator, not error clearing.
- Pending errors display up to 9, then "+" for overflow.
- Consider removing floating error frame.
- Pending errors only shown on Error screen.
- After 30 seconds, pending errors are released; provide a visual indicator (e.g., circle) for unseen errors.
- Use words, not icons, for error descriptions.
- Implement a capacity check for errors.

**Reference Files/Context:**  
- `UI/ErrorLog.qml`
- `UI/ErrorList.qml`
- `UI/ErrorIndicator.qml`
- Error handling logic in backend

**Acceptance Criteria:**  
- All listed behaviors are implemented and verified.
- UI changes match design guidelines.
- Error capacity is enforced and handled gracefully.

**Helpful Notes:**  
- Review with product owner for floating frame removal.
- Document new error visual indicators.

---

## LGT-0006: Service Mode Page – Exit Indication Popup

**Description:**  
Display a popup when exiting Service Mode: “On exit machine mechanics will turn back-up”.

**Reference Files/Context:**  
- `UI/ServiceModePage.qml` or `UI/ServiceScreen.qml`

**Acceptance Criteria:**  
- Popup appears on Service Mode exit.
- Popup text matches requirement.
- User must acknowledge popup before exit completes.

**Helpful Notes:**  
- Popup should follow app modal style.
- Ensure popup does not block emergency exits.

---

## LGT-0007: Status Page – Green Hue for Good Status

**Description:**  
Add a green hue or indicator to the Status Page to visually signify that all systems are functioning normally.

**Reference Files/Context:**  
- `UI/StatusPage.qml`

**Acceptance Criteria:**  
- Green indicator is visible when status is "good".
- Indicator is removed/changed if status is not good.

**Helpful Notes:**  
- Match green hue to design system.
- Confirm with QA what constitutes "all good".

---

## LGT-0008: Function Page – Jet Status and Countdown Configuration

**Description:**  
- BUG: If jet is off, status should always be red.
- Make solvent (default 10s) and ink (default 5s) countdowns configurable by the user.

**Reference Files/Context:**  
- `UI/FunctionPage.qml`

**Acceptance Criteria:**  
- Jet status correctly displays red when off.
- Solvent and ink countdowns can be set via UI/config.

**Helpful Notes:**  
- Validate user input for countdowns.
- Add tooltips/help text as needed.

---

## LGT-0009: Phase Page – Greyed Out Text Field Bug

**Description:**  
Fix the bug where a text field is incorrectly greyed out on the Phase page, preventing user interaction when it should be enabled.

**Reference Files/Context:**  
- `UI/PhasePage.qml`

**Acceptance Criteria:**  
- Text field is only greyed out when appropriate.
- Field is interactive as intended.

**Helpful Notes:**  
- Check for logic in both QML and backend affecting enabled state.

---

## LGT-0010: User Page – Guest/Admin Edit/Delete Restrictions

**Description:**  
Prevent deletion or editing of Guest or Admin user accounts. Show a popup explaining the restriction if attempted.

**Reference Files/Context:**  
- `UI/UserPage.qml`
- `UI/UserList.qml`

**Acceptance Criteria:**  
- Edit/delete actions for Guest/Admin are blocked.
- Popup provides clear feedback.

**Helpful Notes:**  
- Ensure this is enforced both in UI and backend.

---

## LGT-0011: User Login Card – Incorrect Info on New User Login

**Description:**  
Fix the bug where user info is not updated correctly after logging in as a new user.

**Reference Files/Context:**  
- `UI/UserLoginCard.qml`
- `UI/UserPage.qml`
- User session logic

**Acceptance Criteria:**  
- User info is always accurate after login.
- No stale or incorrect data shown.

**Helpful Notes:**  
- Test with multiple user roles and login/logout cycles.

---

## LGT-0012: Printer Setting Selection Page – Confirm on Back

**Description:**  
After selecting a new printer setting, prompt the user to confirm the selection when pressing the back button.

**Reference Files/Context:**  
- `UI/PrinterSettingSelectionPage.qml`
- `UI/PrinterSettings.qml`

**Acceptance Criteria:**  
- Confirmation dialog appears on back after selection.
- User can confirm or cancel the change.

**Helpful Notes:**  
- Dialog should match app modal style.
- Confirm/cancel actions should be clearly distinguished.

---

## LGT-0013: Printer Setting Page – Remove Preview Panel

**Description:**  
Remove the Preview Panel from the Printer Setting page, simplifying the UI.

**Reference Files/Context:**  
- `UI/PrinterSettings.qml`

**Acceptance Criteria:**  
- Preview Panel is no longer present.
- Page layout adjusts accordingly.

**Helpful Notes:**  
- Ensure no references or logic depend on the panel after removal.
