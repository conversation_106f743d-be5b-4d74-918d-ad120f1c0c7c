# 📦 Changelog

All notable changes to **qt-aarch64-builder** will be documented in this file.

This project adheres to [Semantic Versioning](https://semver.org/).

---


## [1.1.0] - 2025-04-01
### Added
- Font management system for cross-platform deployment
- Update build_and_verify_mvp.sh to include target device deployment and build type for release or debug

### Changed
- Updated Dockerfile to include font management scripts
- Updated build_and_verify_mvp.sh to include font management

### Removed
- Legacy shell scripts



## [1.0.0] - 2025-03-20
### Added
- Initial Dockerfile with Linaro GCC 7.4.1 toolchain and Qt 5.15.4
- `docker-qmake` wrapper for Qt Creator integration
- `docker-make` wrapper for CMake/Make builds
- `build_and_push.sh` script for Docker Hub publishing
- `build_local.sh` for local-only builds with version tagging
- `README.md` with setup instructions
- `DEVELOPER_ONBOARDING.md` for collaborator push access
- `VERSION` and `<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>` for tracking changes
- MIT `LICENSE`

## 🆕 Added
- **Makefile** for centralized Docker build/development tooling.
  - Supports `docker-build`, `docker-shell`, `docker-qmake`, `docker-make`, and `build_and_verify`.
- **Docker build support** for:
  - Qt 5.15.4 (desktop x86_64 via manual installer or extracted tarball)
  - Qt 5.15.4 (cross-compile for aarch64 via EmbedSky toolchain)
- **`qt-test-app`**
  - Minimal working test app using `QLabel`
  - Scripts for building and verifying on aarch64 and x86_64
  - `deploy_and_run.sh` for SCP + SSH execution on target device
- **Toolchains Directory**
  - `qt-x86_64-5.15.4-setup/` includes `.tar.xz` archive and Qt Installer scripting for non-interactive setup
  - `EmbedSky` toolchain included in `toolchains/` instead of `opt/`
- **Docker wrapper scripts**:
  - `docker-qmake.sh`, `docker-qmake-desktop.sh`, and `docker-make.sh` support automatic context-relative execution
- **Build script for MVP project**: `build_and_verify_mvp.sh`
- **New `VERSION` file** to track current image version (updated to `1.0.1`)

## 🔁 Changed
- **.gitignore** extended to ignore:
  - `/build/` directories
  - `/toolchains/` folder
  - Qt tarballs and source extraction directories
- **Dockerfile**
  - Embedded system Qt moved to `toolchains/EmbedSky`
  - Optional Qt Desktop support added (with placeholder for tarball extraction)
  - Installed additional packages (`libgl1-mesa-dev`, `libxcb`, `libx11`, Python, etc.)
  - Cleaned up Docker layout and defaulted `WORKDIR` to `/workspace`
- **qmake/make docker scripts**
  - Updated to be repo-relative with `REL_DIR` logic
  - Corrected qmake binary path to point into EmbedSky folder

## 🗑 Removed
- Legacy shell scripts:
  - `build_local.sh`
  - `build_and_push.sh`

## ⚙️ Internal / Tooling
- Switched to using local tarball + non-interactive Qt installer script
- Added support for both desktop and embedded targets via `Makefile`
- Submodule `qt-mvp` now marked dirty (`-dirty`) in Git (indicates local changes)

---

## [Unreleased]
### Planned
- GitHub Actions CI to auto-build and push on new tags
- Version pruning script for old images
- Multi-arch support using Docker `buildx`