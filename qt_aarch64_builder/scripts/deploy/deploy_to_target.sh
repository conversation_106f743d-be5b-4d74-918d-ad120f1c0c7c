#!/bin/bash
set -e

# Colors for output
GREEN="\033[0;32m"
RED="\033[0;31m"
YELLOW="\033[0;33m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Default values
TARGET_IP=""
TARGET_USER="root"
TARGET_DIR="/opt/light"
BUILD_DIR=""
APP_NAME="light_qt5"
DEPLOY_MODE="standard"  # Options: standard, fonts-only, config-only

# Display usage information
function show_usage {
    echo -e "${BLUE}Usage: $0 -i <TARGET_IP> [-u <USER>] [-d <TARGET_DIR>] [-b <BUILD_DIR>] [-m <DEPLOY_MODE>] [-h]${NC}"
    echo "  -i TARGET_IP     IP address of the target device (required)"
    echo "  -u USER          Username for SSH connection (default: root)"
    echo "  -d TARGET_DIR    Destination directory on target (default: /opt/light)"
    echo "  -b BUILD_DIR     Source build directory (default: auto-detect from repo root)"
    echo "  -m DEPLOY_MODE   Deploy mode: standard, fonts-only, config-only"
    echo "  -h               Display this help message"
    exit 1
}

# Parse command-line options
while getopts "i:u:d:b:m:h" opt; do
    case $opt in
        i) TARGET_IP="$OPTARG" ;;
        u) TARGET_USER="$OPTARG" ;;
        d) TARGET_DIR="$OPTARG" ;;
        b) BUILD_DIR="$OPTARG" ;;
        m) DEPLOY_MODE="$OPTARG" ;;
        h) show_usage ;;
        *) show_usage ;;
    esac
done

# Validate required parameters
if [ -z "$TARGET_IP" ]; then
    echo -e "${RED}Error: Target IP (-i) is required${NC}"
    show_usage
fi

# Auto-detect build directory if not specified
if [ -z "$BUILD_DIR" ]; then
    REPO_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../../.." && pwd)"
    BUILD_DIR="$(find "$REPO_ROOT" -type d -name "build-aarch64-*" | head -n 1)"
    
    if [ -z "$BUILD_DIR" ]; then
        echo -e "${RED}Error: Could not auto-detect build directory. Please specify with -b${NC}"
        exit 1
    fi
    
    echo -e "${YELLOW}Auto-detected build directory: ${BUILD_DIR}${NC}"
fi

# Check if build directory exists
if [ ! -d "$BUILD_DIR" ]; then
    echo -e "${RED}Error: Build directory '$BUILD_DIR' does not exist${NC}"
    exit 1
fi

# Check SSH connection
echo -e "${BLUE}Checking SSH connection to ${TARGET_USER}@${TARGET_IP}...${NC}"
if ! ssh -o ConnectTimeout=5 "${TARGET_USER}@${TARGET_IP}" "echo Connected"; then
    echo -e "${RED}Error: Could not connect to ${TARGET_USER}@${TARGET_IP}${NC}"
    exit 1
fi

# Create target directory if it doesn't exist
echo -e "${BLUE}Creating target directory ${TARGET_DIR} if it doesn't exist...${NC}"
ssh "${TARGET_USER}@${TARGET_IP}" "mkdir -p ${TARGET_DIR}"

# Deploy based on mode
case "$DEPLOY_MODE" in
    "standard")
        # Deploy application binary and resources
        echo -e "${BLUE}Deploying application to ${TARGET_USER}@${TARGET_IP}:${TARGET_DIR}...${NC}"
        
        # Deploy binary
        BINARY="${BUILD_DIR}/${APP_NAME}"
        if [ ! -f "$BINARY" ]; then
            echo -e "${RED}Error: Binary '$BINARY' not found${NC}"
            exit 1
        fi
        
        echo -e "${YELLOW}Deploying binary...${NC}"
        scp "$BINARY" "${TARGET_USER}@${TARGET_IP}:${TARGET_DIR}/"
        
        # Deploy QML files and resources
        echo -e "${YELLOW}Deploying QML files and resources...${NC}"
        find "$BUILD_DIR" -name "*.qml" -o -name "*.png" -o -name "*.ttf" | while read -r file; do
            rel_path="${file#$BUILD_DIR/}"
            target_path="${TARGET_DIR}/${rel_path}"
            target_dir=$(dirname "$target_path")
            
            ssh "${TARGET_USER}@${TARGET_IP}" "mkdir -p \"${target_dir}\""
            scp "$file" "${TARGET_USER}@${TARGET_IP}:${target_path}"
        done
        
        # Set permissions
        echo -e "${YELLOW}Setting executable permissions...${NC}"
        ssh "${TARGET_USER}@${TARGET_IP}" "chmod +x ${TARGET_DIR}/${APP_NAME}"
        ;;
        
    "fonts-only")
        # Deploy only fonts
        echo -e "${BLUE}Deploying fonts to ${TARGET_USER}@${TARGET_IP}:${TARGET_DIR}/fonts...${NC}"
        ssh "${TARGET_USER}@${TARGET_IP}" "mkdir -p ${TARGET_DIR}/fonts"
        
        # Find and copy font files
        find "$BUILD_DIR" -name "*.ttf" -o -name "*.otf" | while read -r font; do
            scp "$font" "${TARGET_USER}@${TARGET_IP}:${TARGET_DIR}/fonts/"
        done
        ;;
        
    "config-only")
        # Deploy only configuration files
        echo -e "${BLUE}Deploying configuration files to ${TARGET_USER}@${TARGET_IP}:${TARGET_DIR}/config...${NC}"
        ssh "${TARGET_USER}@${TARGET_IP}" "mkdir -p ${TARGET_DIR}/config"
        
        # Find and copy configuration files
        find "$BUILD_DIR" -name "*.conf" -o -name "*.json" -o -name "*.ini" | while read -r config; do
            scp "$config" "${TARGET_USER}@${TARGET_IP}:${TARGET_DIR}/config/"
        done
        ;;
        
    *)
        echo -e "${RED}Error: Unknown deploy mode '$DEPLOY_MODE'${NC}"
        show_usage
        ;;
esac

echo -e "${GREEN}Deployment complete!${NC}"

# Provide run command for convenience
echo -e "${BLUE}To run the application on the target device:${NC}"
echo -e "  ssh ${TARGET_USER}@${TARGET_IP} \"cd ${TARGET_DIR} && ./${APP_NAME}\""

exit 0
