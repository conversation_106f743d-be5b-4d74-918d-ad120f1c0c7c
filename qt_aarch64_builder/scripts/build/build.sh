#!/bin/bash
set -e

# Colors for output
GREEN="\033[0;32m"
RED="\033[0;31m"
YELLOW="\033[0;33m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Build configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
REPO_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
BUILDER_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
APP_NAME="light_qt5"

# Default configuration
TARGET="aarch64"  # Default to aarch64 target (options: aarch64, x86_64)
BUILD_TYPE="debug"  # Default to debug build (options: debug, release)
DEPLOY="no"  # Default to no deployment (options: yes, no)
TARGET_IP=""  # Target IP for deployment
CLEAN="no"  # Default to no clean build (options: yes, no)
VERIFY="yes"  # Default to verify build (options: yes, no)

# Display usage information
function show_usage {
    echo -e "${BLUE}Usage: $0 [OPTIONS]${NC}"
    echo "Options:"
    echo "  -t, --target <aarch64|x86_64>  Target architecture (default: aarch64)"
    echo "  -b, --build <debug|release>    Build type (default: debug)"
    echo "  -d, --deploy                   Deploy to target after build"
    echo "  -i, --ip <TARGET_IP>           Target IP for deployment"
    echo "  -c, --clean                    Perform a clean build"
    echo "  -n, --no-verify                Skip build verification"
    echo "  -h, --help                     Display this help message"
    exit 1
}

# Parse command-line options
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--target)
            TARGET="$2"
            shift 2
            ;;
        -b|--build)
            BUILD_TYPE="$2"
            shift 2
            ;;
        -d|--deploy)
            DEPLOY="yes"
            shift
            ;;
        -i|--ip)
            TARGET_IP="$2"
            shift 2
            ;;
        -c|--clean)
            CLEAN="yes"
            shift
            ;;
        -n|--no-verify)
            VERIFY="no"
            shift
            ;;
        -h|--help)
            show_usage
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            show_usage
            ;;
    esac
done

# Validate target
if [ "$TARGET" != "aarch64" ] && [ "$TARGET" != "x86_64" ]; then
    echo -e "${RED}Error: Invalid target '$TARGET'. Use 'aarch64' or 'x86_64'${NC}"
    exit 1
fi

# Validate build type
if [ "$BUILD_TYPE" != "debug" ] && [ "$BUILD_TYPE" != "release" ]; then
    echo -e "${RED}Error: Invalid build type '$BUILD_TYPE'. Use 'debug' or 'release'${NC}"
    exit 1
fi

# Check if deploy was requested without IP
if [ "$DEPLOY" = "yes" ] && [ -z "$TARGET_IP" ]; then
    echo -e "${RED}Error: Deployment requested but no target IP provided${NC}"
    echo -e "${YELLOW}Use -i/--ip to specify target IP for deployment${NC}"
    exit 1
fi

# Set platform-specific build directory
BUILD_DIR="$REPO_ROOT/build-$TARGET-$BUILD_TYPE"
BINARY="$BUILD_DIR/$APP_NAME"

echo -e "${BLUE}===== Build Configuration =====${NC}"
echo -e "${YELLOW}Target:${NC} $TARGET"
echo -e "${YELLOW}Build Type:${NC} $BUILD_TYPE"
echo -e "${YELLOW}Build Directory:${NC} $BUILD_DIR"
echo -e "${YELLOW}Clean Build:${NC} $CLEAN"
echo -e "${YELLOW}Verify Build:${NC} $VERIFY"
echo -e "${YELLOW}Deploy:${NC} $DEPLOY"
if [ "$DEPLOY" = "yes" ]; then
    echo -e "${YELLOW}Target IP:${NC} $TARGET_IP"
fi
echo -e "${BLUE}===============================${NC}"

# Clean build directory if requested
if [ "$CLEAN" = "yes" ]; then
    echo -e "${YELLOW}Cleaning build directory: $BUILD_DIR${NC}"
    rm -rf "$BUILD_DIR"
fi

# Create build directory
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# Build based on target architecture
if [ "$TARGET" = "aarch64" ]; then
    echo -e "${YELLOW}🏗️ Building for aarch64 target using Docker...${NC}"
    
    echo -e "${BLUE}🛠 Running docker-qmake...${NC}"
    "$BUILDER_ROOT/scripts/docker/docker-qmake.sh" "$REPO_ROOT/light_qt5.pro" "$BUILD_TYPE"
    
    echo -e "${BLUE}🧱 Running docker-make...${NC}"
    "$BUILDER_ROOT/scripts/docker/docker-make.sh"
    
elif [ "$TARGET" = "x86_64" ]; then
    echo -e "${YELLOW}🏗️ Building for x86_64 local host...${NC}"
    
    # Use local Qt installation
    echo -e "${BLUE}🛠 Running local qmake...${NC}"
    
    # Configure qmake based on build type
    if [ "$BUILD_TYPE" = "release" ]; then
        qmake "$REPO_ROOT/light_qt5.pro" CONFIG+=release
    else
        qmake "$REPO_ROOT/light_qt5.pro" CONFIG+=debug
    fi
    
    echo -e "${BLUE}🧱 Running local make...${NC}"
    make -j$(nproc)
    
else
    echo -e "${RED}❌ Unsupported target: $TARGET${NC}"
    echo -e "   Supported targets are: 'aarch64' or 'x86_64'"
    exit 1
fi

# Verify build
if [ "$VERIFY" = "yes" ]; then
    # Check if build was successful
    if [[ -f "$BINARY" ]]; then
        echo -e "${GREEN}✅ Build succeeded: $BINARY${NC}"
        
        # Print binary info
        echo -e "${BLUE}Binary information:${NC}"
        file "$BINARY"
        
        # Print binary size
        echo -e "${BLUE}Binary size:${NC}"
        du -h "$BINARY"
    else
        echo -e "${RED}❌ Build failed: $BINARY not found${NC}"
        exit 1
    fi
fi

# Deploy if requested
if [ "$DEPLOY" = "yes" ]; then
    echo -e "${YELLOW}Deploying to target device: $TARGET_IP${NC}"
    
    # Call deployment script
    "$BUILDER_ROOT/scripts/deploy/deploy_to_target.sh" -i "$TARGET_IP" -b "$BUILD_DIR"
    
    # Deployment script will handle error checking and reporting
fi

echo -e "${GREEN}✅ Build process completed successfully${NC}"
exit 0
