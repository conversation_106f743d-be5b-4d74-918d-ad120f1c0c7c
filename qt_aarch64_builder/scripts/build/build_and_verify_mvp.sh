#!/bin/bash
set -e

# Colors for output
GREEN="\033[0;32m"
RED="\033[0;31m"
YELLOW="\033[0;33m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Build configuration
REPO_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_DIR="$REPO_ROOT/qt-mvp"
APP_NAME="light_qt5"  # Name of the binary

# Parse command line arguments
TARGET="x86_64" # Default to x86_64 target
BUILD_TYPE="debug" # Default to debug build

# Parse command line options
while getopts ":t:b:h" opt; do
  case ${opt} in
    t )
      TARGET=$OPTARG
      ;;
    b )
      BUILD_TYPE=$OPTARG
      ;;
    h )
      echo -e "${BLUE}Usage: $0 [-t target] [-b build_type]${NC}"
      echo -e "  -t target      Build target: 'aarch64' (default) or 'x86_64'"
      echo -e "  -b build_type  Build type: 'release' (default) or 'debug'"
      echo -e "  -h             Show this help message"
      exit 0
      ;;
    \? )
      echo -e "${RED}Invalid option: -$OPTARG${NC}" 1>&2
      exit 1
      ;;
    : )
      echo -e "${RED}Option -$OPTARG requires an argument.${NC}" 1>&2
      exit 1
      ;;
  esac
done

# Set platform-specific build directory
BUILD_DIR="$APP_DIR/build-$TARGET-$BUILD_TYPE"
BINARY="$BUILD_DIR/$APP_NAME"

# Create build directory
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# Build based on target architecture
if [ "$TARGET" = "aarch64" ]; then
    echo -e "${YELLOW}🏗️ Building for aarch64 target using Docker...${NC}"
    
    echo -e "${BLUE}🛠 Running docker-qmake...${NC}"
    "$REPO_ROOT/docker-qmake.sh" ../light_qt5.pro
    
    echo -e "${BLUE}🧱 Running docker-make...${NC}"
    "$REPO_ROOT/docker-make.sh"
    
    # Check if build was successful
    if [[ -f "$BINARY" ]]; then
        echo -e "${GREEN}✅ aarch64 build succeeded: $BINARY${NC}"
        file "$BINARY"
    else
        echo -e "${RED}❌ aarch64 build failed: $BINARY not found${NC}"
        exit 1
    fi
    
elif [ "$TARGET" = "x86_64" ]; then
    echo -e "${YELLOW}🏗️ Building for x86_64 local host...${NC}"
    
    # Use local Qt installation
    echo -e "${BLUE}🛠 Running local qmake...${NC}"
    qmake ../light_qt5.pro
    
    echo -e "${BLUE}🧱 Running local make...${NC}"
    make
    
    # Check if build was successful
    if [[ -f "$BINARY" ]]; then
        echo -e "${GREEN}✅ x86_64 build succeeded: $BINARY${NC}"
        file "$BINARY"
    else
        echo -e "${RED}❌ x86_64 build failed: $BINARY not found${NC}"
        exit 1
    fi
    
else
    echo -e "${RED}❌ Unsupported target: $TARGET${NC}"
    echo -e "   Supported targets are: 'aarch64' or 'x86_64'"
    exit 1
fi

echo -e "${GREEN}✅ Build completed successfully${NC}"
