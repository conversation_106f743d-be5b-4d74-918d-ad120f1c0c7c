#!/bin/bash
# Move script onto board
# scp board_scripts/generate_hardware_md.sh root@**************:/root/
# Move generated file off board
# scp root@**************:/root/hardware_summary.md .

OUT_FILE="hardware_summary.md"

echo "# 📟 Allwinner T507 Embedded Platform – Developer Hardware Overview" > $OUT_FILE

echo -e "\n## 🧠 SoC: Allwinner T507" >> $OUT_FILE
cat /proc/cpuinfo | awk '
  BEGIN { seen = 0 }
  /processor/ { if (seen++) exit }
  { print " - " $0 }
' >> $OUT_FILE

echo -e "\n## 🧩 OS and Kernel" >> $OUT_FILE
echo -n "- OS: " >> $OUT_FILE
grep PRETTY_NAME /etc/os-release | cut -d= -f2 | tr -d '"' >> $OUT_FILE
echo "- Kernel: $(uname -a)" >> $OUT_FILE
echo "- Version Info: $(cat /proc/version)" >> $OUT_FILE

echo -e "\n## 💾 Memory Info" >> $OUT_FILE
grep MemTotal /proc/meminfo >> $OUT_FILE
grep MemFree /proc/meminfo >> $OUT_FILE
grep MemAvailable /proc/meminfo >> $OUT_FILE

echo -e "\n## 💽 Storage and Filesystem" >> $OUT_FILE
echo '```' >> $OUT_FILE
df -h >> $OUT_FILE
echo '```' >> $OUT_FILE

echo -e "\n## 📡 Network Interfaces" >> $OUT_FILE
ip addr show | grep -E '^[0-9]+:|inet ' >> $OUT_FILE

echo -e "\n## 🖥 Input Devices" >> $OUT_FILE
grep -A3 -B1 "input" /proc/bus/input/devices >> $OUT_FILE

echo -e "\n## 🖲 USB Devices" >> $OUT_FILE
lsusb >> $OUT_FILE 2>/dev/null || echo "- lsusb not available" >> $OUT_FILE

echo -e "\n## ⚠️ Touchscreen Support" >> $OUT_FILE
echo '```' >> $OUT_FILE
dmesg | grep -i touch >> $OUT_FILE
echo '```' >> $OUT_FILE

echo -e "\n✅ Markdown written to: $OUT_FILE"
