#!/bin/bash
set -e

# Determine repository root using git (or fallback to parent directory of this script)
REPO_ROOT=$(git rev-parse --show-toplevel 2>/dev/null || (cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd))
# Compute the current directory relative to the repo root using Python
REL_DIR=$(python3 -c "import os; print(os.path.relpath(os.getcwd(), '$REPO_ROOT'))")

docker run --rm \
    -v "$REPO_ROOT":/workspace \
    -w /workspace/"$REL_DIR" \
    ianlaue/qt-aarch64-builder:latest \
    make "$@"
