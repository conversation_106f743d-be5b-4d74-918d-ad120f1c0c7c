# Use Ubuntu as base image
FROM ubuntu:20.04

ARG VERSION
LABEL org.opencontainers.image.version=$VERSION

# Set non-interactive frontend to avoid timezone prompt
ENV DEBIAN_FRONTEND=noninteractive

# Install necessary tools
RUN apt-get update && apt-get install -y \
    build-essential cmake ninja-build rsync ssh-client tzdata vim wget \
    && rm -rf /var/lib/apt/lists/*

# Set timezone to UTC to avoid prompts
RUN ln -fs /usr/share/zoneinfo/UTC /etc/localtime && \
    dpkg-reconfigure --frontend noninteractive tzdata

# ==========================
RUN apt-get update && apt-get install -y \
    build-essential \
    python3 python3-dev \
    libgl1-mesa-dev libglu1-mesa-dev \
    libx11-dev libxext-dev libxrandr-dev libxrender-dev libxcb1-dev libx11-xcb-dev \
    && ln -s /usr/bin/python3 /usr/bin/python
# ==========================


# Set working directory
WORKDIR /opt

# Copy Linaro toolchain (Alternative: mount at runtime)
COPY ./toolchains/EmbedSky /opt/EmbedSky

# Set environment variables for AArch64 toolchain
ENV PATH="/opt/EmbedSky/gcc-linaro-7.4.1-2019.02-x86_64_aarch64-linux-gnu/bin:/opt/EmbedSky/gcc-linaro-7.4.1-2019.02-x86_64_aarch64-linux-gnu/qt_env/Qt-5.15.4/bin:${PATH}"
ENV CROSS_COMPILE="aarch64-linux-gnu-"
ENV SYSROOT="/opt/EmbedSky/gcc-linaro-7.4.1-2019.02-x86_64_aarch64-linux-gnu/sysroot"

# # Copy local Qt tarball and non-interactive installer script from toolchains folder
# COPY ./toolchains/qt-everywhere-opensource-src-x64-5.15.4-setup/ /tmp/qt-setup/

# # Create installation directory and extract the tarball, then clean up
# RUN mkdir -p /opt/qt-x86_64-5.15.4 && \
#     tar -xJf /tmp/qt-setup/qt-x86_64-5.15.4.tar.xz -C /opt/qt-x86_64-5.15.4 --strip-components=1 && \
#     rm -rf /tmp/qt-setup

# # Set environment for Qt x86_64 Desktop
# ENV QT_X86_64_DIR=/opt/qt-x86_64-5.15.4
# ENV PATH="${QT_X86_64_DIR}/bin:$PATH"

# Show toolchain and qmake versions to confirm install
RUN echo "Toolchain path: $PATH" && \
    echo "Sysroot path: $SYSROOT" && \
    aarch64-linux-gnu-gcc --version && \
    qmake --version


# Create a non-root user for development
# This is a good practice for security and permissions
# RUN useradd -ms /bin/bash devuser
# USER devuser
WORKDIR /workspace

CMD ["/bin/bash"]
