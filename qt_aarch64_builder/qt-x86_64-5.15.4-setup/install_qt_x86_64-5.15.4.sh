#!/bin/bash
set -e

QT_VERSION=5.15.4
# Use the new archive name provided
QT_ARCHIVE="qt-everywhere-opensource-src-${QT_VERSION}.tar.xz"
# Updated URL: typically, the archive is in the "single" directory for version 5.15.4.
QT_DOWNLOAD_URL="https://download.qt.io/archive/qt/5.15/5.15.4/single/$QT_ARCHIVE"
# Set the installation directory for the prebuilt Qt
INSTALL_DIR="/opt/qt-x86_64-5.15.4"

echo "📦 Downloading Qt ${QT_VERSION} archive for x86_64 from:"
echo "    $QT_DOWNLOAD_URL"

# --no-check-certificate
wget "$QT_DOWNLOAD_URL" -O ./toolchains/qt-everywhere-opensource-src-x64-5.15.4-setup/qt-x86_64-5.15.4.tar.xz # -O /tmp/$QT_ARCHIVE

# if [ ! -f /tmp/$QT_ARCHIVE ]; then
#   echo "❌ Failed to download the Qt archive!"
#   exit 1
# fi

# echo "📂 Extracting Qt archive to $INSTALL_DIR..."
# sudo mkdir -p "$INSTALL_DIR"
# sudo tar -xJf /tmp/$QT_ARCHIVE -C "$INSTALL_DIR" --strip-components=1

# echo "✅ Qt ${QT_VERSION} installed to $INSTALL_DIR."
