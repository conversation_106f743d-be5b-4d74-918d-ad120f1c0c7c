# Makefile to simplify Docker build/dev workflow

# Default values
HOST_EMBEDSKY ?= $(HOME)/prospr/EmbedSky
IMAGE_NAME ?= ianlaue/qt-aarch64-builder
DEFAULT_TAG ?= latest

# 🐳 Docker build
docker-build:
	@echo "🐳 Building Docker image..."
	docker build -t $(IMAGE_NAME):$(DEFAULT_TAG) .
	@echo "✅ Docker image built as $(IMAGE_NAME):$(DEFAULT_TAG)"
	@echo "🔖 To push the image, use 'make docker-push'"
	@echo "🔄 To run the image with shell, use 'make docker-shell'"
	

# 🐚 Interactive shell
docker-shell:
	@echo "🐳 Launching interactive shell in Docker..."
	docker run --rm -it \
		-v $(PWD):/workspace \
		-v $(HOST_EMBEDSKY):/opt/EmbedSky \
		-w /workspace \
		$(IMAGE_NAME):$(DEFAULT_TAG) \
		/bin/bash

# 📦 Build & push Docker image using VERSION file
docker-push:
	@echo "📦 Building and pushing Docker image..."
	@if [ ! -f VERSION ]; then \
		echo "❌ VERSION file not found. Please create one."; \
		exit 1; \
	fi
	@VERSION_TAG=$$(cat VERSION); \
	echo "🔖 Version: $$VERSION_TAG"; \
	docker build -t $(IMAGE_NAME):$$VERSION_TAG -t $(IMAGE_NAME):$(DEFAULT_TAG) .; \
	echo "🔐 Logging into Docker Hub..."; \
	docker login; \
	echo "🚀 Pushing to Docker Hub..."; \
	docker push $(IMAGE_NAME):$$VERSION_TAG; \
	docker push $(IMAGE_NAME):$(DEFAULT_TAG); \
	echo "✅ Done! Image pushed as:"; \
	echo "   - $(IMAGE_NAME):$$VERSION_TAG"; \
	echo "   - $(IMAGE_NAME):$(DEFAULT_TAG)"

# 📡 Deploy target (placeholder)
deploy:
	@echo "📡 Deploy not configured. Customize your deploy target."

# 🧼 Clean all build folders
clean:
	rm -rf qt-test-app/build
	rm -rf qt-mvp/build


.PHONY: build_and_verify

build_and_verify:
	@if [ -z "$(PROJECT)" ]; then \
	    echo "Error: PROJECT variable is not set. Usage: make build_and_verify PROJECT=<project_dir> TARGET=<desktop|aarch64|all>"; \
	    exit 1; \
	fi
	@if [ -z "$(TARGET)" ]; then \
	    echo "Error: TARGET variable is not set. Usage: make build_and_verify PROJECT=<project_dir> TARGET=<desktop|aarch64|all>"; \
	    exit 1; \
	fi
	@echo "Building project $(PROJECT) for target $(TARGET)..."
	@BUILD_DIR="$(PROJECT)/build"; \
	mkdir -p $$BUILD_DIR; \
	cd $$BUILD_DIR; \
	\
	if [ "$(TARGET)" = "desktop" ] || [ "$(TARGET)" = "all" ]; then \
	    echo "Running desktop qmake..."; \
	    if [ ! -f "../$(PROJECT).pro" ]; then \
	        echo "Error: Project file ../$(PROJECT).pro not found. Please ensure your .pro file is named correctly."; \
	        exit 1; \
	    fi; \
	    $(CURDIR)/docker-qmake-desktop.sh ../$(PROJECT).pro; \
	    echo "Running make for desktop..."; \
	    $(CURDIR)/docker-make.sh; \
	fi; \
	\
	if [ "$(TARGET)" = "aarch64" ] || [ "$(TARGET)" = "all" ]; then \
	    echo "Running aarch64 qmake..."; \
	    if [ ! -f "../$(PROJECT).pro" ]; then \
	        echo "Error: Project file ../$(PROJECT).pro not found. Please ensure your .pro file is named correctly."; \
	        exit 1; \
	    fi; \
	    $(CURDIR)/docker-qmake.sh ../$(PROJECT).pro; \
	    echo "Running make for aarch64..."; \
	    $(CURDIR)/docker-make.sh; \
	fi; \
	\
	if [ -f "$(PROJECT)/build/$(PROJECT)" ]; then \
	    echo "✅ Build succeeded: $(PROJECT)/build/$(PROJECT)"; \
	    file $(PROJECT)/build/$(PROJECT); \
	else \
	    echo "❌ Build failed: $(PROJECT)/build/$(PROJECT) not found"; \
	    exit 1; \
	fi
