# 🧑‍💻 Developer Onboarding: Pushing to Docker Hub

This guide explains how to securely push Docker images to the shared repository:  
📦 `ianlaue/qt-aarch64-builder`

---

## ✅ 1. Get Added as a Collaborator

Ask @ianlaue (or the repo owner) to:
1. Go to [https://hub.docker.com/repository/docker/ianlaue/qt-aarch64-builder](https://hub.docker.com/repository/docker/ianlaue/qt-aarch64-builder)
2. Click **"Settings" > "Collaborators"**
3. Add your **Docker Hub username**

You’ll then be able to push to the repo from your own account.

---

## 🔐 2. Log in to Docker Hub

Use the Docker CLI to authenticate:

```bash
docker login
```

- Enter your **Docker Hub username** and **personal access token** (recommended over a password)
- You’ll stay logged in until you log out or the session expires

---

## 🏗 3. Build and Tag the Image

Make sure your local repo is set up with the `Dockerfile` and `build_local.sh` script.

### Build a versioned image:
```bash
./build_local.sh --version 1.0.2
```

This will create:
- `ianlaue/qt-aarch64-builder:1.0.2`
- `ianlaue/qt-aarch64-builder:latest`

---

## 🚀 4. Push to Docker Hub

Once built, push both tags:

```bash
docker push ianlaue/qt-aarch64-builder:1.0.2
docker push ianlaue/qt-aarch64-builder:latest
```

✅ These images will now be available to pull from anywhere.

---

## 📥 5. Pulling the Image (for Others)

Anyone (on your team or in CI) can now pull the latest image:

```bash
docker pull ianlaue/qt-aarch64-builder:latest
```

Or pull a specific version:

```bash
docker pull ianlaue/qt-aarch64-builder:1.0.2
```

---

## 📎 Notes

- 🏷️ Use semantic versioning for clarity (`1.0.0`, `1.0.1`, etc.)
- 🔐 Only collaborators can push to the private repo
- 🧹 Old versions can be cleaned up manually by the owner via Docker Hub UI

---

## 🧼 Cleanup (Optional)

To remove local images and save disk space:

```bash
docker image rm ianlaue/qt-aarch64-builder:1.0.2
docker image rm ianlaue/qt-aarch64-builder:latest
```

---

## 🙋 Need Help?

Ping @ianlaue with your Docker Hub username or for access issues.