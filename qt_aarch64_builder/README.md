# qt-aarch64-builder

Dockerized build environment for cross-compiling Qt applications targeting **AArch64 Embedded Linux**. 
This toolkit provides a stable, consistent environment for developing the Prospr Light application for both desktop and embedded targets.

This environment wraps the **Linaro GCC 7.4.1 AArch64 toolchain** and **Qt 5.15.4**, making it easy to build industrial UI applications inside a clean, reproducible container.

---

## 🚀 Features

- AArch64 cross-compiler (Linaro GCC 7.4.1-2019.02)
- Qt 5.15.4 precompiled for AArch64 and x86_64
- Streamlined build workflows for both host and target platforms
- Target deployment tools for efficient testing
- Support for industrial UI/UX with large touch targets
- Font management for consistent appearance across platforms
- Docker containerization for consistent builds

---

## 📁 Directory Structure

The repository is organized following Qt development best practices:

```
qt_aarch64_builder/
├── Dockerfile               # Container definition for cross-compilation
├── config/                  # Environment and configuration files
│   └── qt_T507.env          # T507 target environment variables
├── scripts/                 # Organized script directories
│   ├── build/               # Build scripts for host and target
│   │   ├── build.sh         # Unified build script for all targets
│   │   └── build_and_verify_mvp.sh   # Legacy build script
│   ├── deploy/              # Deployment tools
│   │   └── deploy_to_target.sh  # Script to deploy to embedded device
│   ├── docker/              # Docker integration scripts
│   │   ├── docker-make.sh   # Run make in Docker container
│   │   ├── docker-qmake.sh  # Run qmake for cross-compilation
│   │   └── docker-qmake-desktop.sh  # Run qmake for desktop builds
│   └── board/               # Target hardware specific scripts
│       ├── generate_hardware_md.sh  # Generate hardware documentation
│       └── test_touch.sh    # Test touchscreen on target device
├── docs/                    # Documentation
│   ├── common/              # Documentation for all platforms
│   │   └── DEVELOPER_ONBOARDING.md  # Onboarding guide
│   ├── host/                # Host development documentation
│   └── target/              # Target platform documentation
│       └── hardware_summary.md  # Target hardware specifications
├── toolchains/              # Cross-compilation toolchains
│   └── EmbedSky/            # EmbedSky toolchain for T507
└── qt-x86_64-5.15.4-setup/  # Desktop Qt setup scripts
```

---

## 🛠️ Development Workflow

### Host-Target Development Cycle

This project follows a structured workflow:
1. Develop and test on host machine
2. Cross-compile for target using Docker toolchain
3. Deploy to target device for verification
4. Document changes and commit stable versions

See `context/PLANNING.md` for the complete development workflow.

---

## 📦 Setup Instructions

### 1. ⬇️ Download the EmbedSky Toolchain

Place the EmbedSky Linaro toolchain and Qt libraries in:
```
/opt/EmbedSky/gcc-linaro-7.4.1-2019.02-x86_64_aarch64-linux-gnu
```

> 🔗 Download from: [Google Drive](https://drive.google.com/drive/folders/1rGMoGRTuAmCO4MDz-JiEJvYbX_em-Wqs?usp=drive_link)

Verify installation with:
```bash
/opt/EmbedSky/gcc-linaro-7.4.1-2019.02-x86_64_aarch64-linux-gnu/qt_env/Qt-5.15.4/bin/qmake --version
```

### 2. 🚀 Build and Deploy

#### Building for Embedded Target (Allwinner T507)

```bash
# Build for AArch64 target
./scripts/build/build.sh --target aarch64 --build release

# Deploy to target device
./scripts/build/build.sh --target aarch64 --build release --deploy --ip <TARGET_IP>
```

#### Building for Local Development

```bash
# Build for local testing
./scripts/build/build.sh --target x86_64 --build debug
```

### 3. 📱 UI/UX Testing

The Prospr Light application is designed for industrial environments with:
- Large touch targets (minimum 50-60px height)
- High contrast UI elements
- Factory-optimized interfaces

When testing on the target device, verify these UI/UX principles are maintained.

---

## 🔄 Docker Integration

### Building Docker Image

```bash
# Build and tag Docker image
make docker-build VERSION=1.1.0

# Run interactive shell in Docker container
make docker-shell
```

### Qt Creator Integration

Set up Qt Creator with:
- `scripts/docker/docker-qmake.sh` as the Qt version
- `scripts/docker/docker-make.sh` as the Make tool
- Configure a Generic Linux Device for your target hardware

---

## 📋 Versioning and Changelog

This project adheres to [Semantic Versioning](https://semver.org/).
See `CHANGELOG.md` for a complete history of changes.

---

## 📜 License

This repository is provided as-is for internal development purposes. Please ensure you have proper licensing for any third-party libraries or toolchains used.

---

## 🙌 Credits

- [Linaro Toolchain](https://www.linaro.org/)
- [Qt](https://www.qt.io/)
- [Allwinner T507 SoC](https://www.allwinnertech.com/)
