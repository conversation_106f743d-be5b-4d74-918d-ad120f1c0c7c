QT += quick quickcontrols2 gui

# On Linux, ensure we use the right paths for QML modules
linux {
    QML_IMPORT_PATH += /usr/lib/x86_64-linux-gnu/qt5/qml
    QML_DESIGNER_IMPORT_PATH += /usr/lib/x86_64-linux-gnu/qt5/qml
}

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

# Get Qt version to handle compatibility between development and target
system("qmake -query QT_VERSION"): QT_CURRENT_VERSION = $$system("qmake -query QT_VERSION")
else: QT_CURRENT_VERSION = 0

# Define target Qt version (5.15.4)
QT_TARGET_VERSION = 5.15.4

# Fix SDK warning on macOS
macx {
    CONFIG += sdk_no_version_check
}

# Handle version differences - detect if using Qt 5.15.16 on Mac but targeting 5.15.4
!equals(QT_CURRENT_VERSION, $$QT_TARGET_VERSION) {
    message("Development using Qt version $$QT_CURRENT_VERSION but targeting $$QT_TARGET_VERSION")
    DEFINES += QT_VERSION_DIFFERENCE
    
        # Add compatibility defines for differences between 5.15.4 and 5.15.16
    # Only needed for QML/API changes between these versions
    DEFINES += COMPATIBILITY_5_15_16
    
    # Fix C++ standard for newer compilers
    CONFIG += c++17
}

# Support for cross-compilation to embedded target
# Define T507_BUILD to enable cross-compilation for Allwinner T507
# Example: qmake "T507_BUILD=1" "T507_TOOLCHAIN_PATH=/path/to/toolchain"
T507_BUILD {
    message("Configuring for Allwinner T507 target")
    include(t507_cross_compile.pri)
} else {
    message("Configuring for desktop build")
}
# Include src directory and any platform-specific headers
INCLUDEPATH += src/

# Enable C++11 support (required for Qt 5.15)
# Use C++11 by default but allow it to be overridden for compatibility
!contains(CONFIG, c++17) {
    CONFIG += c++11
}

# Add Qt compatibility code if needed
exists($$PWD/qt_compatibility.h) {
    HEADERS += $$PWD/qt_compatibility.h
}
SOURCES += \
        main.cpp \
        src/timehandler.cpp

RESOURCES += qml.qrc

# Translation setup
TRANSLATIONS += \
    light_qt5_en_US.ts

# For local development, we don't need to enforce translation updates
# This prevents errors when translation file is missing or incomplete
macx {
    # On Mac, make translations optional for local dev
    CONFIG += lrelease embed_translations
    # Add a target for creating translation files if needed
    translation.commands = lupdate $$PWD/light_qt5.pro
    QMAKE_EXTRA_TARGETS += translation
} else:linux {
    # On Linux, specify the lrelease path explicitly
    QMAKE_LRELEASE = /usr/bin/lrelease-qt5
    exists($$QMAKE_LRELEASE) {
        message("Using lrelease at $$QMAKE_LRELEASE")
    } else {
        QMAKE_LRELEASE = lrelease
        message("Using system lrelease")
    }
    CONFIG += lrelease embed_translations
} else {
    # On target, use standard translation handling
    CONFIG += lrelease embed_translations
}

# Performance optimizations for embedded target
T507_BUILD {
    CONFIG += optimize_full
    DEFINES += QT_NO_DEBUG_OUTPUT
    DEFINES += QT_MESSAGELOGCONTEXT
    DEFINES += QT_HAS_EGLFS
}

# Additional import path used to resolve QML modules in Qt Creator's code model
!linux: QML_IMPORT_PATH =

# Additional import path used to resolve QML modules just for Qt Quick Designer
!linux: QML_DESIGNER_IMPORT_PATH =

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: {
    # For embedded Linux target
    T507_BUILD {
        target.path = /opt/light
    } else {
        target.path = /opt/$${TARGET}/bin
    }
}
!isEmpty(target.path): INSTALLS += target

HEADERS += \
    src/timehandler.h

# Use C++17 for modern macOS builds
macx {
    # Let Xcode find the right SDK - don't hardcode it
    # The sdk_no_version_check config deals with SDK version issues
    CONFIG += c++17
}

QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_CC = /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_CXX = /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++

QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_FIX_RPATH = \
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/install_name_tool \
    -id

QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_AR = \
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar \
    cq

QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_RANLIB = \
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib \
    -s

QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_LINK = /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_LINK_SHLIB = /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++

QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_ACTOOL = /Applications/Xcode.app/Contents/Developer/usr/bin/actool
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_LINK_C = /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_LINK_C_SHLIB = /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang

QMAKE_CXX.QT_COMPILER_STDCXX = 199711L
QMAKE_CXX.QMAKE_APPLE_CC = 6000
QMAKE_CXX.QMAKE_APPLE_CLANG_MAJOR_VERSION = 15
QMAKE_CXX.QMAKE_APPLE_CLANG_MINOR_VERSION = 0
QMAKE_CXX.QMAKE_APPLE_CLANG_PATCH_VERSION = 0
QMAKE_CXX.QMAKE_GCC_MAJOR_VERSION = 4
QMAKE_CXX.QMAKE_GCC_MINOR_VERSION = 2
QMAKE_CXX.QMAKE_GCC_PATCH_VERSION = 1

QMAKE_CXX.COMPILER_MACROS = \
    QT_COMPILER_STDCXX \
    QMAKE_APPLE_CC \
    QMAKE_APPLE_CLANG_MAJOR_VERSION \
    QMAKE_APPLE_CLANG_MINOR_VERSION \
    QMAKE_APPLE_CLANG_PATCH_VERSION \
    QMAKE_GCC_MAJOR_VERSION \
    QMAKE_GCC_MINOR_VERSION \
    QMAKE_GCC_PATCH_VERSION
