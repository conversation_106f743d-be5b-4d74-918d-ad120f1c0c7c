*build*/*

# Created by https://www.gitignore.io/api/qt,qml,c++,qtcreator
# Edit at https://www.gitignore.io/?templates=qt,qml,c++,qtcreator

### C++ ###
# Prerequisites
*.d

# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.mod
*.smod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

### QML ###
# Cached binary representations of QML and JS files
*.qmlc
*.jsc

### Qt ###
# C++ objects and libs

# Qt-es
object_script.*.Release
object_script.*.Debug
*_plugin_import.cpp
*.qmake.cache
*.qmake.stash
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
*.moc
moc_*.cpp
moc_*.h
qrc_*.cpp
ui_*.h
Makefile*
*build-*

# Qt unit tests
target_wrapper.*

# QtCreator
*.autosave

# QtCreator Qml
*.qmlproject.user
*.qmlproject.user.*

# QtCreator CMake
CMakeLists.txt.user*

# QtCreator 4.8< compilation database
compile_commands.json

# QtCreator local machine specific files for imported projects
*creator.user*

### QtCreator ###
# gitignore for Qt Creator like IDE for pure C/C++ project without Qt
#
# Reference: http://doc.qt.io/qtcreator/creator-project-generic.html



# Qt Creator autogenerated files


# A listing of all the files included in the project
*.files

# Include directories
*.includes

# Project configuration settings like predefined Macros
*.config

# Qt Creator settings
*.creator

# User project settings
*.creator.user*

# Qt Creator backups

# End of https://www.gitignore.io/api/qt,qml,c++,qtcreator
