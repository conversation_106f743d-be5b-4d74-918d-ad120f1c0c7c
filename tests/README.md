# Light Application Test Suite

This directory contains tests for the Light printer management application to ensure correct functionality across development and embedded environments.

## Test Structure

The test suite is organized into separate modules:

```
tests/
├── qmltests/          # Pure QML/JavaScript tests for component logic
│   ├── tst_contentscaling.qml        # Tests for UI scaling behavior
│   ├── tst_platformdetection.qml     # Tests for platform detection logic
│   └── tst_windowsizing.qml          # Tests for window sizing and positioning
├── uitests/           # UI component tests with rendering
│   ├── qml/                          # Mock QML components for testing
│   ├── TestTopBand.qml               # Test for TopBand component
│   └── testhelper.h                  # C++ helper for UI testing
└── resources/         # Shared test resources
```

## Running Tests

### Prerequisites

- Qt 5.15.x with QtTest module
- qmake
- A development environment (Mac or Linux)

### Build and Run QML Tests

```bash
# From the tests/qmltests directory:
cd tests/qmltests
qmake
make

# On Linux/Generic platforms:
./tst_qml

# On macOS:
./tst_qml.app/Contents/MacOS/tst_qml
```

### Build and Run UI Tests

```bash
# From the tests/uitests directory:
cd tests/uitests
qmake
make

# On Linux/Generic platforms:
./tst_ui

# On macOS:
./tst_ui.app/Contents/MacOS/tst_ui
```

### Run All Tests

```bash
# From the project root directory:
./tests/run_tests.sh
```

The `run_tests.sh` script builds and executes all test modules sequentially, detecting platform-specific differences (like macOS app bundles). It works across different environments and provides a color-coded summary of test results.

## Adding New Tests

### QML Tests

1. Create a new QML file in the `qmltests` directory (e.g., `tst_newfeature.qml`)
2. Add the file to `qmltests.qrc`
3. Test using the QtTest QML module

Example:
```qml
import QtQuick 2.15
import QtTest 1.2

TestCase {
    name: "NewFeatureTest"
    
    function test_something() {
        compare(1 + 1, 2, "Basic math should work")
    }
}
```

### UI Tests

1. Create a test QML file in the `uitests` directory
2. Add it to `uitests.qrc`
3. Add test methods to `tst_uitests.cpp` or create a new test class

## Test Coverage

Current test coverage includes:

- **Content Scaling**: Tests for proper scaling calculations in development and embedded modes
- **Platform Detection**: Verifies correct platform detection for macOS and Linux
- **Window Sizing**: Tests window dimension calculations and positioning
- **TopBand Component**: Tests the TopBand UI component appearance and behavior using a mock implementation
- **Mock Application**: Tests application window sizing and scaling using a simulated app environment

## Implementation Details

### Mock Components

The UI tests use mock components instead of directly testing the actual UI components to avoid dependencies:

- **MockApplication.qml**: A simplified application window with proper scaling logic and mock UI elements
- **TestTopBand.qml**: A mock implementation of the TopBand with core UI elements for testing

This approach provides several benefits:
- Tests can run without requiring the entire application ecosystem
- Tests are isolated from changes in the actual components
- Mock components can be designed specifically for testability

### Test Helper

The `testhelper.h` provides C++ utility functions for UI testing:
- Finding QML items by objectName
- Taking screenshots of QML components
- Manipulating test properties

## Future Test Improvements

- Add reference image comparison for UI components
- Add touch input simulation for embedded mode
- Test navigation between different application views
- Test printer-specific functionality when implemented
- Add automated integration testing for the full application workflow
- Add CI/CD integration for running tests automatically on commits
