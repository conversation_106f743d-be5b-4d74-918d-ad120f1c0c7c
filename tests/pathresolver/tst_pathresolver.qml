/**
 * tst_pathresolver.qml
 *
 * Unit tests for the PathResolver singleton using the Qt Test framework.
 */
import QtQuick 2.15
import QtTest 1.15
import UI.Core 1.0

TestCase {
    id: testCase

    // Test isEmbedded property detection
    function test_01_platformDetection() {
        // We can't directly test the platform detection since it depends on
        // runtime environment, but we can verify that the property exists and is boolean
        verify(typeof PathResolver.isEmbedded === "boolean", "isEmbedded should be a boolean property");
        // Log the detected platform for manual verification
        console.log("Detected platform: " + (PathResolver.isEmbedded ? "Embedded" : "Desktop"));
    }

    // Test the current prefix
    function test_02_currentPrefix() {
        // Check that currentPrefix is a string and not empty
        verify(typeof PathResolver.currentPrefix === "string", "currentPrefix should be a string");
        verify(PathResolver.currentPrefix.length > 0, "currentPrefix should not be empty");
        // Check that it matches one of the expected prefixes
        var validPrefix = PathResolver.currentPrefix === PathResolver.desktopPrefix || PathResolver.currentPrefix === PathResolver.embeddedPrefix;
        verify(validPrefix, "currentPrefix should match either desktopPrefix or embeddedPrefix");
        console.log("Current prefix: " + PathResolver.currentPrefix);
    }

    // Test basic path resolution
    function test_03_basicResolution() {
        // Test plain path resolution
        var testPath = "UI/Assets/icons/home-icon.svg";
        var resolvedPath = PathResolver.resolvePath(testPath);
        verify(typeof resolvedPath === "string", "Resolved path should be a string");
        verify(resolvedPath.length > 0, "Resolved path should not be empty");
        // Verify path starts with the correct prefix
        verify(resolvedPath.startsWith(PathResolver.currentPrefix), "Resolved path should start with the current prefix");
        console.log("Test path: " + testPath);
        console.log("Resolved path: " + resolvedPath);
    }

    // Test specialized asset resolution
    function test_04_assetResolution() {
        var testAsset = "icons/home-icon.svg";
        var resolvedAsset = PathResolver.resolveAsset(testAsset);
        verify(resolvedAsset.includes("UI/Assets/"), "Resolved asset path should include the Assets directory");
        console.log("Test asset: " + testAsset);
        console.log("Resolved asset path: " + resolvedAsset);
    }

    // Test specialized component resolution
    function test_05_componentResolution() {
        var testComponent = "ProsprButton.qml";
        var resolvedComponent = PathResolver.resolveComponent(testComponent);
        verify(resolvedComponent.includes("UI/Components/"), "Resolved component path should include the Components directory");
        console.log("Test component: " + testComponent);
        console.log("Resolved component path: " + resolvedComponent);
    }

    // Test specialized screen resolution
    function test_06_screenResolution() {
        var testScreen = "Home.qml";
        var resolvedScreen = PathResolver.resolveScreen(testScreen);
        verify(resolvedScreen.includes("UI/Screens/"), "Resolved screen path should include the Screens directory");
        console.log("Test screen: " + testScreen);
        console.log("Resolved screen path: " + resolvedScreen);
    }

    // Test path normalization
    function test_07_pathNormalization() {
        // Test with different path formats
        var paths = ["qrc:/UI/Assets/icons/home-icon.svg", "qrc:/UI/Assets/icons/home-icon.svg", "./UI/Assets/icons/home-icon.svg", "/root/UI/Assets/icons/home-icon.svg", "UI/Assets/icons/home-icon.svg"];
        var expectedPath = PathResolver.currentPrefix + "UI/Assets/icons/home-icon.svg";
        for (var i = 0; i < paths.length; i++) {
            var resolvedPath = PathResolver.resolvePath(paths[i]);
            console.log("Original: " + paths[i] + " -> Resolved: " + resolvedPath);
            // The resolved path should either match the expected path
            // or preserve the original if it's already correctly prefixed
            var correctlyResolved = resolvedPath === expectedPath || (paths[i].startsWith(PathResolver.currentPrefix) && resolvedPath === paths[i]);
            verify(correctlyResolved, "Path '" + paths[i] + "' should be correctly normalized");
        }
    }

    // Test caching
    function test_08_caching() {
        // Enable debug for cache testing
        PathResolver.debugMode = true;
        // Clear cache before test
        PathResolver.clearCache();
        // First resolution should not be cached
        var testPath = "UI/Assets/icons/home-icon.svg";
        var firstResult = PathResolver.resolvePath(testPath);
        // Second resolution should use cache
        var secondResult = PathResolver.resolvePath(testPath);
        // Results should be identical
        compare(firstResult, secondResult, "Cached result should match the first result");
        // Clear cache and check again
        PathResolver.clearCache();
        var thirdResult = PathResolver.resolvePath(testPath);
        // Results should still be identical after cache clear
        compare(firstResult, thirdResult, "Result after cache clear should match the first result");
    }

    // Test error handling
    function test_09_errorHandling() {
        // Test with null/undefined
        var nullResult = PathResolver.resolvePath(null);
        var undefinedResult = PathResolver.resolvePath(undefined);
        verify(nullResult === "", "Null path should be handled gracefully");
        verify(undefinedResult === "", "Undefined path should be handled gracefully");
        // Test with empty string
        var emptyResult = PathResolver.resolvePath("");
        verify(emptyResult === PathResolver.currentPrefix, "Empty path should resolve to current prefix");
    }

    name: "PathResolverTests"
    // Run tests after window is shown
    when: windowShown
}
