import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Window 2.15

// Test for the TopBand component
Item {
    // The C++ tests will inspect this component directly
    // We don't need to have test cases in QML for the UI tests
    // since we're doing the testing in the C++ side

    id: root

    width: 1440
    height: 810
    visible: true

    // Create a mock TopBand for testing
    Rectangle {
        id: mockTopBand

        property string currentView: "homePage"

        objectName: "topBand"
        width: parent.width
        height: 60
        color: Theme.Colors.primary

        Text {
            id: applicationTitle

            objectName: "applicationTitle"
            anchors.centerIn: parent
            text: "Light Printer Management"
            color: Theme.Colors.white
            font.pixelSize: 18
        }

        // Mock UI elements that would be in the real TopBand
        Row {
            anchors.right: parent.right
            anchors.rightMargin: 20
            anchors.verticalCenter: parent.verticalCenter
            spacing: 15

            Rectangle {
                objectName: "settingsButton"
                width: 30
                height: 30
                color: Theme.Colors.transparent
                border.color: Theme.Colors.white
                border.width: 1
                radius: 15

                Text {
                    anchors.centerIn: parent
                    text: "⚙"
                    color: Theme.Colors.white
                }

            }

            Rectangle {
                objectName: "userButton"
                width: 30
                height: 30
                color: Theme.Colors.transparent
                border.color: Theme.Colors.white
                border.width: 1
                radius: 15

                Text {
                    anchors.centerIn: parent
                    text: "👤"
                    color: Theme.Colors.white
                }

            }

        }

    }

}
