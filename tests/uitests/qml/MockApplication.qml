import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Window 2.15

// A simplified version of the main app window for testing
Window {
    id: testWindow

    // Testing properties that mimic the real app
    property bool isDevelopment: true
    property bool isEmbedded: false
    property real contentScale: 0.85
    property real scaleFactor: 1

    // Match properties from the real application
    visible: true
    width: 1440
    height: 810

    // Content rectangle that will be scaled
    Rectangle {
        id: contentRect

        width: parent.width
        height: parent.height
        color: Theme.Colors.transparent

        // Simulate the TopBand component
        Rectangle {
            id: mockTopBand

            objectName: "topBand"
            width: parent.width
            height: 60
            color: Theme.Colors.primary

            Text {
                objectName: "applicationTitle"
                anchors.centerIn: parent
                text: "Light Test"
                color: Theme.Colors.white
                font.pixelSize: 18
            }

        }

        // Simulate content area
        Rectangle {
            anchors.top: mockTopBand.bottom
            width: parent.width
            height: parent.height - mockTopBand.height
            color: Theme.Colors.white

            Text {
                anchors.centerIn: parent
                text: "Mock Content Area"
                font.pixelSize: 24
                color: Theme.Colors.textPrimary
            }

        }

        // Apply scaling matching the real app
        transform: Scale {
            xScale: contentScale
            yScale: contentScale
            origin.x: 0
            origin.y: 0
        }

    }

}
