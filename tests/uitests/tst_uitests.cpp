#include <QtTest/QtTest>
#include <QQuickItem>
#include <QQuickWindow>
#include <QQmlEngine>
#include <QQmlComponent>
#include <QQuickView>
#include "testhelper.h"

class UITests : public QObject
{
    Q_OBJECT

private:
    QQmlEngine engine;
    TestHelper helper;

private slots:
    // Initialize before each test
    void initTestCase() {
        // Add the application QML files to the import path
        engine.addImportPath(":/");
        engine.addImportPath("../../");
    }

    // Clean up after each test
    void cleanupTestCase() {
        // Clean up any created components
    }

    // Test the TopBand component
    void testTopBand() {
        // Create a QML component for the TopBand test
        QQmlComponent component(&engine, QUrl("qrc:/TestTopBand.qml"));
        
        // Check for errors
        if (component.isError()) {
            for (const QQmlError &error : component.errors())
                qDebug() << error.url() << error.line() << error;
            QFAIL("Failed to create TopBand test component");
        }
        
        // Create an instance of the test component
        QScopedPointer<QQuickItem> item(qobject_cast<QQuickItem*>(component.create()));
        QVERIFY2(item, "Failed to create item from component");
        
        // Verify the TopBand basic properties
        QQuickItem *topBand = item->findChild<QQuickItem*>("topBand");
        QVERIFY2(topBand, "TopBand component not found");
        
        // Verify the width is set correctly
        QVERIFY2(topBand->width() > 0, "TopBand should have a positive width");
        QCOMPARE(topBand->width(), item->width());
        QCOMPARE(topBand->height(), 60.0);
        
        // Verify the application title is displayed
        QQuickItem *titleText = topBand->findChild<QQuickItem*>("applicationTitle");
        QVERIFY2(titleText, "Application title not found in TopBand");
        
        // Verify UI elements for settings and user buttons
        QQuickItem *settingsButton = topBand->findChild<QQuickItem*>("settingsButton");
        QVERIFY2(settingsButton, "Settings button not found in TopBand");
        
        QQuickItem *userButton = topBand->findChild<QQuickItem*>("userButton");
        QVERIFY2(userButton, "User button not found in TopBand");
    }
    
    // Test window sizing with actual rendering
    void testWindowSizing() {
        // Create a window with the mock application
        QQmlComponent component(&engine, QUrl("qrc:/qml/MockApplication.qml"));
        
        if (component.isError()) {
            for (const QQmlError &error : component.errors())
                qDebug() << error.url() << error.line() << error;
            QFAIL("Failed to create mock application component");
        }
        
        // Create the window object
        QScopedPointer<QObject> item(component.create());
        QQuickWindow *window = qobject_cast<QQuickWindow*>(item.data());
        QVERIFY2(window, "Failed to create window from component");
        
        // Test window size in development mode
        QVariant isDevelopment = window->property("isDevelopment");
        QVERIFY2(isDevelopment.isValid() && isDevelopment.toBool(), "Window should be in development mode");
        
        // Check if window dimensions match expectations
        QCOMPARE(window->width(), 1440);
        QCOMPARE(window->height(), 810);
        
        // Verify content scaling property exists
        QVariant contentScale = window->property("contentScale");
        QVERIFY2(contentScale.isValid(), "Window should have contentScale property");
        QVERIFY2(contentScale.toReal() > 0, "contentScale should be greater than 0");
        
        // Find the mockTopBand to test it's there
        QQuickItem *topBand = window->findChild<QQuickItem*>("topBand");
        QVERIFY2(topBand, "TopBand component not found in MockApplication");
        
        // We don't actually show the window to avoid UI popping up during tests
        // but in a real test, you might want to verify rendered appearance
    }
};

// This is needed for Qt test framework with QObject classes
QTEST_MAIN(UITests)
#include "tst_uitests.moc"
