#ifndef TESTHELPER_H
#define TESTHELPER_H

#include <QObject>
#include <QQuickItem>
#include <QQuickWindow>
#include <QImage>
#include <QMouseEvent>
#include <QCoreApplication>

// Helper class for UI testing functionality
class TestHelper : public QObject
{
    Q_OBJECT
    
public:
    TestHelper(QObject *parent = nullptr) : QObject(parent) {}
    
    // Helper to find a child item by objectName
    Q_INVOKABLE QQuickItem *findItemByName(QQuickItem *parent, const QString &name) {
        if (!parent)
            return nullptr;
            
        if (parent->objectName() == name)
            return parent;
            
        // Recursively search through children
        QList<QQuickItem*> children = parent->childItems();
        for (QQuickItem *child : children) {
            QQuickItem *item = findItemByName(child, name);
            if (item)
                return item;
        }
        
        return nullptr;
    }
    
    // Helper to take screenshot of an item
    Q_INVOKABLE QImage grabImage(QQuickItem *item) {
        if (!item || !item->window())
            return QImage();
            
        QQuickWindow *window = item->window();
        return window->grabWindow();
    }
    
    // Helper to simulate a mouse click
    Q_INVOKABLE void simulateClick(QQuickItem *item) {
        if (!item || !item->window())
            return;
            
        // Find center point of the item
        QPointF center = QPointF(item->width() / 2, item->height() / 2);
        
        // Convert to window coordinates
        QPointF scenePos = item->mapToScene(center);
        
        // Simulate mouse press and release
        QMouseEvent pressEvent(QEvent::MouseButtonPress, scenePos, Qt::LeftButton, Qt::LeftButton, Qt::NoModifier);
        QCoreApplication::sendEvent(item->window(), &pressEvent);
        
        QMouseEvent releaseEvent(QEvent::MouseButtonRelease, scenePos, Qt::LeftButton, Qt::NoButton, Qt::NoModifier);
        QCoreApplication::sendEvent(item->window(), &releaseEvent);
    }
};

#endif // TESTHELPER_H
