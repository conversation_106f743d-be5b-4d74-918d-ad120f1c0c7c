import QtQuick 2.15
import QtTest 1.15
import Backend.Error 1.0
import Backend.PrinterManager 1.0

TestCase {
    id: testCase
    name: "ErrorLogTests"

    function test_errorManagerExists() {
        verify(ErrorManager !== undefined, "ErrorManager should be available");
    }

    function test_printerManagerExists() {
        verify(PrinterManager !== undefined, "PrinterManager should be available");
    }

    function test_errorManagerMethods() {
        verify(typeof ErrorManager.reloadErrorsFromFile === "function", "reloadErrorsFromFile should be a function");
        verify(typeof ErrorManager.clearErrors === "function", "clearErrors should be a function");
        verify(typeof ErrorManager.saveErrorsToFile === "function", "saveErrorsToFile should be a function");
    }

    function test_printerManagerMethods() {
        verify(typeof PrinterManager.setStatusIndicators === "function", "setStatusIndicators should be a function");
        verify(typeof PrinterManager.setStatusIndicator === "function", "setStatusIndicator should be a function");
    }

    function test_errorManagerSignals() {
        var signalSpy = createTemporaryQObject("import QtTest 1.15; SignalSpy {}", testCase);
        signalSpy.target = ErrorManager;
        signalSpy.signalName = "errorsChanged";
        
        // Test that reloading errors triggers the signal
        ErrorManager.reloadErrorsFromFile();
        signalSpy.wait(1000);
        verify(signalSpy.count > 0, "errorsChanged signal should be emitted");
    }

    function test_statusIndicators() {
        var signalSpy = createTemporaryQObject("import QtTest 1.15; SignalSpy {}", testCase);
        signalSpy.target = PrinterManager;
        signalSpy.signalName = "statusIndicatorsChanged";
        
        // Test setting status indicators
        PrinterManager.setStatusIndicators(true, false, false);
        signalSpy.wait(1000);
        verify(signalSpy.count > 0, "statusIndicatorsChanged signal should be emitted");
        
        var indicators = PrinterManager.statusIndicators;
        verify(indicators.length === 3, "Should have 3 status indicators");
        verify(indicators[0] === true, "First indicator should be true");
        verify(indicators[1] === false, "Second indicator should be false");
        verify(indicators[2] === false, "Third indicator should be false");
    }
}
