import QtQuick 2.15
import QtTest 1.2

TestCase {
    id: testCase

    // Mock window and screen properties
    property var mockWindow
    property var mockScreen

    function init() {
        // Reset to default values
        mockWindow.width = 1440;
        mockWindow.height = 810;
        mockWindow.x = 0;
        mockWindow.y = 0;
        mockWindow.isDevelopment = true;
        mockScreen.width = 1920;
        mockScreen.height = 1080;
    }

    // Test window sizing in development mode
    function test_window_sizing_development() {
        // Verify initial dimensions match our expectations
        compare(mockWindow.width, 1440, "Initial width should be 1440px in development mode");
        compare(mockWindow.height, 810, "Initial height should be 810px in development mode");
    }

    // Test window centering logic
    function test_window_centering() {
        // Calculate expected position to center the window
        var expectedX = (mockScreen.width - mockWindow.width) / 2;
        var expectedY = (mockScreen.height - mockWindow.height) / 2;
        // Apply centering logic from main.qml
        mockWindow.x = (mockScreen.width - mockWindow.width) / 2;
        mockWindow.y = (mockScreen.height - mockWindow.height) / 2;
        compare(mockWindow.x, expectedX, "Window should be horizontally centered");
        compare(mockWindow.y, expectedY, "Window should be vertically centered");
    }

    // Test minimum size constraints
    function test_minimum_size_constraints() {
        // Ensure minimum size matches our expectations
        compare(mockWindow.minimumWidth, 1280, "Minimum width should be set correctly");
        compare(mockWindow.minimumHeight, 720, "Minimum height should be set correctly");
        // Simulate trying to resize below minimum (this would be prevented by Qt)
        // but we can verify our application's intended minimum
        verify(mockWindow.minimumWidth <= mockWindow.width, "Window width should not be less than minimum width");
        verify(mockWindow.minimumHeight <= mockWindow.height, "Window height should not be less than minimum height");
    }

    name: "WindowSizingTests"

    mockWindow: QtObject {
        property bool isDevelopment: true
        property int width: 1440
        property int height: 810
        property int x: 0
        property int y: 0
        property int minimumWidth: 1280
        property int minimumHeight: 720
    }

    mockScreen: QtObject {
        property int width: 1920
        property int height: 1080
    }

}
