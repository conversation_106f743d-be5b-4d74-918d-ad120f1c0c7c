import QtQuick 2.15
import QtTest 1.2

TestCase {
    id: testCase

    // Mock environment variables and application arguments
    property var mockEnv

    // Simulate the platform detection logic from main.qml
    function isEmbedded(platform, args, env) {
        if (platform === "linux")
            return args.indexOf("--embedded") !== -1 || env.QT_QPA_PLATFORM === "eglfs";

        return false;
    }

    function init() {
        // Reset to default values
        mockEnv.platform = "unknown";
        mockEnv.arguments = [];
        mockEnv.env = ({
        });
    }

    // Test Linux platform with --embedded flag
    function test_embedded_mode_flag() {
        mockEnv.platform = "linux";
        mockEnv.arguments = ["--embedded"];
        verify(isEmbedded(mockEnv.platform, mockEnv.arguments, mockEnv.env), "Should detect embedded mode with --embedded flag");
    }

    // Test Linux platform with EGLFS platform
    function test_embedded_mode_eglfs() {
        mockEnv.platform = "linux";
        mockEnv.env = {
            "QT_QPA_PLATFORM": "eglfs"
        };
        verify(isEmbedded(mockEnv.platform, mockEnv.arguments, mockEnv.env), "Should detect embedded mode with QT_QPA_PLATFORM=eglfs");
    }

    // Test macOS platform (always development mode)
    function test_development_mode_macos() {
        mockEnv.platform = "osx";
        mockEnv.arguments = ["--embedded"]; // Even with this flag
        verify(!isEmbedded(mockEnv.platform, mockEnv.arguments, mockEnv.env), "Should not detect embedded mode on macOS regardless of flags");
    }

    // Test Linux without embedded indicators
    function test_development_mode_linux() {
        mockEnv.platform = "linux";
        mockEnv.env = {
            "QT_QPA_PLATFORM": "xcb"
        }; // X11 platform
        verify(!isEmbedded(mockEnv.platform, mockEnv.arguments, mockEnv.env), "Should not detect embedded mode on Linux without flags or eglfs");
    }

    name: "PlatformDetectionTests"

    mockEnv: QtObject {
        property var platform: "unknown"
        property var arguments: []
        property var env: ({
        })
    }

}
