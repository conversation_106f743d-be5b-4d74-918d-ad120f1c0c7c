TEMPLATE = app
TARGET = tst_qml
CONFIG += qmltestcase warn_on qml_debug

# Include Qt modules for testing QML
QT += quick testlib

# Sources
SOURCES += \
    tst_main.cpp

# QML test files
DISTFILES += \
    tst_contentscaling.qml \
    tst_platformdetection.qml \
    tst_windowsizing.qml

# Include your app's QML files (add path relative to the test project)
QML_IMPORT_PATH = $$PWD/../../

# Test resources
RESOURCES += \
    qmltests.qrc
