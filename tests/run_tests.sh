#!/bin/bash
# Script to build and run all tests for the Light application

# Exit on error
set -e

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Building and running Light application tests...${NC}"

# Build and run QML tests
echo -e "\n${YELLOW}Building QML tests...${NC}"
cd "$SCRIPT_DIR/qmltests"
qmake
make clean
make

echo -e "\n${YELLOW}Running QML tests...${NC}"
# Check if we're on macOS (app bundle) or Linux/other
if [ -e "./tst_qml.app/Contents/MacOS/tst_qml" ]; then
    ./tst_qml.app/Contents/MacOS/tst_qml
else
    ./tst_qml
fi
QML_TEST_RESULT=$?

# Build and run UI tests
echo -e "\n${YELLOW}Building UI tests...${NC}"
cd "$SCRIPT_DIR/uitests"
qmake
make clean
make

echo -e "\n${YELLOW}Running UI tests...${NC}"
# Check if we're on macOS (app bundle) or Linux/other
if [ -e "./tst_ui.app/Contents/MacOS/tst_ui" ]; then
    ./tst_ui.app/Contents/MacOS/tst_ui
else
    ./tst_ui
fi
UI_TEST_RESULT=$?

# Display summary
echo -e "\n${YELLOW}Test Summary:${NC}"
if [ $QML_TEST_RESULT -eq 0 ]; then
    echo -e "QML Tests: ${GREEN}PASSED${NC}"
else
    echo -e "QML Tests: ${RED}FAILED${NC}"
fi

if [ $UI_TEST_RESULT -eq 0 ]; then
    echo -e "UI Tests: ${GREEN}PASSED${NC}"
else
    echo -e "UI Tests: ${RED}FAILED${NC}"
fi

# Overall result
if [ $QML_TEST_RESULT -eq 0 ] && [ $UI_TEST_RESULT -eq 0 ]; then
    echo -e "\n${GREEN}All tests passed successfully!${NC}"
    exit 0
else
    echo -e "\n${RED}Some tests failed. Please check the output above for details.${NC}"
    exit 1
fi
