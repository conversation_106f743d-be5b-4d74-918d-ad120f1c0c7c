#pragma once
#include <printer/PrinterManager.h>
#include <printer/ServiceFunction.h>
#include <printer/ServiceStatus.h>
#include <QTimer>

class PrinterManagerTest : public PrinterManager {
    Q_OBJECT

public:
    explicit PrinterManagerTest(ConfigManager* configManager = nullptr, QObject *parent = nullptr);
    ~PrinterManagerTest() override = default;

    void startProgressSimulation();
    void stopProgressSimulation();

private slots:
    void updateStatusSimulation();

private:
    void setupFunction();
    void setupStatuses();

private:
    QHash<ServiceFunction*, QTimer*> m_functionTimers;
    QTimer* m_statusSimulationTimer;
};
