#include <QTimer>
#include <QDebug>
#include <QHash>
#include <cmath>
#include <cstdlib>
#include "PrinterManagerTest.h"

PrinterManagerTest::PrinterManagerTest(ConfigManager* configManager, QObject *parent)
    : PrinterManager(config<PERSON><PERSON>ger, parent)
{
    setupFunction();
    setupStatuses();
}

void PrinterManagerTest::startProgressSimulation()
{
    ServiceFunction* function = qobject_cast<ServiceFunction*>(sender());
    if (!function) return;
    function->setProgress(0);

    QTimer* timer = m_functionTimers[function];
    if (!timer) { return; }

    timer->stop();
    timer->disconnect();

    QObject::connect(timer, &QTimer::timeout, function, [function]() {
        function->setProgress(function->progress() + 1);
    });
    timer->start(100);

    // Connect stop signal to stopFunctionTimer
    connect(function, &ServiceFunction::functionStopped, this, &PrinterManagerTest::stopProgressSimulation);
}

void PrinterManagerTest::stopProgressSimulation()
{
    ServiceFunction* function = qobject_cast<ServiceFunction*>(sender());
    if (!function) return;

    QTimer* timer = m_functionTimers.value(function, nullptr);
    if (timer && timer->isActive()) {
        timer->stop();
        timer->disconnect();
    }
}

void PrinterManagerTest::updateStatusSimulation()
{
    // Simulate more intensive status value changes for frequent threshold crossings
    static int simulationStep = 0;
    simulationStep++;

    // Update pressure with larger variation and base closer to threshold
    ServiceStatus* pressure = getStatus("pressure");
    if (pressure) {
        double baseValue = 3200; // Near warning threshold
        double variation = (rand() % 400) - 200; // -200 to +199
        pressure->setValue(baseValue + variation);
    }

    // Update fill level (decrease more rapidly and with random jumps)
    ServiceStatus* fillLevel = getStatus("fillLevel");
    if (fillLevel) {
        int currentLevel = fillLevel->value().toInt();
        if (simulationStep % 2 == 0 && currentLevel > 0) { // Decrease every 4 seconds
            int delta = rand() % 8 + 3; // Decrease by 3-10
            fillLevel->setValue(qMax(0, currentLevel - delta));
        } else if (simulationStep % 15 == 0) { // Occasionally refill
            fillLevel->setValue(95); // Refill to above critical
        }
    }

    // Update pump speed with larger variations
    ServiceStatus* pumpSpeed = getStatus("pumpSpeed");
    if (pumpSpeed) {
        double baseSpeed = 180.0; // Near upper range
        double variation = sin(simulationStep * 0.2) * 20 + (rand() % 80 - 40) * 0.2; // More intense
        pumpSpeed->setValue(baseSpeed + variation);
    }

    // Update viscosity with larger fluctuation and base closer to threshold
    ServiceStatus* viscosity = getStatus("viscosity");
    if (viscosity) {
        int baseViscosity = 900; // Near warning/critical
        int variation = sin(simulationStep * 0.15) * 200 + (rand() % 300 - 150);
        int newValue = baseViscosity + variation;
        viscosity->setValue(newValue);
    }

    // Update filter time (increase more rapidly and with random jumps)
    ServiceStatus* filterTime = getStatus("filterTime");
    if (filterTime) {
        int currentTime = filterTime->value().toInt();
        if (simulationStep % 2 == 0) { // Increase every 4 seconds
            int delta = rand() % 8 + 3; // Increase by 3-10
            filterTime->setValue(qMin(5000, currentTime + delta));
        } else if (simulationStep % 15 == 0) { // Occasionally reset
            filterTime->setValue(3900); // Reset to below warning
        }
    }

    // Update temperature with larger variation and base closer to threshold
    ServiceStatus* temperature = getStatus("temperature");
    if (temperature) {
        int baseTemp = 70; // Near warning threshold
        int variation = sin(simulationStep * 0.08) * 15 + (rand() % 12 - 6);
        temperature->setValue(baseTemp + variation);
    }
}

void PrinterManagerTest::setupFunction()
{
    ServiceFunction* jetFunction = new ServiceFunction("jet", "Jet");
    ServiceFunction* addSolventFunction = new ServiceFunction("addSolvent", "Add Solvent");
    ServiceFunction* addInkFunction = new ServiceFunction("addInk", "Add Ink");
    ServiceFunction* cleanNozzleFunction = new ServiceFunction("cleanNozzle", "Clean Nozzle");

    registerFunction(jetFunction);
    registerFunction(addSolventFunction);
    registerFunction(addInkFunction);
    registerFunction(cleanNozzleFunction);

    cleanNozzleFunction->addMutuallyExclusiveFunction("jet");
    jetFunction->addMutuallyExclusiveFunction("cleanNozzle");

    m_functionTimers[addSolventFunction] = new QTimer(this);
    m_functionTimers[addInkFunction] = new QTimer(this);

    connect(addSolventFunction, &ServiceFunction::functionStarted, this, &PrinterManagerTest::startProgressSimulation);
    connect(addSolventFunction, &ServiceFunction::functionCompleted, this, &PrinterManagerTest::stopProgressSimulation);
    connect(addSolventFunction, &ServiceFunction::functionStopped, this, &PrinterManagerTest::stopProgressSimulation);
    connect(addInkFunction, &ServiceFunction::functionStarted, this, &PrinterManagerTest::startProgressSimulation);
    connect(addInkFunction, &ServiceFunction::functionCompleted, this, &PrinterManagerTest::stopProgressSimulation);
    connect(addInkFunction, &ServiceFunction::functionStopped, this, &PrinterManagerTest::stopProgressSimulation);

    connect(jetFunction, &ServiceFunction::functionStarted, this, [this](){ setStatusIndicators(true, false, false); });
    connect(jetFunction, &ServiceFunction::functionStopped, this, [this](){ setStatusIndicators(false, false, true); });

    jetFunction->start();
}

void PrinterManagerTest::setupStatuses()
{
    ServiceStatus* pressureStatus = new ServiceStatus("pressure", "Pressure (Bars)", this);
    pressureStatus->setUnits("Bars");
    pressureStatus->setRange(0, 3500);
    pressureStatus->setWarningThreshold(3200);
    pressureStatus->setCriticalThreshold(3300);
    pressureStatus->setValue(3000);
    registerStatus(pressureStatus);

    ServiceStatus* fillLevelStatus = new ServiceStatus("fillLevel", "Fill Level", this);
    fillLevelStatus->setUnits("%");
    fillLevelStatus->setRange(0, 100);
    fillLevelStatus->setWarningThreshold(80);
    fillLevelStatus->setCriticalThreshold(90);
    fillLevelStatus->setValue(88); // Start near critical
    registerStatus(fillLevelStatus);

    ServiceStatus* pumpSpeedStatus = new ServiceStatus("pumpSpeed", "Pump Speed (RPM)", this);
    pumpSpeedStatus->setUnits("RPM");
    pumpSpeedStatus->setRange(0, 200);
    pumpSpeedStatus->setPrecision(1);
    pumpSpeedStatus->setValue(100.3);
    registerStatus(pumpSpeedStatus);

    ServiceStatus* viscosityStatus = new ServiceStatus("viscosity", "Viscosity (+/-)", this);
    viscosityStatus->setUnits("");
    viscosityStatus->setRange(-1000, 1000);
    viscosityStatus->setWarningThreshold(800);
    viscosityStatus->setCriticalThreshold(950);
    viscosityStatus->setPrecision(0);
    viscosityStatus->setValue(500);
    registerStatus(viscosityStatus);

    ServiceStatus* filterTimeStatus = new ServiceStatus("filterTime", "Filter Time", this);
    filterTimeStatus->setUnits("hours");
    filterTimeStatus->setRange(0, 5000);
    filterTimeStatus->setWarningThreshold(4000);
    filterTimeStatus->setCriticalThreshold(4800);
    filterTimeStatus->setValue(4700); // Start near critical
    registerStatus(filterTimeStatus);

    ServiceStatus* temperatureStatus = new ServiceStatus("temperature", "Board Temperature (°C)", this);
    temperatureStatus->setUnits("°C");
    temperatureStatus->setRange(-10, 100);
    temperatureStatus->setWarningThreshold(70);
    temperatureStatus->setCriticalThreshold(85);
    temperatureStatus->setValue(38);
    registerStatus(temperatureStatus);

    // Setup status simulation timer
    m_statusSimulationTimer = new QTimer(this);
    connect(m_statusSimulationTimer, &QTimer::timeout, this, &PrinterManagerTest::updateStatusSimulation);
    m_statusSimulationTimer->start(2000); // Update every 2 seconds
}
