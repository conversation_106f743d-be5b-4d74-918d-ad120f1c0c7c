# Light Project Rules for AI Assistants

## Project Overview
Light is a Qt5/QML-based application for printer management and label printing, designed to run on both development environments (macOS/Linux) and embedded targets (Allwinner T507). The application is specifically designed for deployment on touchscreen tablet-like devices in industrial environments.

## AI Assistant Guidelines

reference 
 - context/PLANNING.md
 - README.md
 - context/qt_qml_doc.md

### General Behavior
- Always prioritize code quality and maintainability over quick fixes.
- Remember that this project is developed for both standard desktop environments and industrial touchscreen devices.
- When suggesting UI changes, consider the industrial environment context with larger touch targets.
- Use the updated color palette (April 2025) for all UI elements to maintain visual consistency.
- Respect the established design patterns and architecture throughout the codebase.
- Assume the developer is experienced with Qt/QML but may need assistance with specific implementation details.

### Code Style & Standards
- Follow Qt/QML best practices and naming conventions.
- Use camelCase for variable and function names in QML and C++.
- Maintain consistent indentation (4 spaces) in QML files.
- QML component properties should be organized in a consistent order: id, width/height, anchors, colors, then custom properties.
- Always use Theme.Colors singleton for color values (e.g., `Theme.Colors.primary` instead of hex codes directly).
- Never use hard-coded color values in UI components - reference the centralized colors in Colors.qml.
- Always use Theme.Typography singleton for text styling (e.g., `font: Theme.Typography.heading` instead of setting font properties directly).
- Never hard-code font families, sizes, or weights - use the centralized Typography.qml properties.
- For C++ code, follow the Qt coding style guide.
- Use descriptive variable names that clearly convey purpose.

### UI Development
- All UI elements should be designed with touch input in mind, with minimum touch targets of 50x50 pixels.
- The application must support virtual keyboard input for all text fields, as physical keyboards will not be available in production environments.
- Respect the company's color palette from the design and always use hex code for setting color variables:
  - GREEN: #00AE0F (RGB 0, 174, 15)
  - YELLOW: #FFB818 (RGB 255, 184, 24)
  - RED: #CF0F25 (RGB 207, 15, 37)
  - PRIMARY: #F15B26 (RGB 241, 91, 38)
  - SECONDARY: #002A40 (RGB 0, 42, 64)
  - BLACK: #1C1C1C (RGB 28, 28, 28)
  - WHITE: #FFFFFF (pure white)
  - LIGHT GREY: #CBCBC8 (RGB 203, 203, 200)
  - MID GREY: #888888 (RGB 136, 136, 136)
  - DARK GREY: #282828 (RGB 40, 40, 40)
- Use the company's typography system with these brand fonts:
  - PRIMARY FONT: Young Serif (Regular)
    - Used for headings, highlights, and emphasis
  - SECONDARY FONT: Clash Grotesk (Regular, Medium, Semibold, Bold)
    - Used for body text, buttons, labels, and most UI elements
- QML components should use centralized navigation through MainLayout.qml.
- UI should be responsive with proper scaling for different screen sizes.
- Text sizes should be appropriately large for industrial environments (16px minimum, 18-22px preferred for regular text).
- Buttons should have visual feedback for pressed states.
- Maintain visual consistency with shadow effects and rounded corners across the application.
- Text input fields should automatically trigger the virtual keyboard and position content to remain visible when the keyboard appears.

### Architecture
- Navigation between screens should use the MainLayout.navigateToRequested signal.
- Reusable components should be placed in the UI_new/Components directory.
- Assets should be placed in the UI_new/Assets directory.
- New screens should be placed in the UI_new/Screens directory.
- **IMPORTANT: ALL new QML files must be registered in qml.qrc to be accessible at runtime. Failure to do this will result in the component not being found when deployed.**
  - Any time a new .qml file is created, it MUST be added to qml.qrc in the appropriate section
  - For UI screens, add under UI_new/Screens section
  - For components, add under UI_new/Components section
  - For theme files, add under UI_new/Theme section
- Bottom navigation is centralized through the BottomBar component in MainLayout.qml.

### Cross-Platform Development
- The application must support:
  1. macOS development environment
  2. Linux development environment
  3. Allwinner T507 embedded target
- In Linux environments, be aware of specific QML module paths and dependencies.
- Use conditional compilation for platform-specific code.
- Consider the scaling factors needed for high-DPI displays vs. embedded targets.

### Build System
- Use the universal build script while developing to allow dev to test changes:
  - `./scripts/builder/universal_build.sh --target x86_64 --build debug --clean`
  - `./scripts/run.sh --width 1920 --height 1080 --fixed` to emulate the embedded target more closely on host machine
- Update VERSION file when making significant changes.
- Update CHANGELOG.md with all notable changes, following the established format.

### Common Tasks
- When implementing new screens, follow the pattern of existing screens (Home.qml, Print.qml, etc.)
- When adding new components, ensure they are responsive and follow the established UI principles.
- For navigation changes, modify MainLayout.qml and ensure proper signal connections.
- When fixing bugs, document the cause and solution in commit messages and CHANGELOG.md.
- After making new files in the qt project always add to the qml.qrc file.

### Documentation
- Document public API functions and custom QML components with clear descriptions.
- Update README.md when adding new features or changing existing functionality.
- Use inline comments to explain complex logic or workarounds.
- Include dependency requirements in README.md for different platforms.

### Security & Safety
- Never include API tokens or sensitive information in the code.
- Sanitize all user inputs before processing.
- Consider error handling for all external interactions.
- Use placeholder values for sensitive information in scripts (like `<figma_token>` in the download_figma_design.sh script).


### Files imports
- Use relative paths for imports in QML files (e.g., `import "qrc:/UI_new/Screens/login/LogoBrand.qml"` should be `import "qrc:/UI_new/Screens/login/LogoBrand.qml"`)