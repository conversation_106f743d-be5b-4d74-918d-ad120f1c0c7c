# Light Project Tasks

## Completed Tasks
- [x] Implemented base application structure with Qt5/QML
- [x] Created main navigation system via MainLayout.qml
- [x] Implemented printer management screens and label printing functionality
- [x] Created reusable UI components
- [x] Removed navigation headers from all screens
- [x] Added virtual keyboard support for ProsprTextField components

## Current Tasks
- [x] Modularize LogIn.qml screen into separate components
  - [x] Create logical component breakdown following QML best practices
  - [x] Place components in UI_new/Screens/login directory
  - [x] Update main LogIn.qml to use the new components
  - [x] Register all new files in qml.qrc
- [x] Update LoginForm to use ProsprTextField for better virtual keyboard support
- [x] Improve error handling and validation in the login form
- [x] Add proper error messages for failed login attempts
- [ ] Implement "remember me" functionality for login
- [ ] Add password recovery option

## Future Tasks
- [x] Add virtual keyboard support for all text input screens
- [x] Enhance login validation with proper error messaging
- [ ] Implement "remember me" functionality for login
- [ ] Add password recovery option
- [ ] Improve touchscreen support across all screens
- [ ] Add biometric authentication options for touchscreen devices
- [ ] Implement proper security for authentication

## Performance Optimization Tasks
- [ ] Implement lazy loading for heavy components
- [ ] Review and optimize image assets for embedded target
- [ ] Implement proper component lifecycle management
- [ ] Add explicit garbage collection hints for large resources
- [ ] Consider implementing object pooling for frequently created/destroyed items
- [ ] Monitor memory usage on the target device with profiling tools

## Embedded Target Optimization Tasks
- [ ] Fix display scaling issues on T507 target
- [ ] Optimize environment variables for embedded deployment
- [ ] Ensure proper QML import paths on target device
- [ ] Implement power management considerations
- [ ] Add watchdog timer integration
- [ ] Optimize boot time and application startup
- [ ] Test and optimize touch input for industrial gloves
- [ ] Implement touch feedback animations for better UX


# Light Application Task List (from Figma Design)

## User Management & Permissions
- [x] Default user on turn-on is guest with fewer permissions
- [x] Admin role with additional capabilities
- [ ] Implement technician role
- [ ] User permissions management interface
- [ ] Password-protected user switching

## Mode Controls
- [ ] Service Mode implementation
- [ ] Function controls
- [ ] Phase controls
- [ ] Move Phase and Service mode as buttons in the Function page

## Print Page Features
- [ ] Start At and End At settings
- [ ] Reset counter functionality
- [ ] Settings button for quick navigation to the current setting selection
- [ ] Default print settings selection
- [ ] Make print page elements larger to fill the screen better

## Navigation & UI
- [x] Remove navigation headers from all screens
- [x] Remove TopBand.qml and references
- [ ] Simplified screen layouts
- [ ] Modern UI with simplified navigation
- [ ] Consider removing the Pair button

## Printer Settings
- [ ] Printer configuration interface
- [ ] Print job queue management
- [ ] Printer status monitoring

## File Management
- [ ] Label creation interface
- [ ] File manager improvements
- [ ] File organization functionality

## Layout & Design
- [ ] Responsive scaling for both embedded and development modes
- [ ] Industrial environment UI optimizations
- [ ] Color scheme standardization

## Error Handling
- [ ] Error information display
- [ ] Error logging system
- [ ] Error resolution guidance

## System Integration
- [ ] Linux support enhancements
- [ ] TimeHandler component for date/time display optimization
- [ ] Implement resource caching for frequently used assets
- [ ] Optimize Qt resource system usage
- [ ] Expand C++ backend for performance-critical operations
- [ ] Implement proper model/view architecture for data-heavy screens
- [ ] Use QML profiler to identify bottlenecks
- [ ] Consider using Qt Quick Compiler for production builds
- [ ] Implement structured logging using QLoggingCategory

## Legend
- [x] Completed
- [ ] To be implemented

# Login Page Implementation Tasks

Based on the Figma design screenshot, here are the tasks needed to implement the Login page:

## UI Components
- [x] Create Login page main container with blue gradient background
- [x] Implement Prospr logo and branding on left side
- [x] Create "WELCOME BACK!" header text
- [x] Implement User Name input field with placeholder "Enter User Name"
- [x] Implement Password input field with masked text entry
- [x] Create CANCEL button (gray/white)
- [x] Create LOG IN button (orange)
- [x] Implement CREATE NEW USER button (dark blue with plus icon)
- [ ] Add status indicator dots at bottom left (green and gray)
- [ ] Add warning/alert icon in status bar
- [ ] Add user icon in status bar
- [ ] Add home icon in status bar
- [ ] Add date/time display ("02/05/2025 08:36 PM")

## Functionality
- [x] Implement user authentication logic (basic)
- [ ] Create validation for username and password fields
- [ ] Add error handling for invalid credentials
- [x] Implement password masking functionality
- [ ] Create navigation flow from CANCEL button
- [x] Create navigation flow from LOG IN button
- [ ] Implement CREATE NEW USER functionality
- [ ] Add session management
- [ ] Connect status indicators to system status
- [ ] Implement date/time synchronization

## Integration
- [ ] Connect to user database/service
- [ ] Integrate with authentication system
- [ ] Link warning icon to system alerts
- [ ] Connect user profile system

## Testing
- [ ] Test form validation
- [ ] Test authentication success/failure paths
- [ ] Verify UI rendering on target device
- [ ] Test keyboard interactions
- [ ] Validate error messages

## Accessibility
- [ ] Ensure proper tab order
- [ ] Add appropriate aria labels
- [ ] Verify color contrast compliance
- [ ] Test with screen readers if applicable


*Note: This task list is a living document. Please check off items as they are completed and add additional tasks as they are identified during the development process.*

# Settings Page Implementation Tasks

Based on the screenshot, here are the tasks needed to implement the Settings page:

## UI Components
- [ ] Create main container with light background
- [ ] Implement 2x3 grid layout for settings categories
- [ ] Create category tiles with consistent styling:
  - [ ] Rounded corners
  - [ ] Light background
  - [ ] Orange border for selected item
  - [ ] Centered content layout
- [ ] Implement category icons:
  - [ ] Date & Time (calendar icon)
  - [ ] Users & Permissions (user icon)
  - [ ] Language (language symbol icon)
  - [ ] System Information (alert/info icon)
  - [ ] Upgrade (graph/chart icon)
  - [ ] Other (hamburger menu icon)
- [ ] Add category labels with proper typography
- [ ] Implement navigation bar at bottom
  - [ ] SERVICE button (dark blue)
  - [ ] PRINT button (dark blue)
  - [ ] SETTINGS button (orange/active)
  - [ ] Status indicator dots at bottom left (green and gray)
  - [ ] Alert/warning icon
  - [ ] User profile icon
  - [ ] Home icon
  - [ ] Date/time display ("02/05/2025 08:36 PM")

## Functionality
- [ ] Implement navigation to specific settings pages:
  - [ ] Date & Time settings
  - [ ] Users & Permissions management
  - [ ] Language selection
  - [ ] System Information display
  - [ ] Upgrade functionality
  - [ ] Other settings
- [ ] Create tap/click handlers for each settings tile
- [ ] Implement visual feedback on tile selection
- [ ] Ensure navigation bar buttons work correctly
  - [ ] SERVICE button navigation
  - [ ] PRINT button navigation
  - [ ] SETTINGS button (already active)
  - [ ] Home icon navigation
  - [ ] User profile icon navigation
  - [ ] Alert icon navigation
- [ ] Connect status indicators to system status
- [ ] Implement date/time display and synchronization

## Integration
- [ ] Connect to user permission system (show/hide options based on user role)
- [ ] Link system status indicators
- [ ] Implement proper navigation flow between screens
- [ ] Connect alert/warning system

## Testing
- [ ] Verify UI rendering on target device
- [ ] Test navigation to each settings page
- [ ] Test responsive layout for different screen sizes
- [ ] Verify permission-based visibility of settings
- [ ] Test navigation bar functionality

## Accessibility
- [ ] Ensure proper tab navigation
- [ ] Add appropriate aria labels
- [ ] Verify color contrast meets requirements
- [ ] Test touch target sizes for industrial environment use
