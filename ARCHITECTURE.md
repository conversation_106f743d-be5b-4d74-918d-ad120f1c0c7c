# Prospr Light — System Architecture

This document provides a comprehensive overview of the Prospr Light application architecture, including system design, component interactions, data flow, and deployment considerations.

## Executive Summary

Prospr Light is a Qt5/QML-based industrial printer management system designed for reliability in factory environments. The architecture emphasizes:

- **Modularity**: Clear separation between UI, business logic, and data layers
- **Cross-platform compatibility**: Desktop development with embedded deployment
- **Industrial reliability**: Robust error handling and persistent state management
- **Scalability**: Plugin-based architecture for future extensibility
- **Performance**: Optimized for touch interfaces and embedded hardware

## System Overview

### Target Environments

**Development Environment:**
- Ubuntu 22.04.x LTS / macOS with Homebrew
- Qt 5.15.x development libraries
- Desktop window management
- Development tools and debugging support

**Production Environment:**
- Allwinner T507 aarch64 embedded Linux
- Framebuffer-based display (1920x1080)
- Touch input interface
- Industrial-grade reliability requirements

## Architectural Layers

### 1. C++ Backend Layer

**Application Bootstrap (`main.cpp`)**
- Qt application initialization and configuration
- Font loading and registration for embedded deployment
- C++ type registration for QML integration
- QML engine startup and resource loading
- Platform-specific environment setup

**Core Backend Services**
- **ConfigManager**: Application configuration and settings management
- **UserManager**: Authentication, user management, and permissions
- **PrinterManager**: Printer control, service functions, and status monitoring
- **PrintfileManager**: Print file creation, management, and storage
- **ErrorManager**: Centralized error logging and notification system

**Data Models**
- **PermissionsModel**: User permission and role management
- **ServiceFunctionModel**: Printer service function states
- **ServiceStatusModel**: Real-time printer status information
- **ErrorModel**: Error log entries and categorization
- **PrintfileModel**: Print file metadata and content

### 2. QML Frontend Layer

**Core Application Logic (`UI/Core/`)**
- **AppController**: Central application state and navigation management
- **ContentLoader**: Dynamic screen loading with error handling
- **PathResolver**: Cross-platform resource path resolution
- **DisplayConfig**: Display scaling and resolution management
- **PlatformHelpers**: Platform-specific utilities and detection

**User Interface Components (`UI/Components/`)**
- **BottomBar**: Centralized navigation and status indicators
- **ProsprButton/ProsprTextField**: Branded UI controls
- **PrintControlsComponent**: Printer operation controls
- **ErrorLogPanel**: Error display and management interface
- **ShutdownDialog**: System shutdown and power management

**Application Screens (`UI/Screens/`)**
- **Home**: Main dashboard and quick actions
- **Print**: Print job creation and management
- **Service**: Printer maintenance and diagnostics
- **Settings**: System configuration and user management
- **LogIn**: User authentication interface

**Design System (`UI/Theme/`)**
- **Colors**: Brand and semantic color palette
- **Typography**: Font families, sizes, and weights
- **Spacing**: Layout spacing and touch-optimized dimensions
- **Radius**: Border radius standards
- **Shadows**: Elevation and depth system

### 3. Build and Deployment Layer

**Build System**
- **qmake-based**: Primary build system with .pro files
- **Cross-compilation**: Docker-based aarch64 toolchain
- **Resource management**: Automatic QRC generation
- **Dependency management**: Submodule and package integration

**Deployment Scripts (`scripts/`)**
- **setup_dev_env.sh**: Development environment configuration
- **build.sh**: Multi-target build automation
- **run.sh**: Application execution with environment setup
- **Font and resource preparation**: Embedded deployment optimization

---

## System Architecture Diagrams

The following diagram illustrates how Prospr Light models a classic MVC (Model-View-Controller) pattern, with a clear separation between the QML frontend client (View & Controller) and the C++ backend (Model & Services). This architecture enables maintainable, scalable, and robust interactions between user interface components and core business logic.

### Service MVC Example

![Service MVC Example](service_mvc_example.png)

### System Architecture Diagram

![System Architecture Diagram](service_arch.png)

```mermaid
---
config:
  layout: fixed
  theme: forest
  look: classic
---
flowchart LR
 subgraph subGraph0["Backend Services"]
        B1["ConfigManager<br>Settings &amp; Config"]
        B2["UserManager<br>Auth &amp; Permissions"]
        B3["PrinterManager<br>Printer Control"]
        B4["PrintfileManager<br>File Management"]
        B5["ErrorManager<br>Error Logging"]
  end
 subgraph subGraph1["Data Models"]
        C1["PermissionsModel"]
        C2["ServiceFunctionModel"]
        C3["ServiceStatusModel"]
        C4["ErrorModel"]
        C5["PrintfileModel"]
  end
 subgraph subGraph2["C++ Backend Layer"]
        A2["Font Loading<br>&amp; Registration"]
        A1["main.cpp<br>Application Bootstrap"]
        A3["QML Type<br>Registration"]
        A4["QML Engine<br>Initialization"]
        subGraph0
        subGraph1
  end
 subgraph subGraph3["Core Logic (UI/Core)"]
        D1["AppController<br>Navigation &amp; State"]
        D2["ContentLoader<br>Dynamic Loading"]
        D3["PathResolver<br>Resource Paths"]
        D4["DisplayConfig<br>Scaling &amp; Display"]
        D5["PlatformHelpers<br>Platform Utils"]
  end
 subgraph subGraph4["UI Components (UI/Components)"]
        E1["BottomBar<br>Navigation"]
        E2["ProsprButton<br>UI Controls"]
        E3["PrintControls<br>Printer UI"]
        E4["ErrorLogPanel<br>Error Display"]
  end
 subgraph subGraph5["Application Screens (UI/Screens)"]
        F1["Home<br>Dashboard"]
        F2["Print<br>Print Jobs"]
        F3["Service<br>Maintenance"]
        F4["Settings<br>Configuration"]
        F5["LogIn<br>Authentication"]
  end
 subgraph subGraph6["Design System (UI/Theme)"]
        G1["Colors<br>Palette"]
        G2["Typography<br>Fonts"]
        G3["Spacing<br>Layout"]
        G4["Radius<br>Borders"]
        G5["Shadows<br>Elevation"]
  end
 subgraph subGraph7["QML Frontend Layer"]
        subGraph3
        subGraph4
        subGraph5
        subGraph6
  end
 subgraph subGraph8["External Dependencies"]
        H1["CuteKeyboard<br>Virtual Keyboard"]
        H2["Font Libraries<br>DejaVu, Liberation, Ubuntu"]
        H3["YAML-CPP<br>Configuration"]
  end
    A1 --> A2 & A3 & A4
    A3 --> B1 & B2 & B3 & B4 & B5
    B1 -.-> C1
    B2 -.-> C1
    B3 -.-> C2 & C3
    B5 -.-> C4
    B4 -.-> C5
    D1 --> D2 & E1
    D2 --> F1 & F2 & F3 & F4 & F5
    E1 --> E2 & E3 & E4
    F1 -.-> G1 & G2 & G3
    E2 -.-> G1 & G4 & G5
    A4 --> D1
    B1 --> D1
    B2 --> F5
    B3 --> E3
    B4 --> F2
    B5 --> E4
    H1 --> F5
    H2 --> A2
    H3 --> B1
    style subGraph2 fill:#E3F2FD
    style subGraph7 fill:#F3E5F5
    style subGraph8 fill:#FFFDE7
    style H1 fill:#BBDEFB,stroke:#1976D2,stroke-width:2,color:#212121
    style H2 fill:#BBDEFB,stroke:#1976D2,stroke-width:2,color:#212121
    style H3 fill:#BBDEFB,stroke:#1976D2,stroke-width:2,color:#212121
```

## Data Flow and Component Interactions

### Application Startup Sequence

1. **Bootstrap Phase (`main.cpp`)**
   - Initialize Qt application with platform-specific settings
   - Load and register font families for embedded deployment
   - Register C++ backend services as QML singletons
   - Configure QML engine with import paths and context properties
   - Launch main QML interface

2. **UI Initialization Phase (`main.qml` → `AppController`)**
   - Initialize application controller and core services
   - Set up navigation system and screen management
   - Configure display scaling and platform adaptations
   - Load initial screen based on configuration

3. **Runtime Phase**
   - Dynamic screen loading via `ContentLoader`
   - Real-time data binding between C++ models and QML views
   - Event-driven communication through Qt signals/slots
   - Persistent state management through `ConfigManager`

### Cross-Layer Communication

**QML → C++ (Method Invocation)**
```qml
// Direct method calls on registered singletons
UserManager.authenticateUser(username, password)
PrinterManager.startFunction("print_job")
ErrorManager.clearErrors()
```

**C++ → QML (Property Binding & Signals)**
```cpp
// Property changes automatically update QML
emit usersChanged();
emit errorsChanged();
emit statusIndicatorsChanged();
```

**Component Communication (QML)**
```qml
// Signal-based communication between components
AppController.navigateTo("Print")
BottomBar.showErrorLog()
ContentLoader.loadingFailed(errorMessage)
```

## Platform-Specific Adaptations

### Desktop Development Environment

**Resource Management:**
- Uses Qt Resource System (`qrc:/`) for embedded assets
- Standard file system access for development files
- Desktop window management and scaling

**Development Features:**
- Hot-reload capability for QML files
- Debugging and profiling tools integration
- Development-specific logging and diagnostics

**Build Configuration:**
```bash
# Desktop build with development features
./scripts/build.sh -t x86_64 -b debug -c
```

### Embedded Production Environment

**Resource Management:**
- File system-based resource loading (`file:///`)
- Optimized font loading and caching
- Minimal resource footprint

**Display Configuration:**
- Framebuffer-based rendering (1920x1080)
- Touch input optimization
- Full-screen application mode

**Performance Optimizations:**
- Reduced debug output
- Optimized rendering pipeline
- Memory usage optimization

**Build Configuration:**
```bash
# Cross-compile for embedded target
./scripts/build.sh -t aarch64 -b release -c
```

### Path Resolution Strategy

The `PathResolver` component handles environment-specific resource loading:

```qml
// Automatically resolves to appropriate path based on platform
PathResolver.resolveScreenPath("Home")
// Desktop: "qrc:/UI/Screens/Home.qml"
// Embedded: "file:///opt/light/UI/Screens/Home.qml"
```

## Security and Reliability Considerations

### User Authentication and Authorization
- Role-based access control with configurable permissions
- Session management with automatic timeout
- Secure credential storage and validation

### Error Handling and Recovery
- Centralized error logging with categorization
- Graceful degradation for component failures
- Automatic recovery mechanisms for critical services

### Data Persistence
- Configuration data stored in YAML format
- User data and permissions in JSON format
- Error logs with timestamp and categorization
- Atomic file operations for data integrity

### Industrial Reliability Features
- Watchdog timer integration for system monitoring
- Automatic restart capabilities for critical failures
- Persistent state recovery after power cycles
- Hardware abstraction for different printer models

## Performance Optimization

### Memory Management
- Lazy loading of UI components
- Efficient QML object lifecycle management
- Optimized image and asset loading
- Memory pool allocation for frequent operations

### Rendering Performance
- Hardware-accelerated graphics where available
- Optimized QML rendering pipeline
- Efficient font rendering and caching
- Touch input optimization for industrial environments

### Network and I/O
- Asynchronous file operations
- Efficient printer communication protocols
- Optimized configuration file parsing
- Background processing for non-critical operations

## Extensibility and Future Considerations

### Plugin Architecture
- Modular backend service design
- QML component-based UI architecture
- Configurable service function framework
- Extensible error handling system

### Internationalization Support
- Unicode font support with comprehensive character sets
- Configurable locale and language settings
- Extensible translation framework
- Cultural adaptation for different markets

### Hardware Integration
- Abstract printer interface for multiple printer types
- Configurable I/O interfaces
- Sensor integration capabilities
- External device communication protocols

## References and Documentation

- **[DEVELOPER_GUIDE.md](DEVELOPER_GUIDE.md)**: Development setup and workflow
- **[README.md](README.md)**: Project overview and quick start
- **[docs/](docs/)**: Technical documentation and implementation guides
- **[context/](context/)**: Development context and best practices
- **[scripts/](scripts/)**: Build and deployment automation
